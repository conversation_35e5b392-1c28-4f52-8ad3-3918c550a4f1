import { Injectable } from '@nestjs/common';

import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { Catalog } from 'src/entities/import/catalog.entity';
import { ImportColumn } from 'src/entities/import/column.entity';
import { ColumnToValidation } from 'src/entities/import/column-to-validation.entity';
import { Validation } from 'src/entities/import/validation.entity';
import { ValidatorDBInterface } from './interfaces/validator.interface';

@Injectable()
export class ValidatorDataAccess {
  constructor(
    private readonly catalogColumnRepository: CatalogColumnRepository,
  ) {}
  public async getValidatorsByCatalog(
    catalog: CatalogEnum,
  ): Promise<ValidatorDBInterface[]> {
    return this.catalogColumnRepository
      .createQueryBuilder('icc')
      .select([
        'ic."Name"     as "ImportName"',
        'icol."Name"   as "ColumnName"',
        'icol."ID"     as "ColumnID"',
        'iv."Name"     as "ValidationName"',
        'iv."CustomURL"     as "CustomURL"',
        'ictv."Params" as "ValidationParams"',
        'ictv."SkipOnEmpty" as "SkipOnEmpty"',
        'ictv."DependentColumn" as "DependentColumn"',
      ])
      .innerJoin(Catalog, 'ic', 'icc."CatalogID" = ic."ID"')
      .innerJoin(ImportColumn, 'icol', 'icc."ColumnID" = icol."ID"')
      .innerJoin(ColumnToValidation, 'ictv', 'icc."ColumnID" = ictv."ColumnID"')
      .innerJoin(Validation, 'iv', 'ictv."ValidationID" = iv."ID"')
      .where('icc."CatalogID" = :catalogId', { catalogId: catalog })
      .andWhere('icc."IsDeleted" = 0')
      .andWhere('ic."IsDeleted" = 0')
      .andWhere('icol."IsDeleted" = 0')
      .andWhere('ictv."IsDeleted" = 0')
      .andWhere('iv."IsDeleted" = 0')
      .orderBy('ic."Priority", icc."Priority"')
      .execute();
  }
}
