import { ApiClient } from 'src/common/api.client';
import { HttpMethod } from 'src/common/enums/http-method.enum';
import { kebabize } from 'src/common/helpers/kebabize';
import { FailValidationResult } from 'src/common/validation/interfaces';
import { ValidationResult } from 'src/common/validation/validation-result.type';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ColumnValidator } from './interfaces/column-validator.interface';
import { Validator } from './interfaces/validator.interface';
import { ValidatorDataAccess } from './data-access/validator.data-access';
import { SimpleValidatorsEnum } from 'src/common/validation/simple-validator.enum';
import { SimpleValidator } from 'src/common/validation/simple-validators';
import { ValidatorDBInterface } from './data-access/interfaces/validator.interface';

export class CatalogValidator {
  private baseValidationPath: string;
  private columnValidators: ColumnValidator[] = [];
  private validatorResults: ValidationResult[] = [];
  protected validatorDataAccess: ValidatorDataAccess;
  protected apiClient: ApiClient;

  constructor(
    private catalog: CatalogEnum,
    validatorDataAccess: ValidatorDataAccess,
    apiClient: ApiClient,
  ) {
    this.validatorDataAccess = validatorDataAccess;
    this.apiClient = apiClient;
    this.baseValidationPath = kebabize(CatalogEnum[this.catalog]);
  }

  public async validate<K extends { [key: string]: any }>(
    records: K[],
  ): Promise<ValidationResult> {
    await this.getValidators();

    const externalValidations: Promise<ValidationResult>[] = [];

    for (const columnValidator of this.columnValidators) {
      for (const validator of columnValidator.validators) {
        if (validator.name in SimpleValidatorsEnum) {
          if (SimpleValidator[validator.name]) {
            const data = this.generateDataForSimpleValidation(
              columnValidator.columnName,
              records,
            );
            const result = SimpleValidator[validator.name](
              columnValidator.columnName,
              data,
              validator.skipOnEmpty,
            );
            this.validatorResults.push(result);
          }
        } else {
          const data = this.generateDataForExternalValidation(
            columnValidator.columnName,
            validator.dependentColumn,
            records,
          );

          externalValidations.push(
            this.invokeExternalValidation(
              data,
              columnValidator.columnName,
              validator,
            ),
          );
        }
      }
    }

    await this.performAllExternalValidations(externalValidations);

    const notValidResults = this.validatorResults.filter(
      (r) => !r.success,
    ) as FailValidationResult[];

    return {
      success: notValidResults.length === 0,
      ...(notValidResults.length > 0 && {
        errors: notValidResults.flatMap((r) => r.errors),
      }),
    } as ValidationResult;
  }

  private async getValidators() {
    const columnValidators =
      await this.validatorDataAccess.getValidatorsByCatalog(this.catalog);

    const columnNames = new Set(columnValidators.map((c) => c.ColumnName));

    for (const columnName of columnNames) {
      const validators: Validator[] = columnValidators
        .filter((c) => c.ColumnName === columnName)
        .map(
          (c) =>
            ({
              name: c.ValidationName,
              customUrl: c.CustomURL,
              params: c.ValidationParams,
              skipOnEmpty: c.SkipOnEmpty,
              dependentColumn: this.getDependentColumnName(
                c.DependentColumn,
                columnValidators,
              ),
            } as Validator),
        );

      const columnValidator: ColumnValidator = {
        columnName,
        validators,
      };

      this.columnValidators.push(columnValidator);
    }
  }

  private getDependentColumnName(
    columnIds: number[] | null,
    columnValidators: ValidatorDBInterface[],
  ): string[] {
    return columnIds
      ? columnValidators
          .filter((c) => columnIds.includes(c.ColumnID))
          .map((c) => c.ColumnName)
      : [];
  }

  private generateDataForExternalValidation<K extends { [key: string]: any }>(
    columnName: string,
    dependentColumn: string[],
    records: K[],
  ): any[] {
    return records.map((record) => ({
      [columnName]: record[columnName],
      ...Object.assign(
        {},
        ...dependentColumn.map((c) => ({
          [c]: record[c],
        })),
      ),
    }));
  }

  private generateDataForSimpleValidation<K extends { [key: string]: any }>(
    columnName: string,
    records: K[],
  ): any[] {
    return records.map((record) => record[columnName]);
  }

  private async invokeExternalValidation(
    data: any[],
    columnName: string,
    validator: Validator,
  ): Promise<ValidationResult> {
    const path = validator.customUrl ?? this.baseValidationPath;

    return this.apiClient.sendRequest<ValidationResult>(
      HttpMethod.POST,
      `${path}/${validator.name}`,
      {
        body: {
          data,
          columnName,
          skipOnEmpty: validator.skipOnEmpty,
          params: validator.params,
        },
        timeout: 120_000,
      },
    );
  }

  private async performAllExternalValidations(
    externalValidations: Promise<ValidationResult>[],
  ): Promise<void> {
    const externalValidationsResult =
      externalValidations.length > 0
        ? await Promise.all(externalValidations)
        : [];

    this.validatorResults.push(...externalValidationsResult);
  }
}
