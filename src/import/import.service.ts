import { stream as excelStream } from 'exceljs';
import { ApiClient } from 'src/common/api.client';

import { ValidationResult } from 'src/common/validation/validation-result.type';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ImportColumn } from 'src/entities/import/column.entity';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import stream from 'stream';
import { CatalogValidator } from './catalog-validator';
import { ValidatorDataAccess } from './data-access/validator.data-access';
import { UploadStatus } from './upload-status.enum';

type PreValidateType<T, V> = V extends undefined ? T : V;

export abstract class Import<T, V = undefined> {
  protected abstract validatorDataAccess: ValidatorDataAccess;
  protected abstract apiClient: ApiClient;
  protected abstract uploadHistoryRepository: UploadHistoryRepository;
  protected abstract catalogColumnRepository: CatalogColumnRepository;
  abstract catalog: CatalogEnum;

  abstract preValidate?(data: T[]): Promise<PreValidateType<T, V>[]>;
  abstract publish(
    data: PreValidateType<T, V>[],
    userId: number,
    uploadHistoryId: string,
    additionalParams: any,
  ): Promise<void>;

  async customPublish(
    data: PreValidateType<T, V>[],
    userId: number,
    uploadHistoryId: string,
    pageNumber: number,
    additionalParams: any,
  ): Promise<void> {
    console.log('empty customPublish()');
  }

  public async import(
    data: T[],
    userId: number,
    additionalParams: any = null,
  ): Promise<ValidationResult> {
    const changedRecords = this.preValidate
      ? await this.preValidate(data)
      : (data as PreValidateType<T, V>[]);

    const validator = new CatalogValidator(
      this.catalog,
      this.validatorDataAccess,
      this.apiClient,
    );

    const uploadHistory = await this.uploadHistoryRepository.save({
      importListId: this.catalog,
      insertedUserId: userId,
      statusId: UploadStatus.New,
    });

    const validationResult = await validator.validate(changedRecords);

    if (validationResult.success) {
      uploadHistory.statusId = UploadStatus.InProgress;
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: uploadHistory.statusId },
      );
      console.log(
        'Count of import data on publisher side ' + changedRecords.length,
      );
      this.publish(
        changedRecords,
        userId,
        uploadHistory.id,
        additionalParams,
      ).catch((error) => console.error(error));
    } else {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.ValidationFailed },
      );
    }

    return { ...validationResult, id: uploadHistory.id };
  }

  async importLargeFile(
    filePath: string,
    userId: number,
    type: new () => T,
    additionalParams: any,
  ): Promise<ValidationResult> {
    return {
      success: true,
    };
  }

  public async getGeneratedCsv(
    type: string,
    multiPackage?: number,
  ): Promise<stream.PassThrough> {
    let csvBody = '';
    if (type === 'dictionary') {
      const catalogColumns = await this.getDictionaryData();
      return await this.makeExcelFile(catalogColumns);
    } else {
      const catalogColumns = await this.getCatalogColumns();
      csvBody = catalogColumns.map((column) => column.name).join(';');
      if (multiPackage && Number(multiPackage) === 1) {
        csvBody += ';PackageID';
      }
    }
    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  protected async makeExcelFile(data: {
    [p: string]: string[];
  }): Promise<stream.PassThrough> {
    const readStream = new stream.PassThrough();
    const book = new excelStream.xlsx.WorkbookWriter({
      stream: readStream,
    });
    for (const name in data) {
      const rows = data[name];
      rows.unshift(name);
      const sheet = book.addWorksheet(name);
      for (const row of rows) {
        await sheet.addRow([row]).commit();
      }
      await sheet.commit();
    }
    await book.commit();
    readStream.end();
    return readStream;
  }

  protected async getCatalogColumns(): Promise<
    { priority: number; name: string }[]
  > {
    const catalogColumns: { priority: number; name: string }[] =
      await this.catalogColumnRepository
        .createQueryBuilder('cc')
        .select(['cc."Priority" as "priority"', 'c."Name" as name'])
        .innerJoin(ImportColumn, 'c', 'c."ID" = cc."ColumnID"')
        .where('cc."CatalogID" =:catalogId', { catalogId: this.catalog })
        .andWhere('cc."IsDeleted" = 0 AND c."IsDeleted" = 0')
        .execute();

    return catalogColumns.sort((a, b) => (a.priority > b.priority ? 1 : -1));
  }

  public abstract getDictionaryData(): Promise<{ [p: string]: string[] }>;
}
