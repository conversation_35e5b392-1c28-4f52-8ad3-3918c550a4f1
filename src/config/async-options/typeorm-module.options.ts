import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModuleAsyncOptions } from '@nestjs/typeorm';

import database from 'src/config/database';

export const typeormModuleOptions: TypeOrmModuleAsyncOptions = {
  imports: [ConfigModule.forFeature(database)],
  useFactory: (configService: ConfigService) => ({
    type: 'postgres',
    replication: {
      master: {
        host: configService.get('database.master.host'),
        port: configService.get('database.master.port') as number,
        username: configService.get('database.master.username'),
        password: configService.get('database.master.password'),
        database: configService.get('database.master.database'),
      },
      slaves: [
        {
          host: configService.get('database.slave.host'),
          port: configService.get('database.slave.port') as number,
          username: configService.get('database.slave.username'),
          password: configService.get('database.slave.password'),
          database: configService.get('database.slave.database'),
        },
      ],
    },

    entities: ['dist/**/entities/**/*.entity{.ts,.js}'],
    synchronize: false,
    path: '/',
    fieldResolverEnhancers: ['guards'],
    logging:
      configService.get('environment') !== 'production'
        ? 'all'
        : ['error', 'warn'],
  }),
  inject: [ConfigService],
};
