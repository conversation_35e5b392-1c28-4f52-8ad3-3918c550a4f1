import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerModuleAsyncParams } from 'nestjs-pino';
import { Options } from 'pino-http';

export const loggerModuleOptions: LoggerModuleAsyncParams = {
  imports: [ConfigModule],
  useFactory: (configService: ConfigService) => ({
    pinoHttp: [
      {
        useLevel:
          configService.get('environment') === 'production' ? 'error' : 'debug',
        prettyPrint: true, // configService.get('environment') !== 'production',
        autoLogging: false,
      },
    ] as Options,
  }),
  inject: [ConfigService],
};
