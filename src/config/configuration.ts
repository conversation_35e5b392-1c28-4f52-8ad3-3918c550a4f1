import * as process from 'process';

const CHUNK_DEFAULT = 200;
const IMPORT_CHUNK_DEFAULT = 50_000;
const REQUEST_TIMEOUT = 60;

export default () => ({
  environment: process.env.NODE_ENV || 'development',
  release: process.env.INITIAL_JENKINS_GIT_VERSION || undefined,
  port: Number.parseInt(process.env.PORT || '80', 10),
  store: process.env.STORE ?? '/opt/store',
  deploySuffix: process.env.DEPLOY_SUFFIX,
  sockets: {
    path: '/imports-progress-sockets',
  },
  validationService: {
    url:
      process.env.VALIDATION_SERVICE_URL ||
      'http://validation-service' + process.env.DEPLOY_SUFFIX,
  },
  caseService: {
    url:
      process.env.CASE_SERVICE_URL ||
      'http://case-service' + process.env.DEPLOY_SUFFIX,
  },
  dictionaryService: {
    url:
      process.env.DICTIONARY_SERVICE_URL ||
      'http://dictionary-service' + process.env.DEPLOY_SUFFIX,
  },
  importService: {
    url:
      process.env.IMPORT_SERVICE_URL ||
      'http://import-service' + process.env.DEPLOY_SUFFIX,
  },
  searchService: {
    url: 'http://search-service' + process.env.DEPLOY_SUFFIX,
  },
  actionNodeService: {
    url:
      process.env.ACTION_NODE_SERVICE_URL ||
      'http://action-node-service' + process.env.DEPLOY_SUFFIX,
  },
  chunk: {
    default: Number(process.env.DEFAULT_CHUNK) || CHUNK_DEFAULT,
    importDefault:
      Number(process.env.IMPORT_DEFAULT_CHUNK) || IMPORT_CHUNK_DEFAULT,
  },
  requestTimeout:
    (Number(process.env.REQUEST_TIMEOUT) || REQUEST_TIMEOUT) * 1000,
});
