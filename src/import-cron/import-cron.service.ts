import { Inject, Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { BaseImportService } from '../common/imports/base-import.service';
import CatalogToServiceMapping from '../common/imports/catalog-to-service-mapping';
import { RABBITMQ } from '../common/rabbitmq/constants';
import { REDIS } from '../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../config/redis-import-keys';
import { CatalogNameEnum as CatalogName } from '../entities/enum/catalog-name.enum';
import { CatalogEnum as ImportCatalog } from '../entities/enum/catalog.enum';
import { UploadStatus } from '../import/upload-status.enum';
import { UploadHistoryRepository } from '../repositories/import/upload-history.repository';

type CatalogKey = keyof typeof CatalogName &
  keyof typeof CatalogToServiceMapping;
@Injectable()
export class ImportCronService {
  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    private moduleReference: ModuleRef,
  ) {}
  async run(): Promise<void> {
    const orders = await this.uploadHistoryRepository.find({
      where: {
        statusId: UploadStatus.InProgress,
        isDeleted: 0,
      },
    });

    for (const order of orders) {
      const catalog: CatalogKey = this.getCatalogKey(order.importListId);

      const pages = await this.redis.get(
        REDIS_KEYS.pages(CatalogName[catalog], Number(order.id)),
      );

      if (
        pages &&
        (await this.checkAllJobsCompleted(
          Number(order.id),
          catalog,
          Number(pages),
        ))
      ) {
        await this.saveResults(Number(order.id), catalog, Number(pages));
        break;
      }
    }
  }

  private async checkAllJobsCompleted(
    id: number,
    catalog: CatalogKey,
    pages: number,
  ): Promise<boolean> {
    for (let page = 1; page < Number(pages) + 1; page++) {
      const status = await this.redis.hget(
        REDIS_KEYS.results(CatalogName[catalog], id),
        `status:${page}`,
      );

      if (!status) {
        return false;
      }
    }
    return true;
  }
  private async saveResults(
    id: number,
    catalog: CatalogKey,
    pages: number,
  ): Promise<void> {
    await this.uploadHistoryRepository.update(
      { id: String(id) },
      { statusId: UploadStatus.Saving },
    );
    await this.handleSaveResult(id, catalog, pages);
  }

  private getCatalogKey(id: number): CatalogKey {
    return ImportCatalog[id] as CatalogKey;
  }
  private async handleSaveResult(
    id: number,
    catalog: CatalogKey,
    pages: number,
  ): Promise<void> {
    try {
      const className = CatalogToServiceMapping[catalog].name;
      if (!className) {
        return;
      }
      const service: BaseImportService<any, any> =
        this.moduleReference.get(className);
      await service.handleSaveResult(id, pages);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
