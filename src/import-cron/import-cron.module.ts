import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerModule } from 'nestjs-pino';
import { ApiClient } from '../common/api.client';
import { RabbitmqModule } from '../common/rabbitmq/rabbitmq.module';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { TransferToCollectionAgency } from '../entities/data/transfer-to-collection-agency.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportActivityParameterService } from '../import-activity-parameter/import-activity-parameter.service';
import { ImportBasisComplaintService } from '../import-basis-complaint/import-basis-complaint.service';
import { ImportBasisCpiioService } from '../import-basis-cpiio/import-basis-cpiio.service';
import { ImportBasisEmployerService } from '../import-basis-employer/import-basis-employer.service';
import { ImportBasisFinaService } from '../import-basis-fina/import-basis-fina.service';
import { ImportCaseDiscountService } from '../import-case-discount/import-case-discount.service';
import { ImportCaseParameterService } from '../import-case-parameter/import-case-parameter.service';
import { ImportCaseStateService } from '../import-case-state/import-case-state.service';
import { ImportContractDataService } from '../import-contract-data/import-contract-data.service';
import { ImportCourtParameterService } from '../import-court-parameter/import-court-parameter.service';
import { ImportDeleteInteractionService } from '../import-delete-interaction/import-delete-interaction.service';
import { ImportDeleteLegalCaseService } from '../import-delete-legal-case/import-delete-legal-case.service';
import { ImportDiscountService } from '../import-discount/import-discount.service';
import { ImportExtraInfoService } from '../import-extra-info/import-extra-info.service';
import { ImportLegalCaseService } from '../import-legal-case/import-legal-case.service';
import { ImportSmsActivityService } from '../import-sms-activity/import-sms-activity.service';
import { ImportTagService } from '../import-tag/import-tag.service';
import { ImportTransferToCollectionAgencyService } from '../import-transfer-to-collection-agency/import-transfer-to-collection-agency.service';
import { ImportLegalAppellateCourtService } from '../import-legal-appellate-court/import-legal-appellate-court.service';
import { ImportLegalCourtService } from '../import-legal-court/import-legal-court.service';
import { ImportLegalDocumentService } from '../import-legal-document/import-legal-document.service';
import { ValidatorDataAccess } from '../import/data-access/validator.data-access';
import { CaseChangeLogRepository } from '../repositories/activity/case-change-log.repository';
import { CommentRepository } from '../repositories/activity/comment.repository';
import { ContactWithResultRepository } from '../repositories/activity/contact-with-result.repository';
import { HistoryParameterRepository } from '../repositories/activity/history-parameter.repository';
import { HistoryRepository } from '../repositories/activity/history.repository';
import { SmsServiceRepository } from '../repositories/activity/sms-service.repository';
import { AdditionalNoteRepository } from '../repositories/data/additional-note.repository';
import { CaseDiscountRepository } from '../repositories/data/case-discount.repository';
import { CaseParameterRepository } from '../repositories/data/case-parameter.repository';
import { CaseStageChangeLogRepository } from '../repositories/data/case-stage-change-log.repository';
import { CaseStateHistoryRepository } from '../repositories/data/case-state-history.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { ContactPersonRelationRepository } from '../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { CourtProcessParameterRepository } from '../repositories/data/court-process-parameter.repository';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { DocumentParameterRepository } from '../repositories/data/document-parameter.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { InvoiceDiscountRepository } from '../repositories/data/invoice-discount.repository';
import { TagForCaseDebtorNetworkRepository } from '../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../repositories/data/tag-for-invoice.repository';
import { AvailabilityOptionRepository } from '../repositories/dictionary/availability-option.repository';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { DataSourceRepository } from '../repositories/dictionary/data-source.repository';
import { HistoryTypeToParameterRepository } from '../repositories/dictionary/history-type-to-parameter.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { RegionRepository } from '../repositories/dictionary/region.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { CatalogColumnRepository } from '../repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from '../repositories/import/upload-history.repository';
import { AppellateCourtRepository } from '../repositories/legal-ua/appellate-court.repository';
import { CourtTypeRepository } from '../repositories/legal-ua/court-type.repository';
import { CourtRepository } from '../repositories/legal-ua/court.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { Custom004EEnforcementComplaintRepository } from '../repositories/legal/custom004-enforcement-complaint.repository';
import { Custom004EEnforcementRequestToCpiioRepository } from '../repositories/legal/custom004-enforcement-request-to-cpiio.repository';
import { Custom004EEnforcementRequestToEmployerRepository } from '../repositories/legal/custom004-enforcement-request-to-employer.repository';
import { Custom004EEnforcementRequestToFinaRepository } from '../repositories/legal/custom004-enforcement-request-to-fina.repository';
import { LegalInvoiceRepository } from '../repositories/legal/legal-invoice.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { UpdateLegalDocumentService } from '../update-legal-document/update-legal-document.service';
import { ImportCronController } from './import-cron.controller';
import { ScheduleModule } from '@nestjs/schedule';
import { ImportCronService } from './import-cron.service';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';
import { ImportPhoneService } from 'src/import-phone/import-phone.service';

@Module({
  imports: [
    RabbitmqModule,
    ConfigModule,
    HttpModule,
    LoggerModule,
    ImportRedisModule,
    TypeOrmModule.forFeature([
      ServiceParameterRepository,
      UploadHistoryRepository,
      CatalogColumnRepository,
      AdditionalNoteRepository,
      Custom005CaseAdditionalInfoRepository,
      CaseRepository,
      TransactionRepository,
      DocumentRepository,
      DocumentParameterRepository,
      InvoiceRepository,
      CourtProcessRepository,
      CourtProcessParameterRepository,
      ParameterRepository,
      CourtTypeRepository,
      RegionRepository,
      CourtRepository,
      AppellateCourtRepository,
      Custom004EEnforcementRequestDeliveryStatusRepository,
      Custom004EEnforcementRequestToFinaRepository,
      Custom004EEnforcementRequestToCpiioRepository,
      Custom004EEnforcementRequestToEmployerRepository,
      Custom004EEnforcementComplaintRepository,
      TransferToCollectionAgency,
      AvailabilityOptionRepository,
      HistoryRepository,
      HistoryParameterRepository,
      HistoryTypeToParameterRepository,
      InvoiceDiscountRepository,
      ContactWithResultRepository,
      CommentRepository,
      TagForInvoiceRepository,
      TagForCaseRepository,
      TagForCasePhoneRepository,
      TagForCaseDebtorNetworkRepository,
      CaseChangeLogRepository,
      CaseParameterRepository,
      DebtorRepository,
      ContactPersonRepository,
      ContactPersonRelationRepository,
      CaseStateHistoryRepository,
      LegalInvoiceRepository,
      DataTypeRepository,
      DataSourceRepository,
      CaseStageChangeLogRepository,
      SmsServiceRepository,
      EventRuleLogRepository,
      CaseDiscountRepository,
    ]),
    ScheduleModule.forRoot(),
  ],
  controllers: [ImportCronController],
  providers: [
    ImportCronService,
    EventsGateway,
    ImportContractDataService,
    ImportExtraInfoService,
    ImportLegalDocumentService,
    ImportCourtParameterService,
    UpdateLegalDocumentService,
    ImportLegalCourtService,
    ImportLegalAppellateCourtService,
    ImportBasisFinaService,
    ImportBasisCpiioService,
    ImportBasisEmployerService,
    ImportBasisComplaintService,
    ImportTransferToCollectionAgencyService,
    ImportActivityParameterService,
    ImportDiscountService,
    ImportTagService,
    ImportPhoneService,
    ImportCaseParameterService,
    ImportLegalCaseService,
    ImportDeleteLegalCaseService,
    ImportDeleteInteractionService,
    ImportCaseStateService,
    ImportSmsActivityService,
    ImportCaseDiscountService,
    ValidatorDataAccess,
    ApiClient,
  ],
})
export class ImportCronModule {}
