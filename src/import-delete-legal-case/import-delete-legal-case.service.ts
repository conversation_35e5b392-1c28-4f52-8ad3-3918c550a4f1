import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import stream from 'stream';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { Case } from '../entities/data/case.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { CourtProcess } from '../entities/legal/court-process.entity';
import { LegalInvoice } from '../entities/legal/legal-invoice.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportDeleteLegalCasePublisher } from '../events/publishers/import-delete-legal-case-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseRepository } from '../repositories/data/case.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { LegalDeleteCaseDTO } from './dto/legal-delete-case.dto';

const DELETED_INVOICES_NUMBER = 'Number of deleted invoices';
const DELETED_CASES_NUMBER = 'Number of deleted legal cases';
@Injectable()
export class ImportDeleteLegalCaseService extends BaseImportService<LegalDeleteCaseDTO> {
  catalog: CatalogEnum = CatalogEnum.DeleteLegalCase;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.DeleteLegalCase;
  protected publisher: ImportPublisher<any> =
    new ImportDeleteLegalCasePublisher(this.connection);

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly httpService: HttpService,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly caseRepository: CaseRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  protected getPageSize(): number {
    return 1000;
  }

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [DELETED_INVOICES_NUMBER]: 0,
          [DELETED_CASES_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [DELETED_INVOICES_NUMBER]:
        this.resultDetalization[id][0][DELETED_INVOICES_NUMBER],
      [DELETED_CASES_NUMBER]:
        this.resultDetalization[id][0][DELETED_CASES_NUMBER],
    };

    const invoiceForDelete: number[] = result.deleteInvoices;
    const checkCases: number[] = result.checkCases;
    const userID: number = result.metaData.userId;

    if (invoiceForDelete.length > 0) {
      const chunks = this.sliceIntoChunks(invoiceForDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          LegalInvoice,
          { id: In(chunk) },
          { isDeleted: 1, updatedUserId: userID },
        );
      }

      detalization[DELETED_INVOICES_NUMBER] += invoiceForDelete.length;
    }

    if (checkCases.length > 0) {
      const chunks = this.sliceIntoChunks(checkCases, 500);
      for (const chunk of chunks) {
        const casesToClose = await this.getCasesWithoutInvoices(
          queryRunner,
          chunk,
        );
        await this.closeCases(queryRunner, casesToClose, userID);
        await this.updateParentCases(queryRunner, casesToClose, userID);
        detalization[DELETED_CASES_NUMBER] += casesToClose.length;
      }
    }
  }

  public async getGeneratedCsv(): Promise<stream.PassThrough> {
    const csvBody = 'LegalCaseID;InvoiceID';
    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  private async getCasesWithoutInvoices(
    queryRunner: QueryRunner,
    caseIDs: number[],
  ): Promise<number[]> {
    if (caseIDs.length === 0) {
      return [];
    }

    const casesWithInvoices = await queryRunner.query(
      `
    select "CaseID"
from "LegalUA"."Invoice"
where "CaseID" = any ($1)
  and "IsDeleted" = 0
group by "CaseID";
    `,
      [caseIDs],
    );

    const casesWithInvoicesIDs = new Set(
      casesWithInvoices.map((item: any) => item.CaseID),
    );

    const casesToClose: number[] = [];
    for (const caseID of caseIDs) {
      if (!casesWithInvoicesIDs.has(caseID)) {
        casesToClose.push(caseID);
      }
    }

    return casesToClose;
  }

  private async closeCases(
    queryRunner: QueryRunner,
    caseIDs: number[],
    userId: number,
  ) {
    await queryRunner.manager.update(
      Case,
      { id: In(caseIDs) },
      { isDeleted: 1 },
    );

    await queryRunner.manager.update(
      CourtProcess,
      { caseId: In(caseIDs), isDeleted: 0 },
      { isDeleted: 1, updatedUserId: userId },
    );
  }

  private async updateParentCases(
    queryRunner: QueryRunner,
    caseIDs: number[],
    userId: number,
  ): Promise<number[]> {
    if (caseIDs.length === 0) {
      return [];
    }

    const casesToChangeStage = await queryRunner.query(
      `
    SELECT di."CaseID"
FROM "LegalUA"."Invoice" AS li
         JOIN "Data"."Invoice" AS di ON li."InvoiceID" = di."ID"
WHERE di."IsDeleted" = 0 and li."CaseID" = any ($1);
    `,
      [caseIDs],
    );

    const casesToChangeStageIDs: Set<number> = new Set(
      casesToChangeStage.map((item: any) => Number(item.CaseID)),
    );

    const debtPurchaseStage = await this.serviceParameterRepository.findOne({
      where: {
        name: 'DebtPurchaseStageID',
        isDeleted: 0,
      },
    });

    if (debtPurchaseStage) {
      const actionServiceURL = this.config.get<string>('actionNodeService.url');
      try {
        await this.httpService
          .post(actionServiceURL + '/admin/action/change-stage/from-request', {
            CaseIDs: [...casesToChangeStageIDs],
            stageID: debtPurchaseStage.value,
            userID: userId,
          })
          .toPromise();
      } catch (error) {
        console.log(error);
      }
    }

    return [...casesToChangeStageIDs];
  }
}
