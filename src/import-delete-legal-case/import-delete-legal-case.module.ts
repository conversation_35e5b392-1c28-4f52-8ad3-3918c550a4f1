import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseRepository } from '../repositories/data/case.repository';
import { UploadHistoryRepository } from '../repositories/import/upload-history.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportDeleteLegalCaseController } from './import-delete-legal-case.controller';
import { ImportDeleteLegalCaseService } from './import-delete-legal-case.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      CaseRepository,
      LegalInvoiceRepository,
      CourtProcessRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportDeleteLegalCaseController],
  providers: [
    ImportDeleteLegalCaseService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportDeleteLegalCaseModule {}
