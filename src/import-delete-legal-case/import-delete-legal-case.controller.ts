import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportDeleteLegalCaseService } from './import-delete-legal-case.service';
import { LegalDeleteCaseDTO } from './dto/legal-delete-case.dto';
import { ImportDeleteLegalCaseDto } from './dto/import-delete-legal-case.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-delete-legal-case')
export class ImportDeleteLegalCaseController {
  constructor(
    private importDeleteLegalCaseService: ImportDeleteLegalCaseService,
  ) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; multiPackage?: number },
  ): Promise<any> {
    const readStream =
      await this.importDeleteLegalCaseService.getGeneratedCsv();

    response.set(
      disposition,
      'attachment; filename=DeleteLegalCase - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file')
  async import(@Body() importDeleteLegalCaseDto: ImportDeleteLegalCaseDto) {
    const { importFile, userID } = importDeleteLegalCaseDto;

    return await this.importDeleteLegalCaseService.importLargeFile(
      importFile,
      userID,
      LegalDeleteCaseDTO,
    );
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importDeleteLegalCaseService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
