import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';

import { LegalDeleteCaseDTO } from './legal-delete-case.dto';

function transformNumberString(value: any): any {
  if (IsNumberString(value)) {
    return Number(value);
  }
  return value;
}

export class ImportDeleteLegalCaseDto {
  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: LegalDeleteCaseDTO[];

  importFile: string;
}
