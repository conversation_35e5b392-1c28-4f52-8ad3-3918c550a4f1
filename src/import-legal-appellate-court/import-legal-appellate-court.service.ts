import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { TemporaryLegalUaCourt } from '../events/listeners/legal-appellate-court-listener/temporary-tables/temporary-legal-ua-court';
import { ImportLegalAppellateCourtPublisher } from '../events/publishers/import-legal-appellate-court-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { RegionRepository } from '../repositories/dictionary/region.repository';
import { AppellateCourtRepository } from '../repositories/legal-ua/appellate-court.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { LegalAppellateCourtDTO } from './dto/legal-appellate-court.dto';

const INSERTED_COURT_NUMBER = 'Number of inserted appellate courts';
const UPDATED_COURT_NUMBER = 'Number of updated appellate courts';

@Injectable()
export class ImportLegalAppellateCourtService extends BaseImportService<LegalAppellateCourtDTO> {
  catalog: CatalogEnum = CatalogEnum.LegalAppellateCourt;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.LegalAppellateCourt;
  protected publisher: ImportPublisher<any> =
    new ImportLegalAppellateCourtPublisher(this.connection);

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly courtRepository: AppellateCourtRepository,
    protected readonly regionRepository: RegionRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertCourts = result?.insertCourt;
    const updateCourts = result?.updateCourt;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_COURT_NUMBER]: 0,
          [UPDATED_COURT_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_COURT_NUMBER]:
        this.resultDetalization[id][0][INSERTED_COURT_NUMBER],
      [UPDATED_COURT_NUMBER]:
        this.resultDetalization[id][0][UPDATED_COURT_NUMBER],
    };

    if (insertCourts.length > 0) {
      await this.courtRepository.save(insertCourts);
      detalization[INSERTED_COURT_NUMBER] += insertCourts.length;
    }

    if (updateCourts.length > 0) {
      const temporaryCourt = new TemporaryLegalUaCourt(
        queryRunner.manager,
        this.config,
        Object.keys(updateCourts[0]),
      );
      await temporaryCourt.manualInitialize();
      await temporaryCourt.insert(updateCourts);
      detalization[UPDATED_COURT_NUMBER] += updateCourts.length;
    }

    this.resultDetalization[id][0][INSERTED_COURT_NUMBER] =
      detalization[INSERTED_COURT_NUMBER];
    this.resultDetalization[id][0][UPDATED_COURT_NUMBER] =
      detalization[UPDATED_COURT_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    data.Region = (await this.regionRepository.getList()).map(
      (item) => item.name,
    );

    return data;
  }
}
