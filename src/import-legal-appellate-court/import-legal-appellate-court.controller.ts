import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { LegalAppellateCourtDTO } from './dto/legal-appellate-court.dto';
import { ImportLegalAppellateCourtService } from './import-legal-appellate-court.service';
import { ImportLegalAppellateCourtDTO } from './dto/import-legal-appellate-court.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-legal-appellate-court')
export class ImportLegalAppellateCourtController {
  constructor(
    private importLegalAppellateCourtService: ImportLegalAppellateCourtService,
  ) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importLegalCourtDTO: ImportLegalAppellateCourtDTO) {
    const { importFile, userID } = importLegalCourtDTO;

    return this.importLegalAppellateCourtService.importLargeFile(
      importFile,
      userID,
      LegalAppellateCourtDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream =
      await this.importLegalAppellateCourtService.getGeneratedCsv(query.id);

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportLegalAppellateCourt - ' +
          query.id +
          '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportLegalAppellateCourt - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importLegalAppellateCourtService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
