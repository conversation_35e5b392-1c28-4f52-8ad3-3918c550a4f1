import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { LegalAppellateCourtDTO } from './legal-appellate-court.dto';

export class ImportLegalAppellateCourtDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: LegalAppellateCourtDTO[];

  importFile: string;
}
