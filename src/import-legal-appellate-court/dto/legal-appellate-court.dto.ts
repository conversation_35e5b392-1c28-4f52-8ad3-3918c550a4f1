import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class LegalAppellateCourtDTO {
  @Expose()
  @IsNumber()
  public ID?: number;

  @Expose()
  @IsString()
  public Name: string;

  @Expose()
  @IsNumber()
  public IsDeleted: number;

  @Expose()
  @IsString()
  public Type: string;

  @Expose()
  @IsString()
  public CityCode: string;

  @Expose()
  @IsString()
  public Region: string;

  @Expose()
  @IsString()
  public City: string;

  @Expose()
  @IsString()
  public District: string;

  @Expose()
  @IsString()
  public Street: string;

  @Expose()
  @IsString()
  public House: string;

  @Expose()
  @IsString()
  public Building: string;

  @Expose()
  @IsString()
  public Apartment: string;

  @Expose()
  public ChangeAppellateCourtID: number | string | null;

  @Expose()
  @IsString()
  public Receiver: string;

  @Expose()
  @IsString()
  public InvoiceNumber: string;

  @Expose()
  @IsString()
  public ReceiverCode: string;

  @Expose()
  @IsString()
  public PaymentDestination: string;

  @Expose()
  @IsString()
  public PhoneNumber: string;

  @Expose()
  @IsString()
  public Email: string;

  @Expose()
  @IsOptional()
  @IsNumber()
  public RegionID: number;
}
