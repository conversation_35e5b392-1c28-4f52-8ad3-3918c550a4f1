import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { UploadHistory } from '../entities/import/upload-history.entity';
import incompleteStatus from '../common/constants/incomplete-status';
import { UploadHistoryDto } from './dto/upload-history.dto';

@Injectable()
export class UploadHistoryService {
  constructor(
    @InjectRepository(UploadHistory)
    private readonly uploadHistoryRepository: Repository<UploadHistory>,
  ) {}

  public async getList(data: UploadHistoryDto): Promise<UploadHistory[]> {
    const records = await this.uploadHistoryRepository
      .createQueryBuilder('iuh')
      .select([
        'iuh.*',
        'status."Name" as "Status"',
        'action."ID" as "ActionUploadHistoryID"',
        'action."CatalogID" as "ActionCatalogID"',
        'action."StatusID" as "ActionStatusID"',
        '"actionStatus"."Name" as "ActionStatus"',
        '"actionCatalog"."Name" as "ActionCatalog"',
      ])
      .leftJoin('iuh.status', 'status')
      .leftJoin('iuh.actionToImportHistory', 'actionToImport')
      .leftJoin('actionToImport.action', 'action')
      .leftJoin('action.status', 'actionStatus')
      .leftJoin('action.catalog', 'actionCatalog')
      .where('iuh."IsDeleted" = 0')
      .andWhere('iuh."InsertedUserID" = :userId', { userId: data.userID })
      .andWhere('iuh."ImportListID" = :catalogId', {
        catalogId: data.catalogID,
      })
      .orderBy('iuh."ID"', 'DESC')
      .limit(15)
      .execute();

    const rows: UploadHistory[] = [];
    for (const record of records) {
      if (record.ActionUploadHistoryID) {
        record['PostAction'] = {
          ID: record.ActionUploadHistoryID,
          CatalogID: record.ActionCatalogID,
          Catalog: record.ActionCatalog,
          StatusID: record.ActionStatusID,
          Status: record.ActionStatus,
        };
      }

      delete record.ActionUploadHistoryID;
      delete record.ActionCatalogID;
      delete record.ActionCatalog;
      delete record.ActionStatusID;
      delete record.ActionStatus;

      rows.push(record);
    }

    return rows;
  }

  public async delete(id: string, userId: number): Promise<any> {
    const record = await this.uploadHistoryRepository.findOne({
      id: id,
      isDeleted: 0,
    });
    if (record && record.insertedUserId !== userId) {
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    } else if (!record) {
      throw new HttpException('NotFound', HttpStatus.NOT_FOUND);
    } else if (!incompleteStatus.includes(record.statusId)) {
      throw new HttpException(
        {
          message: 'You can not delete incomplete import',
        },
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    } else {
      await this.uploadHistoryRepository.update(
        { id: id, insertedUserId: userId, isDeleted: 0 },
        { isDeleted: 1 },
      );
      return { status: 'success' };
    }
  }

  public async deleteAll(catalogId: number, userId: number): Promise<any> {
    await this.uploadHistoryRepository.update(
      {
        importListId: catalogId,
        insertedUserId: userId,
        isDeleted: 0,
        statusId: In(incompleteStatus),
      },
      { isDeleted: 1 },
    );
    return { status: 'success' };
  }
}
