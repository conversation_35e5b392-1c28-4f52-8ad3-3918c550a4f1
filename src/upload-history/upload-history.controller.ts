import {
  Body,
  Controller,
  Get,
  Query,
  Param,
  Put,
  Delete,
} from '@nestjs/common';

import { UploadHistoryDto } from './dto/upload-history.dto';
import { UploadHistoryService } from './upload-history.service';

@Controller('admin/import-upload-history')
export class UploadHistoryController {
  constructor(private uploadHistoryService: UploadHistoryService) {}

  @Get()
  async getUserUploadHistory(@Query() params: UploadHistoryDto) {
    return this.uploadHistoryService.getList(params);
  }

  @Put(':id')
  async deleteUserUploadHistory(
    @Body('userID') userID: number,
    @Param('id') id: string,
  ) {
    return this.uploadHistoryService.delete(id, userID);
  }

  @Delete(':id')
  async deleteAllUserUploadHistory(
    @Query('userID') userID: number,
    @Param('id') id: number,
  ) {
    return this.uploadHistoryService.deleteAll(id, userID);
  }
}
