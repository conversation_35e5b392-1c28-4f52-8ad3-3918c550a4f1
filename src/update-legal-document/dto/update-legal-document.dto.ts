import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { LegalDocumentDTO } from './legal-document.dto';

export class UpdateLegalDocumentDto {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: LegalDocumentDTO[];

  importFile: string;

  typeID: number;
  parameters: string[];
}
