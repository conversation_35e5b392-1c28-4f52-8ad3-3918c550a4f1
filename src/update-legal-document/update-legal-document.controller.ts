import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { LegalDocumentDTO } from './dto/legal-document.dto';
import { UpdateLegalDocumentService } from './update-legal-document.service';
import { UpdateLegalDocumentDto } from './dto/update-legal-document.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/update-legal-document')
export class UpdateLegalDocumentController {
  constructor(private updateLegalDocumentService: UpdateLegalDocumentService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() updateLegalDocumentDTO: UpdateLegalDocumentDto) {
    const { importFile, userID, typeID } = updateLegalDocumentDTO;

    const validationResult =
      await this.updateLegalDocumentService.checkFileTemplate(
        typeID,
        importFile,
      );

    if (!validationResult.success) {
      return validationResult;
    }

    return this.updateLegalDocumentService.importLargeFile(
      importFile,
      userID,
      LegalDocumentDTO,
      { typeId: typeID },
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream =
      await this.updateLegalDocumentService.getGeneratedCsvFields(
        query.id,
        query.parameters,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=UpdateLegalDocument - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=UpdateLegalDocument - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.updateLegalDocumentService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
