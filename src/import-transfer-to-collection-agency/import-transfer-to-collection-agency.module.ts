import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { TransferToCollectionAgency } from '../entities/data/transfer-to-collection-agency.entity';
import { EventsGateway } from '../events/events.gateway';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportTransferToCollectionAgencyController } from './import-transfer-to-collection-agency.controller';
import { ImportTransferToCollectionAgencyService } from './import-transfer-to-collection-agency.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      TransactionRepository,
      InvoiceRepository,
      TransferToCollectionAgency,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportTransferToCollectionAgencyController],
  providers: [
    ImportTransferToCollectionAgencyService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportTransferToCollectionAgencyModule {}
