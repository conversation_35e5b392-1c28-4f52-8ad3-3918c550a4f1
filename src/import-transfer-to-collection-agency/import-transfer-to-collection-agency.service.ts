import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { TemporaryTransferToCollectionAgency } from '../events/listeners/transfer-to-collection-agency-listener/temporary-tables/temporary-transfer-to-collection-agency';
import { TransferToCollectionAgencyPublisher } from '../events/publishers/transfer-to-collection-agency-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { TransferToCollectionAgencyRepository } from '../repositories/data/transfer-to-collection-agency.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { CollectionAgencyDTO } from './dto/collection-agency.dto';

const INSERTED_RECORD_NUMBER = 'Number of inserted records';
const UPDATED_RECORD_NUMBER = 'Number of updated records';

@Injectable()
export class ImportTransferToCollectionAgencyService extends BaseImportService<CollectionAgencyDTO> {
  catalog: CatalogEnum = CatalogEnum.TransferToCollectionAgency;
  protected catalogName: CatalogNameEnum =
    CatalogNameEnum.TransferToCollectionAgency;
  protected publisher: ImportPublisher<any> =
    new TransferToCollectionAgencyPublisher(this.connection);

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly transferToCollectionAgencyRepository: TransferToCollectionAgencyRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertTransfers = result?.insertTransfers;
    const updateTransfers = result?.updateTransfers;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_RECORD_NUMBER]: 0,
          [UPDATED_RECORD_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_RECORD_NUMBER]:
        this.resultDetalization[id][0][INSERTED_RECORD_NUMBER],
      [UPDATED_RECORD_NUMBER]:
        this.resultDetalization[id][0][UPDATED_RECORD_NUMBER],
    };

    if (insertTransfers.length > 0) {
      const transfers = insertTransfers.map((item: any) => {
        return this.transferToCollectionAgencyRepository.create(item);
      });

      await queryRunner.manager.save(transfers);
      detalization[INSERTED_RECORD_NUMBER] += transfers.length;
    }

    if (updateTransfers.length > 0) {
      const temporaryTransferToCollectionAgency =
        new TemporaryTransferToCollectionAgency(
          queryRunner.manager,
          this.config,
          Object.keys(updateTransfers[0]),
        );
      await temporaryTransferToCollectionAgency.manualInitialize();
      await temporaryTransferToCollectionAgency.insert(updateTransfers);

      detalization[UPDATED_RECORD_NUMBER] += updateTransfers.length;
    }

    this.resultDetalization[id][0][INSERTED_RECORD_NUMBER] +=
      detalization[INSERTED_RECORD_NUMBER];
    this.resultDetalization[id][0][UPDATED_RECORD_NUMBER] +=
      detalization[UPDATED_RECORD_NUMBER];
  }
}
