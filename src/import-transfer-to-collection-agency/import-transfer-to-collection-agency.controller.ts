import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { CollectionAgencyDTO } from './dto/collection-agency.dto';
import { ImportTransferToCollectionAgencyService } from './import-transfer-to-collection-agency.service';
import { TransferToCollectionAgencyDTO } from './dto/transfer-to-collection-agency.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-transfer-to-collection-agency')
export class ImportTransferToCollectionAgencyController {
  constructor(
    private importTransferToCollectionAgencyService: ImportTransferToCollectionAgencyService,
  ) {}

  @Post()
  @CsvImport('file')
  async import(
    @Body() transferToCollectionAgencyDTO: TransferToCollectionAgencyDTO,
  ) {
    const { importFile, userID } = transferToCollectionAgencyDTO;

    return this.importTransferToCollectionAgencyService.importLargeFile(
      importFile,
      userID,
      CollectionAgencyDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream =
      await this.importTransferToCollectionAgencyService.getGeneratedCsv(
        query.id,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportTransferToCollectionAgency - ' +
          query.id +
          '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportInfoToCollectionAgency - ' +
          query.id +
          '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output =
      await this.importTransferToCollectionAgencyService.getDetailsPath(
        params.id,
      );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
