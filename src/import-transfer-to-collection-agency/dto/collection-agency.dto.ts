import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class CollectionAgencyDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  @IsString()
  public AccountNum: string;

  @Expose()
  @IsString()
  public TransferDate: string;

  @Expose()
  @IsString()
  public CollectionAgencyName: string;

  @Expose()
  @IsString()
  public CollectionAgencyAddress: string;

  @Expose()
  @IsString()
  public CollectionAgencyEmail: string;

  @Expose()
  @IsString()
  public EDRPOU: string;
}
