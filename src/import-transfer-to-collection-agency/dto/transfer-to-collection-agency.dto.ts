import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { CollectionAgencyDTO } from './collection-agency.dto';

export class TransferToCollectionAgencyDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: CollectionAgencyDTO[];

  importFile: string;
}
