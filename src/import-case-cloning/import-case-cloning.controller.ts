import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportCaseCloningService } from './import-case-cloning.service';
import { CaseCloningDTO } from './dto/case-cloning.dto';
import { ImportCaseCloningDTO } from './dto/import-case-cloning.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-case-cloning')
export class ImportCaseCloningController {
  constructor(private importCaseCloningService: ImportCaseCloningService) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importCaseCloningService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportCaseCloning - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportCaseCloning - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file', CaseCloningDTO)
  import(@Body() importCaseCloningDto: ImportCaseCloningDTO) {
    const { importData, userID } = importCaseCloningDto;
    return this.importCaseCloningService.import(importData, userID);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importCaseCloningService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
