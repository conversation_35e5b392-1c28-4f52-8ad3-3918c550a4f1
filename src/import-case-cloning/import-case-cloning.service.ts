import { Inject, Injectable } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { Import } from 'src/import/import.service';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { ImportCaseCloningPublisher } from '../events/publishers/import-case-cloning-publisher';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { CaseCloningDTO } from './dto/case-cloning.dto';

@Injectable()
export class ImportCaseCloningService extends Import<CaseCloningDTO> {
  catalog: CatalogEnum = CatalogEnum.CaseCloning;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super();
  }

  preValidate = undefined;

  public async publish(
    importData: CaseCloningDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<void> {
    new ImportCaseCloningPublisher(this.connection).publish({
      importData,
      userId,
      uploadHistoryId,
    });
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }
}
