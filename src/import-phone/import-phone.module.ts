import { HttpModule, Module } from '@nestjs/common';
import { ImportPhoneController } from './import-phone.controller';
import { ImportPhoneService } from './import-phone.service';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { DebtorRepository } from 'src/repositories/data/debtor.repository';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ContactPersonRepository } from 'src/repositories/data/contact-person.repository';
import { ContactPersonRelationRepository } from 'src/repositories/data/contact-person-relation.repository';
import { LegalInvoiceRepository } from 'src/repositories/legal/legal-invoice.repository';
import { CourtProcessRepository } from 'src/repositories/legal/court-process.repository';
import { ImportRedisModule } from 'src/common/redis/import-redis.module';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { EventsGateway } from 'src/events/events.gateway';
import { ApiClient } from 'src/common/api.client';
import { PhoneCodeRepository } from 'src/repositories/dictionary/phone-code.repository';

@Module({
  controllers: [ImportPhoneController],
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        console.log(
          'config validationService',
          config.get<string>('validationService.url'),
        );
        return {
          baseURL: config.get<string>('validationService.url'),
          timeout: 5000,
        };
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      DebtorRepository,
      CaseRepository,
      ContactPersonRepository,
      ContactPersonRelationRepository,
      LegalInvoiceRepository,
      CourtProcessRepository,
      PhoneCodeRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [
    ImportPhoneService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportPhoneModule {}
