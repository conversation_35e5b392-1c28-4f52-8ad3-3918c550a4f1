export interface ImportPhoneFileData {
  PhoneID: string;
  CaseID: string;
  PhoneNumber: string;
  PhoneNote: string;
  PhoneType: string;
  PhoneSource: string;
  PartyType: string;
  ContactPersonID: string;
}

export interface TransformedPhoneFileData {
  PhoneID: Array<string | number>;
  CaseID: Array<number>;
  PhoneNumber: Array<string>;
  PhoneNote: Array<string>;
  PhoneType: Array<string>;
  PhoneSource: Array<string>;
  PartyType: Array<string>;
  ContactPersonID: Array<string>;
}
