import { BaseImportService } from 'src/common/imports/base-import.service';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { CatalogNameEnum } from 'src/entities/enum/catalog-name.enum';
import { ImportPublisher } from 'src/events/publishers/import-publisher';

import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { REDIS } from '../common/redis/constants';
import { EventsGateway } from '../events/events.gateway';
import { CaseRepository } from '../repositories/data/case.repository';
import { ContactPersonRelationRepository } from '../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportPhonePublisher } from 'src/events/publishers/import-phone-publisher';
import { PhonePreImportDataDto } from 'src/events/listeners/phone-listener/dto/phone-pre-import-data.dto';
import { getManager, QueryRunner } from 'typeorm';

@Injectable()
export class ImportPhoneService extends BaseImportService<PhonePreImportDataDto> {
  catalog: CatalogEnum = CatalogEnum.Phone;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.Phone;
  protected publisher: ImportPublisher<any> = new ImportPhonePublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly httpService: HttpService,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly debtorRepository: DebtorRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly contactPersonRepository: ContactPersonRepository,
    protected readonly contactPersonRelationRepository: ContactPersonRelationRepository,
    protected readonly legalInvoiceRepository: LegalInvoiceRepository,
    protected readonly courtProcessRepository: CourtProcessRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }
  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ) {
    console.log('pageResult', pageResult);

    // if (this.resultDetalization[id].length === 0) {
    //   this.resultDetalization[id] = result.mappingResults;
    // } else {
    //   this.resultDetalization[id].push(...result.mappingResults);
    // }

    // skip
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const dictionaryMethod =
      (await this.serviceParameterRepository.getCaseServiceParameterValueByName<
        string | null
      >('ImportDictionaryMethod')) ?? 'defaultDownloadDictionary';

    if (dictionaryMethod === 'defaultDownloadDictionary') {
      return await this.defaultDownloadDictionary();
    }

    if (dictionaryMethod === 'SVHUDownloadDictionary') {
      return await this.SVHUDownloadDictionary();
    }

    if (dictionaryMethod === 'CROADownloadDictionary') {
      return await this.CROADownloadDictionary();
    }

    throw new Error('[getDictionaryData] Method not found');
  }

  public async defaultDownloadDictionary(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    const phoneToolSourceFeature =
      await this.serviceParameterRepository.getCaseServiceParameterValueByName(
        'phoneToolSourceFeature',
      );

    const phoneVisibilityLevelSetting =
      (await this.serviceParameterRepository.getCaseServiceParameterValueByName(
        'PhoneVisibilityLevelSetting',
      )) as Record<string, any>;

    const usePhone2Feature =
      await this.serviceParameterRepository.getGlobalParameterValueByName(
        'usePhone2Feature',
      );

    const phoneSources = await getManager().query(`
      select "Name" 
      from "Dictionary"."DataSource" ds
      where ds."Availability"->>'Phone' = '1'
      order by 1;
    `);

    if (phoneToolSourceFeature) {
      data.CasePhoneSource = phoneSources.map((index: any) => index.Name);
      data.DebtorPhoneSource = phoneSources.map((index: any) => index.Name);
    } else {
      data.PhoneSource = phoneSources.map((index: any) => index.Name);
    }

    if (
      phoneVisibilityLevelSetting &&
      phoneVisibilityLevelSetting.FeatureEnabledImportPhone
    ) {
      const visibilityLevel = await getManager().query(`
        select "Name" 
        from "Dictionary"."VisibilityLevel" vl
        where vl."IsDeleted" = 0
        order by 1;
      `);

      data.Level = visibilityLevel.map((index: any) => index.Name);
    }

    if (usePhone2Feature) {
      const partyTypes = await getManager().query(`
        select "Name" 
        from "Dictionary"."PartyType" pt
        where pt."Availability"->>'Phone2ImportPhone' = '1'
        and pt."IsDeleted" = 0
        order by 1;
      `);

      data.PartyType = partyTypes.map((index: any) => index.Name);
    } else {
      const phoneTypes = await getManager().query(`
        select "Name" 
        from "Dictionary"."PhoneType" pt
        where pt."IsDeleted" = 0
        order by 1;
      `);

      data.PhoneType = phoneTypes.map((index: any) => index.Name);

      const partyTypes = await getManager().query(`
        select "Name" 
        from "Dictionary"."PartyType" pt
        where pt."Availability"->>'Phone' = '1'
        and pt."IsDeleted" = 0
        order by 1;
      `);

      data.PartyType = partyTypes.map((index: any) => index.Name);
    }

    return data;
  }

  async SVHUDownloadDictionary() {
    const data: { [p: string]: string[] } = {};

    const phoneTypes = await getManager().query(`
        select pt."Name" as "Name"
        from "Dictionary"."PhoneType" pt
        where pt."IsDeleted" = 0
        order by 1;
      `);

    data.PhoneType = phoneTypes.map((index: any) => index.Name);

    const phoneSource = await getManager().query(`
        select ds."Name" as "Name"
        from "Dictionary"."DataSource" ds
        where ds."Availability"->>'Phone' = '1'
        order by 1;
      `);

    data.PhoneSource = phoneSource.map((index: any) => index.Name);

    const status = await getManager().query(`
        select ps."Name" as "Name"
        from "Dictionary"."Custom001PhoneStatus" ps
        where ps."IsDeleted" = 0
        order by 1;
      `);

    data.Status = status.map((index: any) => index.Name);

    return data;
  }

  async CROADownloadDictionary() {
    const data: { [p: string]: string[] } = {};

    const phoneTypes = await getManager().query(`
        select pt."Name" as "Name"
        from "Dictionary"."PhoneType" pt
        where pt."Availability"->>'Phone' = '1'
        and pt."IsDeleted" = 0
        order by 1;
      `);

    data.PhoneType = phoneTypes.map((index: any) => index.Name);

    const phoneSource = await getManager().query(`
        select ds."Name" as "Name"
        from "Dictionary"."DataSource" ds
        where ds."Availability"->>'Phone' = '1'
        order by 1;
      `);

    data.PhoneSource = phoneSource.map((index: any) => index.Name);

    const status = await getManager().query(`
        select ps."Name" as "Name"
        from "Dictionary"."Custom001PhoneStatus" ps
        where ps."IsDeleted" = 0
        order by 1;
      `);

    data.Status = status.map((index: any) => index.Name);

    return data;
  }
}
