import { Body, Controller, Get, Param, Post, Query, Res } from '@nestjs/common';
import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { ImportPhoneDto } from './dto/import-phone.dto';
import { ImportPhoneService } from './import-phone.service';
import { PhonePreImportDataDto } from 'src/events/listeners/phone-listener/dto/phone-pre-import-data.dto';
import { Response } from 'express';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-phone')
export class ImportPhoneController {
  constructor(private importPhoneService: ImportPhoneService) {}

  @Post()
  @CsvImport('file')
  import(@Body() dto: ImportPhoneDto) {
    const { importFile, userID } = dto;
    return this.importPhoneService.importLargeFile(
      importFile,
      userID,
      PhonePreImportDataDto,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query('id') id: string,
  ): Promise<any> {
    const readStream = await this.importPhoneService.getGeneratedCsv(id);

    if (id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename="ImportPhone - ' + id + '.xlsx"',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename="ImportPhone - ' + id + '.csv"',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    // const output = await this.importPhoneService.getDetailsPath(params.id);
    // if (!output) {
    //   throw new NotFoundException('Detalization file not found');
    // }
    // const filepath = path.join(process.cwd(), output);
    // if (!fs.existsSync(filepath)) {
    //   throw new NotFoundException('Detalization file not found');
    // }
    // const file = fs.createReadStream(path.join(process.cwd(), output));
    // response.set(
    //   disposition,
    //   'attachment; filename=details-' + params.id + '.csv',
    // );
    // response.set(contentType, 'text/plain');
    // file.pipe(response);
  }
}
