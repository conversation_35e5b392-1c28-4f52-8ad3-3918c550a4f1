import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { PhonePreImportDataDto } from 'src/events/listeners/phone-listener/dto/phone-pre-import-data.dto';

export class ImportPhoneDto {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: PhonePreImportDataDto[];

  importFile: string;

  typeID: number;
  parameters: string[];
}
