import { Redis } from 'ioredis';
import { PhoneCode } from 'src/entities/dictionary/phone-code.entity';
import { PhoneCodeRepository } from 'src/repositories/dictionary/phone-code.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';

export abstract class BasePhoneValidator {
  constructor(
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly phoneCodeRepository: PhoneCodeRepository,
    protected readonly redis: Redis,
  ) {}

  async formatDataBeforeValidate(data: any) {
    const country =
      await this.serviceParameterRepository.getGlobalParameterValueByName<string>(
        'country',
      );

    for (const [key, value] of data.PhoneNumber.entries()) {
      if (country === 'Hu') {
        data.PhoneNumber[key] = await this.normalizePhoneNumberForHungary(
          value,
        );
      }

      if (data.CaseID && data.CaseID[key] !== undefined) {
        data.CaseID[key] = Number.parseInt(data.CaseID[key], 10);
      }

      if (data.PhoneID && data.PhoneID[key] !== undefined) {
        data.PhoneID[key] = Number.parseInt(data.PhoneID[key], 10);
      }
    }

    return data;
  }

  protected async normalizePhoneNumberForHungary(
    phone: string,
  ): Promise<string> {
    // Extract only digits from the phone number
    phone = phone.replace(/\D/g, '').trim();

    const phoneCode =
      ((await this.serviceParameterRepository.getImportServiceParameterValueByName(
        'phoneCodeLengthHungary',
      )) as number) ?? 0;

    switch (true) {
      case phone.length === 7:
        phone = '061' + phone;
        break;

      case phone.length === 8 &&
        (await this.isNonMobileCodeHungary(
          phone.slice(0, Math.max(0, Math.max(phoneCode))),
        )):
        phone = '06' + phone;
        break;

      case phone.length === 9 &&
        phone[0] === '6' &&
        (await this.isNonMobileCodeHungary(
          phone.slice(1, Math.max(phoneCode)),
        )):
        phone = '0' + phone;
        break;

      case phone.length === 9 &&
        phone[0] !== '6' &&
        (await this.isMobileCodeHungary(phone.slice(0, 2))):
        phone = '06' + phone;
        break;

      case phone.length === 10 &&
        (phone.startsWith('36') || phone.startsWith('06')) &&
        (await this.isNonMobileCodeHungary(
          phone.slice(2, Math.max(phoneCode)),
        )):
        phone = phone.slice(1);
        break;

      case phone.length === 10 &&
        phone[0] === '6' &&
        (await this.isMobileCodeHungary(phone.slice(1, 3))):
        phone = '0' + phone;
        break;

      case phone.length === 11 &&
        (phone.startsWith('36') || phone.startsWith('06')) &&
        (await this.isMobileCodeHungary(phone.slice(2, Math.max(phoneCode)))):
        phone = phone.slice(1);
        break;

      default:
        return phone;
    }

    return phone;
  }

  async isNonMobileCodeHungary(phoneCode: string): Promise<boolean> {
    const allPhoneNonMobileCodesHungary = await this.redis.get(
      'allPhoneNonMobileCodesHungary',
    );
    let nonMobileCodes: PhoneCode[] = [];
    let codes: string[] = [];

    if (allPhoneNonMobileCodesHungary) {
      nonMobileCodes = JSON.parse(allPhoneNonMobileCodesHungary) as PhoneCode[];
    }

    if (!nonMobileCodes) {
      nonMobileCodes = await this.phoneCodeRepository.getNonMobileCodes();
      codes = nonMobileCodes.map((code) => code.name);
      this.redis.set('allPhoneNonMobileCodesHungary', codes, 'EX', 300);
    }

    if (!nonMobileCodes) {
      return false;
    }
    // method not tested
    return codes.includes(phoneCode.slice(0, 7));
  }

  async isMobileCodeHungary(phoneCode: string): Promise<boolean> {
    const allPhoneMobileCodesHungary = await this.redis.get(
      'allPhoneMobileCodesHungary',
    );
    let mobileCodes: PhoneCode[] = [];
    let codes: string[] = [];

    if (allPhoneMobileCodesHungary) {
      mobileCodes = JSON.parse(allPhoneMobileCodesHungary) as PhoneCode[];
    }

    if (!mobileCodes) {
      mobileCodes = await this.phoneCodeRepository.getMobileCodes();
      codes = mobileCodes.map((code) => code.name);
      this.redis.set('allPhoneMobileCodesHungary', codes, 'EX', 300);
    }

    if (!mobileCodes) {
      return false;
    }
    // method not tested
    return codes.includes(phoneCode.slice(0, 2));
  }
}
