import { Inject, Injectable } from '@nestjs/common';
import { BasePhoneValidator } from './base-phone-validator';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { REDIS } from 'src/common/redis/constants';
import { Redis } from 'ioredis';
import { PhoneCodeRepository } from 'src/repositories/dictionary/phone-code.repository';

@Injectable()
export class PhoneValidator extends BasePhoneValidator {
  constructor(
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly phoneCodeRepository: PhoneCodeRepository,
    @Inject(REDIS) protected readonly redis: Redis,
  ) {
    super(serviceParameterRepository, phoneCodeRepository, redis);
  }
}
