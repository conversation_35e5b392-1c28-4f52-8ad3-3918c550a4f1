import { HttpService, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';

import { HttpMethod } from 'src/common/enums/http-method.enum';

export interface RequestOptions {
  body?: any;
  queries?: any;
  headers?: any;
  baseURL?: string;
  timeout?: number;
}

@Injectable()
export class ApiClient {
  constructor(
    private httpService: HttpService,
    private config: ConfigService,
  ) {}

  public async sendRequest<T>(
    method: HttpMethod,
    url: string,
    options?: RequestOptions,
  ): Promise<T> {
    const axiosOptions: AxiosRequestConfig = {
      method,
      baseURL: options?.baseURL,
      url,
      data: options?.body,
      params: options?.queries,
      headers: options?.headers,
      timeout: options?.timeout || this.config.get<number>('requestTimeout'),
    };

    return new Promise(async (resolve, reject) => {
      try {
        const { data } = await this.httpService
          .request(axiosOptions)
          .toPromise();
        if (data.status >= 400) {
          return reject(data.message.error);
        }
        return resolve(data as T);
      } catch (error) {
        console.log(error);

        reject(
          `The timeout for a response from the ${axiosOptions.url} resource has expired`,
        );
      }
    });
  }
}
