import { Mo<PERSON><PERSON>, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

import redis from 'src/config/redis';
import { REDIS } from 'src/common/redis/constants';

const redisProvider: Provider = {
  provide: REDIS,
  useFactory: (config: ConfigService): Redis.Redis => {
    return new Redis({
      host: config.get<string>('redis.host'),
      port: config.get<number>('redis.port'),
      password: config.get<string>('redis.password'),
      db: config.get<number>('redis.importDBIndex'),
      keyPrefix: config.get<string>('deploySuffix'),
    });
  },
  inject: [ConfigService],
};
@Module({
  imports: [ConfigModule.forFeature(redis)],
  providers: [redisProvider],
  exports: [redisProvider],
})
export class ImportRedisModule {}
