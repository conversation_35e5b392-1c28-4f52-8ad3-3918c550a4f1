import { Modu<PERSON>, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AmqpConnectionManager, connect } from 'amqp-connection-manager';

import rabbitmq from 'src/config/rabbitmq';
import { RABBITMQ } from 'src/common/rabbitmq/constants';

const amqpProvider: Provider = {
  provide: RABBITMQ,
  useFactory: (config: ConfigService): AmqpConnectionManager => {
    return connect({
      hostname: config.get<string>('rabbitmq.host'),
      port: config.get<number>('rabbitmq.port'),
      username: config.get<string>('rabbitmq.userName') || undefined,
      password: config.get<string>('rabbitmq.password') || undefined,
    });
  },
  inject: [ConfigService],
};

@Module({
  imports: [ConfigModule.forFeature(rabbitmq)],
  providers: [amqpProvider],
  exports: [amqpProvider],
})
export class RabbitmqModule {}
