import {
  <PERSON>rrorItem,
  FailValidationResult,
  SuccessValidationResult,
  WarningItem,
} from './interfaces';
import { ValidationResult } from './validation-result.type';

export class CustomValidator {
  public static response(data?: {
    errors?: ErrorItem[];
    warnings?: WarningItem[];
  }): ValidationResult {
    if (!data) {
      return CustomValidator.success();
    } else {
      const errors = data.errors || [];
      if (errors.length > 0) {
        return CustomValidator.fail(errors);
      }

      return CustomValidator.success(data.warnings);
    }
  }

  public static success(warnings?: WarningItem[]): SuccessValidationResult {
    return {
      success: true,
      ...(warnings && warnings.length > 0 && { warnings }),
    };
  }

  public static fail(errors: ErrorItem[]): FailValidationResult {
    return { success: false, errors };
  }
}
