import { CustomValidator } from './custom-validator';
import { ValidationResult } from './validation-result.type';
import { ErrorItem } from './interfaces';

export const SimpleValidator: {
  [key: string]: (
    column: string,
    values: any,
    skipOnEmpty?: boolean,
  ) => ValidationResult;
} = {
  unique(
    column: string,
    values: any[],
    skipOnEmpty?: boolean,
  ): ValidationResult {
    const errors: ErrorItem[] = [];
    const uniqueValues = new Set();

    for (const [index, value] of values.entries()) {
      if (!value && skipOnEmpty) {
        continue;
      }

      if (!uniqueValues.has(value)) {
        uniqueValues.add(value);
      } else {
        errors.push({
          column,
          errorCode: 'Unique',
          errorParams: '',
          row: getExcelRowNumberByIndex(index),
        });
      }
    }

    return CustomValidator.response({ errors });
  },

  required(column: string, values: any[]): ValidationResult {
    const errors: ErrorItem[] = [];

    for (const [index, value] of values.entries()) {
      if (!value) {
        errors.push({
          column,
          errorCode: 'Required',
          errorParams: '',
          row: getExcelRowNumberByIndex(index),
        });
      }
    }

    return CustomValidator.response({ errors });
  },
};

function getExcelRowNumberByIndex(index: number): number {
  // Given the header and start of the arrays from 0
  const DIFFERENCE_BETWEEN_ROW = 2;
  return index + DIFFERENCE_BETWEEN_ROW;
}
