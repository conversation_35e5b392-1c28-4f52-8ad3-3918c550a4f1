import { EntityManager } from 'typeorm';

interface TemporaryDeclaration {
  declaration: string;
  tableName?: string;
}

export function Temporary(declaration: TemporaryDeclaration) {
  return function <T extends { new (...args: any[]): any }>(ctr: T) {
    return class extends ctr {
      manager: EntityManager;
      tableName = declaration.tableName ?? ctr.name;
      initialize: () => void = async () => {
        await this.manager.query(`DROP TABLE if EXISTS "${this.tableName}"`);
        await this.manager.query(
          `CREATE TEMPORARY TABLE "${this.tableName}" (${declaration.declaration});`,
        );
      };
      dispose: () => Promise<void> = async () => {
        this.manager.query(`DROP TABLE "${this.tableName}"`);
      };
    };
  };
}
