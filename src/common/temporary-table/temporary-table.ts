import { ConfigService } from '@nestjs/config';
import { EntityManager, EntitySchema } from 'typeorm';

import { splitOnChunks } from 'src/common/helpers/split-on-chunks';

export abstract class TemporaryTable<T> {
  protected tableName: string;
  protected tableEntity: EntitySchema;
  protected abstract manager: EntityManager;
  protected abstract config: ConfigService;
  protected abstract updateOriginTable(): Promise<void>;
  protected abstract decompose(data: T[]): any[];

  initialize: () => Promise<void>;
  dispose: () => Promise<void>;

  async insert(data: T[], options?: { chunk: number }): Promise<void> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const chunk = options?.chunk ?? this.config.get<number>('chunk.default')!;
    const chunks = splitOnChunks(data, chunk);
    const promises: Array<Promise<any>> = [];

    for (const chunk of chunks) {
      const values = this.decompose(chunk);

      promises.push(
        this.manager
          .createQueryBuilder()
          .insert()
          .into(this.tableEntity)
          .values(values)
          .execute(),
      );
    }

    await Promise.all(promises);

    return this.updateOriginTable();
  }

  protected timestampColumn(
    value: string | Date | null | undefined,
  ): string | null {
    return value ? `${new Date(value).toUTCString()}` : null;
  }

  protected jsonColumn(value: Record<string, any>): string | null {
    return value ? JSON.stringify(value) : null;
  }

  protected booleanColumn(
    value: number | boolean | null | undefined,
  ): boolean | null {
    return value ? Boolean(value) : false;
  }

  protected nullableStringColumn(
    value: number | string | null | undefined,
  ): string | null {
    return value === null ? null : `${String(value)}`;
  }
}
