import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  BadRequestException,
} from '@nestjs/common';
import parse from 'csv-parse';
import { Duplex } from 'stream';
import { Observable } from 'rxjs';
import { appendToFile } from '../helpers/append-to-file';
import { checkIfExists } from '../helpers/check-if-exists';
import { mkDir } from '../helpers/mk-dir';

function bufferToStream(myBuffer: any) {
  const temporary = new Duplex();
  temporary.push(myBuffer);
  // eslint-disable-next-line unicorn/no-array-push-push
  temporary.push(null);
  return temporary;
}

@Injectable()
export class CsvParserLargeFileInterceptor implements NestInterceptor {
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    if (!request.file && !request.body.importData) {
      throw new BadRequestException('Need to select a file');
    }

    try {
      const { path, hasData } = await parseCsv(request.file.buffer);
      if (!hasData) {
        throw new BadRequestException(
          'The CSV file contains only headers with no data rows',
        );
      }

      request.body.importFile = path;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'The file could not be read. The file must be in the CSV format',
      );
    }
    return next.handle();
  }
}

async function parseCsv(
  buffer: Buffer,
): Promise<{ path: string; hasData: boolean }> {
  return new Promise(async (resolve, reject) => {
    const stream = bufferToStream(buffer);

    const timestamp = Date.now();
    const filepath = `output/buffers`;
    const filename = `${filepath}/json-buffer-${timestamp}.csv`;

    const parser = stream.pipe(
      parse({ delimiter: ';', columns: true, bom: true }),
    );

    if (!(await checkIfExists(filepath))) {
      await mkDir(filepath, {
        recursive: true,
      });
    }

    let hasData = false;

    parser.on('readable', async () => {
      let record;
      while ((record = parser.read())) {
        hasData = true;
        await appendToFile(filename, JSON.stringify(record) + '\n', 'utf8');
      }
    });

    parser.on('end', () => {
      resolve({ path: filename, hasData });
    });

    parser.on('error', (error) => {
      reject(error);
    });
  });
}
