import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  BadRequestException,
} from '@nestjs/common';
import parse from 'csv-parse';
import { Duplex } from 'stream';
import { plainToClass } from 'class-transformer';
import { Observable } from 'rxjs';

function bufferToStream(myBuffer: any) {
  const temporary = new Duplex();
  temporary.push(myBuffer);
  // eslint-disable-next-line unicorn/no-array-push-push
  temporary.push(null);
  return temporary;
}

@Injectable()
export class CsvParserInterceptor implements NestInterceptor {
  constructor(private dto: any) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    if (!request.file && !request.body.importData) {
      throw new BadRequestException('Need to select a file');
    }
    if (request.body.importData) {
      plainToClass(this.dto, request.body.importData, {
        excludeExtraneousValues: true,
        enableImplicitConversion: true,
      });
      return next.handle();
    }
    try {
      const csvParsedData = await this.parseCsv(request.file.buffer);
      request.body.importData = plainToClass(this.dto, csvParsedData, {
        excludeExtraneousValues: true,
        enableImplicitConversion: true,
      });
    } catch {
      throw new BadRequestException(
        'The file could not be read. The file must be in the CSV format',
      );
    }
    return next.handle();
  }

  public async parseCsv(buffer: Buffer): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const stream = bufferToStream(buffer);

      const records: any = [];
      const parser = stream.pipe(
        parse({ delimiter: ';', columns: true, bom: true }),
      );
      parser.on('readable', () => {
        let record;
        while ((record = parser.read())) {
          records.push(record);
        }
        resolve(records);
      });

      parser.on('error', () => {
        reject();
      });
    });
  }
}
