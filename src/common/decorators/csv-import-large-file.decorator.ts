import { applyDecorators, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CsvParserLargeFileInterceptor } from '../interceptors/csv-parser-large-file.interceptor';

export const CsvImport = (fileName: string) =>
  applyDecorators(
    UseInterceptors(FileInterceptor(fileName)),
    UseInterceptors(new CsvParserLargeFileInterceptor()),
  );
