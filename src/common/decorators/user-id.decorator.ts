import {
  createParamDecorator,
  ExecutionContext,
  BadRequestException,
} from '@nestjs/common';

export const UserID = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest();
    let userID;

    // Check if x-user-id header exists
    if (request.headers['x-user-id']) {
      userID = Number.parseInt(request.headers['x-user-id']);
    } else if (request.method === 'GET') {
      // If request method is GET, get userID from query string
      userID = Number.parseInt(request.query.userID);
    } else if (request.method === 'POST') {
      // If request method is POST, get userID from request body
      userID = Number.parseInt(request.body.userID);
    }

    // If userID is still undefined, throw an error
    if (!userID) {
      throw new BadRequestException('UserID not provided');
    }

    return userID;
  },
);
