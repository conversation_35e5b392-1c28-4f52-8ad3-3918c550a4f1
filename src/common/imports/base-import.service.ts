import { HttpService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Sentry from '@sentry/node';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { plainToClass } from 'class-transformer';
import { stream as excelStream } from 'exceljs';
import fs from 'fs';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import path from 'path';
import { v4 as uuid } from 'uuid';
import readline from 'readline';
import stream from 'stream';
import { getManager, In, QueryRunner } from 'typeorm';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../config/redis-import-keys';
import { ActionToImportHistory } from '../../entities/data/action-to-import-history.entity';
import { UploadHistory } from '../../entities/import/upload-history.entity';
import { UploadHistory as ActionUploadHistory } from '../../entities/action/upload-history.entity';
import { CatalogNameEnum } from '../../entities/enum/catalog-name.enum';
import { EventRuleLog } from '../../entities/list/event-rule-log.entity';
import { CheckEventRulesPublisher } from '../../events/publishers/check-event-rules-publisher';
import { ImportPublisher } from '../../events/publishers/import-publisher';
import { BaseImportEvent } from '../../events/types/base-import-event.interface';
import { CatalogValidator } from '../../import/catalog-validator';
import { ValidatorDataAccess } from '../../import/data-access/validator.data-access';
import { UploadStatus } from '../../import/upload-status.enum';
import { EventsGateway } from '../../events/events.gateway';
import { CatalogColumnRepository } from '../../repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from '../../repositories/import/upload-history.repository';
import { EventRuleLogRepository } from '../../repositories/list/event-rule-log.repository';
import { ApiClient } from '../api.client';
import { ToolSourceEnum } from '../enums/tool-source.enum';
import { appendToFile } from '../helpers/append-to-file';
import { checkIfExists } from '../helpers/check-if-exists';
import { countRows } from '../helpers/count-rows';
import { mkDir } from '../helpers/mk-dir';
import { rmDir } from '../helpers/rm-dir';
import { splitOnChunks } from '../helpers/split-on-chunks';
import { FailValidationResult } from '../validation/interfaces';
import { ValidationResult } from '../validation/validation-result.type';
import { BaseImportInterface } from './base-import.interface';
import { Import } from 'src/import/import.service';
import { ImportDTO } from './dto/import.dto';
import { ParameterRepository } from 'src/repositories/dictionary/parameter.repository';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

type PreValidateType<T, V> = V extends undefined ? T : V;

const caseServiceURLKey = 'caseService.url';
const taskManagerURL = '/legal-task-manager';

export abstract class BaseImportService<T, V = undefined>
  extends Import<T, V>
  implements BaseImportInterface
{
  protected chunkSize: number;
  protected resultDetalization: { [key: number]: any[] } = {};
  protected postActionData: { [key: number]: any } = {};
  protected eventRules: { [key: number]: any } = {};

  protected abstract catalogName: CatalogNameEnum;
  protected abstract publisher: ImportPublisher<BaseImportEvent>;

  protected constructor(
    protected readonly connection: AmqpConnectionManager,
    protected readonly config: ConfigService,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly parameterRepository?: ParameterRepository,
    protected readonly dataTypeRepository?: DataTypeRepository,
    protected readonly eventRuleLogRepository?: EventRuleLogRepository,
  ) {
    super();
    this.chunkSize = this.getChunkSize();
  }

  public async publish(
    importData: any[],
    userId: number,
    id: string,
    additionalParams: any,
  ): Promise<void> {
    const numberOfCases = importData.length;
    const chunks = Math.ceil(numberOfCases / this.chunkSize);
    const chunkData = this.sliceIntoChunks(importData, this.chunkSize);

    const chunkPercent = (1 / chunks) * 100;

    await this.redis
      .pipeline()
      .set(REDIS_KEYS.progress(this.catalogName, Number(id)), 0)
      .set(REDIS_KEYS.chunkPercent(this.catalogName, Number(id)), chunkPercent)
      .set(REDIS_KEYS.pages(this.catalogName, Number(id)), chunks)
      .exec();

    for (const [page, chunk] of chunkData.entries()) {
      if (page === 0) {
        await this.emitPreparingProgress(Number(id), 0);
      }
      await this.publisher.publish({
        importData: chunk,
        userId,
        uploadHistoryId: id,
        chunk: page + 1,
        additionalParams,
      });
    }
  }

  public async importLargeFile(
    filePath: string,
    userId: number,
    type: new () => T,
    additionalParams: any = null,
  ): Promise<ValidationResult> {
    const pageSize = this.getPageSize();
    let pageNumber = 0;

    const totalRows = Number.parseInt((await countRows(filePath)).toString());

    const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });

    const uploadHistory = await this.uploadHistoryRepository.save({
      importListId: this.catalog,
      insertedUserId: userId,
      statusId: UploadStatus.New,
    });

    const batch: any[] = [];
    let lastValidationResult: ValidationResult = {
      success: true,
      id: uploadHistory.id,
      errors: [],
    } as ValidationResult;

    const chunks = Math.ceil(totalRows / pageSize);
    const chunkPercent = chunks > 0 ? (1 / chunks) * 100 : 100;

    await this.redis
      .pipeline()
      .set(REDIS_KEYS.progress(this.catalogName, Number(uploadHistory.id)), 0)
      .set(
        REDIS_KEYS.chunkPercent(this.catalogName, Number(uploadHistory.id)),
        chunkPercent,
      )
      .set(REDIS_KEYS.pages(this.catalogName, Number(uploadHistory.id)), chunks)
      .exec();

    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Number.POSITIVE_INFINITY,
    });

    const errors = [];
    await this.processRows(
      rl,
      batch,
      pageSize,
      lastValidationResult,
      async () => {
        lastValidationResult = await this.publishBatch(
          this.formatRow(batch, type),
          userId,
          uploadHistory,
          ++pageNumber,
          additionalParams,
        );

        if (!lastValidationResult.success && lastValidationResult.errors) {
          errors.push(...lastValidationResult.errors);
        }
      },
    );

    if (batch.length > 0 && lastValidationResult.success) {
      lastValidationResult = await this.publishBatch(
        this.formatRow(batch, type),
        userId,
        uploadHistory,
        ++pageNumber,
        additionalParams,
      );

      if (!lastValidationResult.success && lastValidationResult.errors) {
        errors.push(...lastValidationResult.errors);
      }
    }

    if (chunks !== pageNumber) {
      const chunkPercent = (1 / pageNumber) * 100;
      await this.redis
        .pipeline()
        .set(
          REDIS_KEYS.chunkPercent(this.catalogName, Number(uploadHistory.id)),
          chunkPercent,
        )
        .set(
          REDIS_KEYS.pages(this.catalogName, Number(uploadHistory.id)),
          pageNumber,
        )
        .exec();
    }

    await rmDir(filePath);

    if (errors.length > 0) {
      return { ...lastValidationResult, errors } as FailValidationResult;
    }

    return lastValidationResult;
  }

  protected async processRows(
    rl: any,
    batch: any[],
    pageSize: number,
    lastValidationResult: ValidationResult,
    callback: any,
  ) {
    for await (const line of rl) {
      if (batch.length === pageSize && lastValidationResult.success) {
        await callback();
        batch.length = 0;
      }
      batch.push(line);
    }
  }

  protected formatRow(lines: string[], type: any): any[] {
    const array: any[] = [];
    for (const line of lines) {
      array.push(JSON.parse(line));
    }

    return plainToClass(type, array, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true,
    });
  }

  protected async validateData(changedRecords: any, additionalParams: any) {
    const validator = new CatalogValidator(
      this.catalog,
      this.validatorDataAccess,
      this.apiClient,
    );

    const customValidationResult = await this.callCustomServiceValidation(
      changedRecords,
      additionalParams,
    );

    if (!customValidationResult.success) {
      return customValidationResult;
    }

    return await validator.validate(changedRecords);
  }

  protected async callCustomServiceValidation(
    changedRecords: any,
    additionalParams: any,
  ): Promise<ValidationResult> {
    return {
      success: true,
    };
  }

  private async publishBatch(
    data: any[],
    userId: number,
    uploadHistory: UploadHistory,
    pageNumber: number,
    additionalParams: any,
  ): Promise<ValidationResult> {
    if (uploadHistory.statusId === UploadStatus.ValidationFailed) {
      return {
        success: false,
        id: uploadHistory.id,
      } as ValidationResult;
    }
    const changedRecords = this.preValidate
      ? await this.preValidate(data)
      : (data as PreValidateType<T, V>[]);

    const validationResult = await this.validateData(
      changedRecords,
      additionalParams,
    );

    validationResult.id = uploadHistory.id;

    if (validationResult.success) {
      uploadHistory.statusId = UploadStatus.InProgress;
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: uploadHistory.statusId },
      );

      await this.customPublish(
        changedRecords,
        userId,
        uploadHistory.id,
        pageNumber,
        additionalParams,
      );
    } else {
      const pageSize = this.getPageSize();
      for (const [key, value] of validationResult.errors.entries()) {
        const row = value.row;
        validationResult.errors[key].row = (pageNumber - 1) * pageSize + row;
      }

      uploadHistory.statusId = UploadStatus.ValidationFailed;
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.ValidationFailed },
      );
    }

    return validationResult;
  }

  async customPublish(
    importData: any[],
    userId: number,
    id: string,
    pageNumber: number,
    additionalParams: any,
  ): Promise<void> {
    if (pageNumber === 1) {
      await this.emitPreparingProgress(Number(id), 0);
    }

    try {
      await this.publisher.publish({
        importData: importData,
        userId,
        uploadHistoryId: id,
        chunk: pageNumber,
        additionalParams,
      });
    } catch (error) {
      console.log(error);
    }
  }

  public async apply(importDTO: ImportDTO): Promise<ValidationResult> {
    const { importData, userID } = importDTO;
    return await this.import(importData, userID);
  }

  protected async emitPreparingProgress(
    id: number,
    progress: number,
  ): Promise<void> {
    const channel = `import:${this.catalog}`;

    const payload = {
      UploadHistoryID: id,
      Progress: progress,
      StatusID: UploadStatus.InProgress,
      CatalogID: this.catalog,
    };

    this.messageGateway.server.in(channel).emit('progress', payload);
  }

  protected async emitProgress(id: number, progress: number): Promise<void> {
    const channel = `import:${this.catalog}`;

    const payload = {
      UploadHistoryID: id,
      Progress: Math.ceil(progress) > 100 ? 100 : Math.ceil(progress),
      StatusID: UploadStatus.Saving,
      CatalogID: this.catalog,
    };

    this.messageGateway.server.in(channel).emit('progress', payload);
  }

  protected async emitCompleted(id: number, statusId: number): Promise<void> {
    const channel = `import:${this.catalog}`;
    const payload = {
      UploadHistoryID: id,
      CatalogID: this.catalog,
      StatusID: statusId,
      Progress: 100,
    };
    this.messageGateway.server.in(channel).emit('progress', payload);
  }

  protected async clearOrderCache(id: number): Promise<void> {
    await this.redis
      .pipeline()
      .del(REDIS_KEYS.results(this.catalogName, id))
      .del(REDIS_KEYS.pages(this.catalogName, id))
      .del(REDIS_KEYS.chunkPercent(this.catalogName, id))
      .del(REDIS_KEYS.progress(this.catalogName, id))
      .exec();

    if (this.eventRules[id]) {
      delete this.eventRules[id];
    }
  }

  protected async saveDetalizationToFile(id: number): Promise<string | null> {
    if (this.resultDetalization[id] && this.resultDetalization[id].length > 0) {
      const csvBody = '\uFEFF';
      const headers =
        Object.keys(this.resultDetalization[id][0]).join(';') + '\n';
      const values = this.resultDetalization[id]
        .map((row) => Object.values(row).join(';'))
        .join('\n');
      return await this.writeDataToFile(id, csvBody + headers + values);
    }
    return null;
  }

  protected async generateCustomExcelFile(dictionaries: {
    [key: string]: { [key: string]: number };
  }): Promise<string> {
    const filePath = path.join(__dirname, 'dictionaries.xlsx');
    const writeStream = fs.createWriteStream(filePath, { flags: 'w' });

    const book = new excelStream.xlsx.WorkbookWriter({
      stream: writeStream,
    });

    for (const sheetName of Object.keys(dictionaries)) {
      const sheet = book.addWorksheet(sheetName);
      const sheetValues = Object.keys(dictionaries[sheetName]);
      sheet.addRow([sheetName]).commit();
      for (const sheetValue of sheetValues) {
        sheet.addRow([sheetValue]).commit();
      }
      sheet.commit();
    }

    await book.commit();
    writeStream.end();

    return filePath;
  }

  protected async generateExcelFile(dictionaries: {
    [key: string]: { [key: string]: number };
  }): Promise<stream.PassThrough> {
    const readStream = new stream.PassThrough();
    const book = new excelStream.xlsx.WorkbookWriter({
      stream: readStream,
    });
    for (const sheetName of Object.keys(dictionaries)) {
      const sheet = book.addWorksheet(sheetName);
      const sheetValues = Object.keys(dictionaries[sheetName]);
      sheet.addRow([sheetName]).commit();
      for (const sheetValue of sheetValues) {
        sheet.addRow([sheetValue]).commit();
      }
      sheet.commit();
    }

    await book.commit();
    readStream.end();
    return readStream;
  }

  protected async saveErrorToFile(id: number, error: any): Promise<string> {
    const csvBody = '\uFEFF' + 'Error\n' + error.toString();
    return this.writeDataToFile(id, csvBody);
  }

  protected getPageSize(): number {
    return 1000;
  }

  protected saveDebtorRegrouping(id: number, result: any) {
    const activateDebtorRegrouping = result?.activateDebtorRegrouping;
    if (activateDebtorRegrouping) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].activateDebtorRegrouping) {
          this.postActionData[id].activateDebtorRegrouping = [
            ...this.postActionData[id].activateDebtorRegrouping,
            ...activateDebtorRegrouping,
          ];
        } else {
          this.postActionData[id].activateDebtorRegrouping =
            activateDebtorRegrouping;
        }
      } else {
        this.postActionData[id] = {
          activateDebtorRegrouping: activateDebtorRegrouping,
        };
      }
    }
  }

  protected saveAppellateCourt(id: number, result: any) {
    const activateAppellateCourtMapping = result?.activateAppellateCourtMapping;
    if (activateAppellateCourtMapping) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].activateAppellateCourtMapping) {
          this.postActionData[id].activateAppellateCourtMapping = [
            ...this.postActionData[id].activateAppellateCourtMapping,
            ...activateAppellateCourtMapping,
          ];
        } else {
          this.postActionData[id].activateAppellateCourtMapping =
            activateAppellateCourtMapping;
        }
      } else {
        this.postActionData[id] = {
          activateAppellateCourtMapping: activateAppellateCourtMapping,
        };
      }
    }
  }

  protected saveDocumentIDs(id: number, documentIDs: number[]) {
    if (documentIDs.length > 0) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].closeTasksByDocuments) {
          this.postActionData[id].closeTasksByDocuments = [
            ...this.postActionData[id].closeTasksByDocuments,
            ...documentIDs,
          ];
        } else {
          this.postActionData[id].closeTasksByDocuments = documentIDs;
        }
      } else {
        this.postActionData[id] = {
          closeTasksByDocuments: documentIDs,
        };
      }
    }
  }

  protected saveHistoryIDs(id: number, historyIDs: number[]) {
    if (historyIDs.length > 0) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].closeTasksByActivity) {
          this.postActionData[id].closeTasksByActivity = [
            ...this.postActionData[id].closeTasksByActivity,
            ...historyIDs,
          ];
        } else {
          this.postActionData[id].closeTasksByActivity = historyIDs;
        }
      } else {
        this.postActionData[id] = {
          closeTasksByActivity: historyIDs,
        };
      }
    }
  }

  private async writeDataToFile(id: number, body: string): Promise<string> {
    const relativePath = `output`;
    const filePath = path.join(relativePath, `${id}.csv`);
    const resultPath = path.join(process.cwd(), relativePath);
    if (!(await checkIfExists(resultPath))) {
      await mkDir(resultPath, {
        recursive: true,
      });
    }

    await appendToFile(filePath, body, 'utf8');
    return filePath;
  }

  async handleSaveResult(id: number, pages: number): Promise<void> {
    console.time(`id=${id}`);
    const queryRunner = getManager().connection.createQueryRunner('master');

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let currentProgress = 0;
      const pagePercent = (1 / Number(pages)) * 100;
      await this.emitProgress(id, currentProgress);

      this.resultDetalization[id] = [];
      for (let page = 1; page < Number(pages) + 1; page++) {
        const pageResult = await this.redis.hget(
          REDIS_KEYS.results(this.catalogName, id),
          `page:${page}`,
        );
        if (pageResult) {
          await this.handleSavePageResult(queryRunner, pageResult, id);
        }

        currentProgress += pagePercent;
        await this.emitProgress(id, currentProgress);
      }

      await queryRunner.commitTransaction();

      const detalizationPath = await this.saveDetalizationToFile(id);

      await queryRunner.manager.update(
        UploadHistory,
        { id },
        { statusId: UploadStatus.Finished, resultFilePath: detalizationPath },
      );
      await this.emitCompleted(id, UploadStatus.Finished);
      await this.postActionHandler(id);
      await this.eventRulesHandler(id);
    } catch (error) {
      this.logger.error(error);
      Sentry.captureException(error);
      await queryRunner.rollbackTransaction();
      const errorPath = await this.saveErrorToFile(id, error);
      await queryRunner.manager.update(
        UploadHistory,
        { id },
        { statusId: UploadStatus.Failed, resultFilePath: errorPath },
      );
      await this.failPostActionHandler(id);
      await this.emitCompleted(id, UploadStatus.Failed);
    } finally {
      await queryRunner.release();
      await this.clearOrderCache(id);
      // await this.messageGateway.closeChannel(`import:${this.catalog}`);
      console.timeEnd(`id=${id}`);
    }
  }

  abstract handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void>;

  protected async callRegroupDebtors(id: number) {
    const cases = this.postActionData[id].activateDebtorRegrouping;
    if (cases) {
      const caseServiceURL = this.config.get<string>(caseServiceURLKey);
      await this.httpService
        .post(caseServiceURL + taskManagerURL, {
          strategy: 'regroup-debtors',
          CaseIDs: cases,
          userID: this.postActionData[id].userID,
        })
        .toPromise();
    }
  }

  protected async callAppellateCourtMapping(id: number) {
    const cases = this.postActionData[id]?.activateAppellateCourtMapping;
    if (cases) {
      const caseServiceURL = this.config.get<string>(caseServiceURLKey);
      await this.httpService
        .post(caseServiceURL + taskManagerURL, {
          strategy: 'appellate-court-mapping',
          CaseIDs: cases,
        })
        .toPromise();
    }
  }

  protected async callCloseTasksByDocuments(id: number, userID: number) {
    const documents = this.postActionData[id]?.closeTasksByDocuments;
    if (documents.length > 0) {
      const caseServiceURL = this.config.get<string>(caseServiceURLKey);
      await this.httpService
        .post(caseServiceURL + taskManagerURL, {
          strategy: 'new-document',
          DocumentIDs: documents,
          userID: userID,
        })
        .toPromise();
    }
  }

  protected async callCloseTasksByActivity(id: number, userID: number) {
    const historyIDs = this.postActionData[id]?.closeTasksByActivity ?? [];
    if (historyIDs.length > 0) {
      const caseServiceURL = this.config.get<string>(caseServiceURLKey);
      await this.httpService
        .post(caseServiceURL + taskManagerURL, {
          strategy: 'new-action',
          HistoryIDs: historyIDs,
          userID: userID,
        })
        .toPromise();
    }
  }

  sliceIntoChunks(items: any[], chunkSize: number) {
    const result = [];
    for (let index = 0; index < items.length; index += chunkSize) {
      const chunk = items.slice(index, index + chunkSize);
      result.push(chunk);
    }
    return result;
  }

  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public getChunkSize(): number {
    return 1000;
  }

  async postActionHandler(id: number): Promise<void> {
    // skip
  }

  async eventRulesHandler(id: number): Promise<void> {
    const events = this.eventRules[id] ?? [];

    const uniqueEvents: any[] = [
      ...new Map(events.map((item: any) => [item.CaseID, item])).values(),
    ];

    const parameter = await getManager().query(
      `SELECT "Value" FROM "Dictionary"."ServiceParameter" WHERE "Name" = 'useEventRuleFeature' and "IsDeleted" = 0;`,
    );

    const useEventRuleFeature =
      parameter.length > 0 ? parameter[0].Value : null;

    if (useEventRuleFeature && uniqueEvents.length > 0) {
      const caseLogs = await this.makeCaseLogs(uniqueEvents);

      const uploadHistory = {
        catalogId: 9,
        statusId: 2,
        insertedUserId: uniqueEvents[0].userID,
        inserted: new Date(),
      } as ActionUploadHistory;

      const actionHistory = await getManager().insert(
        ActionUploadHistory,
        uploadHistory,
      );

      const actionHistoryId = actionHistory.identifiers[0].id;

      await getManager().insert(ActionToImportHistory, {
        actionId: actionHistoryId,
        importId: id,
      });

      const initialCall = uuid();

      this.messageGateway.server
        .in(`import:${this.catalog}`)
        .emit('add-post-action', {
          ImportID: id,
          ID: actionHistoryId,
          Catalog: 'Event processing',
          CatalogID: 9,
          Status: 'In progress',
          StatusID: UploadStatus.InProgress,
        });

      const publisher = new CheckEventRulesPublisher(this.connection);
      for (const [index, event] of uniqueEvents.entries()) {
        await publisher.publish({
          ...event,
          ActionUploadHistoryID: actionHistoryId,
          InitialCallID: initialCall,
          TotalItems: uniqueEvents.length,
          IsFirst: index === 0,
          LogID: caseLogs[event.CaseID],
        });
      }
      await publisher.close();
    }
  }

  async failPostActionHandler(id: number): Promise<void> {
    // skip
  }

  async replaceMultiselectParameters(rows: Array<Record<string, any>>) {
    const errors: Array<{ column: string; errorCode: string; row: number }> =
      [];

    if (!this.parameterRepository || !this.dataTypeRepository) {
      throw new Error(
        '[getMultiselectParameters] Parameter repository not found',
      );
    }

    const parameterType = await this.dataTypeRepository.findOne({
      where: { name: 'multi-select' },
    });

    if (!parameterType) {
      throw new Error('[getMultiselectParameters] Data type not found');
    }

    const parameterSlugs = Object.keys(rows[0]);
    const multiselectParameters = await this.parameterRepository.find({
      where: {
        slug: In(parameterSlugs),
        isDeleted: 0,
        dataTypeId: parameterType.id,
      },
    });

    const multiselectSlugs = multiselectParameters.map((item) => item.slug);

    const dictionaries = await this.parameterRepository.getSlugDictionary(
      multiselectSlugs,
    );

    for (const row of rows) {
      for (const [key, value] of Object.entries(row)) {
        if (multiselectSlugs.includes(key)) {
          const dictionary = dictionaries[key];
          const clearValues = value
            .split(',')
            .map((item: string) => item.trim());

          row[key] = clearValues.map((item: string) => {
            if (item === 'null') {
              return item;
            }

            if (!dictionary[item]) {
              errors.push({
                column: key,
                errorCode: `Dictionary value not found: ${item}`,
                row: rows.indexOf(row) + 1,
              });
            }
            return dictionary[item];
          });
        } else {
          row[key] = value;
        }
      }
    }

    return { errors, changedRecords: rows, multiselectSlugs };
  }

  protected activateLegalRules(
    cases: number[],
    caseChanges: { [key: number]: any },
    id: number,
    userId: number,
  ) {
    for (const caseID of cases) {
      this.eventRules[id] = this.eventRules[id] ?? [];
      this.eventRules[id].push({
        CaseID: caseID,
        userID: userId,
        ToolSourceID: ToolSourceEnum.ImportFile,
        CaseChanges: caseChanges[caseID],
      });
    }
  }

  protected async makeCaseLogs(
    events: any[],
  ): Promise<{ [key: number]: number }> {
    const caseToLog: { [key: number]: number } = {};

    if (this.eventRuleLogRepository) {
      const logs: EventRuleLog[] = [];
      const pool = await this.eventRuleLogRepository.getPoolIds(events.length);
      for (const [index, event] of events.entries()) {
        caseToLog[Number(event.CaseID)] = pool[index];
        const item = this.eventRuleLogRepository.create({
          ID: pool[index],
          LevelID: 4,
          LevelValue: Number(event.CaseID),
          StatusID: 1,
          ToolSourceID: this.getToolSource(),
          ConditionValue: event.CaseChanges,
          InsertedUserID: event.userID,
          UpdatedUserID: event.userID,
        });
        logs.push(item);
      }

      if (logs.length > 0) {
        const chunks = splitOnChunks(logs, 1000);
        for (const chunk of chunks) {
          await this.eventRuleLogRepository.save(chunk);
        }
      }
    }

    return caseToLog;
  }

  protected getToolSource() {
    return ToolSourceEnum.ImportFile;
  }
}
