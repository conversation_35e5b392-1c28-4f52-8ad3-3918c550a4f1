import { ImportActivityParameterService } from '../../import-activity-parameter/import-activity-parameter.service';
import { ImportBasisComplaintService } from '../../import-basis-complaint/import-basis-complaint.service';
import { ImportBasisCpiioService } from '../../import-basis-cpiio/import-basis-cpiio.service';
import { ImportBasisEmployerService } from '../../import-basis-employer/import-basis-employer.service';
import { ImportBasisFinaService } from '../../import-basis-fina/import-basis-fina.service';
import { ImportCaseDiscountService } from '../../import-case-discount/import-case-discount.service';
import { ImportCaseParameterService } from '../../import-case-parameter/import-case-parameter.service';
import { ImportCaseStateService } from '../../import-case-state/import-case-state.service';
import { ImportContractDataService } from '../../import-contract-data/import-contract-data.service';
import { ImportCourtParameterService } from '../../import-court-parameter/import-court-parameter.service';
import { ImportDeleteInteractionService } from '../../import-delete-interaction/import-delete-interaction.service';
import { ImportDeleteLegalCaseService } from '../../import-delete-legal-case/import-delete-legal-case.service';
import { ImportDiscountService } from '../../import-discount/import-discount.service';
import { ImportExtraInfoService } from '../../import-extra-info/import-extra-info.service';
import { ImportLegalCaseService } from '../../import-legal-case/import-legal-case.service';
import { ImportSmsActivityService } from '../../import-sms-activity/import-sms-activity.service';
import { ImportTagService } from '../../import-tag/import-tag.service';
import { ImportTransferToCollectionAgencyService } from '../../import-transfer-to-collection-agency/import-transfer-to-collection-agency.service';
import { ImportLegalAppellateCourtService } from '../../import-legal-appellate-court/import-legal-appellate-court.service';
import { ImportLegalCourtService } from '../../import-legal-court/import-legal-court.service';
import { ImportLegalDocumentService } from '../../import-legal-document/import-legal-document.service';
import { UpdateLegalDocumentService } from '../../update-legal-document/update-legal-document.service';
import { ImportPhoneService } from 'src/import-phone/import-phone.service';

export default {
  ExtraInfo: ImportExtraInfoService,
  ContractData: ImportContractDataService,
  LegalDocument: ImportLegalDocumentService,
  UpdateLegalDocument: UpdateLegalDocumentService,
  CourtParameter: ImportCourtParameterService,
  LegalCourt: ImportLegalCourtService,
  LegalAppellateCourt: ImportLegalAppellateCourtService,
  BasisFina: ImportBasisFinaService,
  BasisCpiio: ImportBasisCpiioService,
  BasisEmployer: ImportBasisEmployerService,
  BasisComplaint: ImportBasisComplaintService,
  TransferToCollectionAgency: ImportTransferToCollectionAgencyService,
  ActivityParameter: ImportActivityParameterService,
  Discount: ImportDiscountService,
  CaseDiscount: ImportCaseDiscountService,
  Tag: ImportTagService,
  CaseParameter: ImportCaseParameterService,
  LegalCase: ImportLegalCaseService,
  Phone: ImportPhoneService,
  DeleteLegalCase: ImportDeleteLegalCaseService,
  DeleteInteraction: ImportDeleteInteractionService,
  CaseState: ImportCaseStateService,
  SmsActivity: ImportSmsActivityService,
};
