import { HttpService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { plainToClass } from 'class-transformer';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { CatalogNameEnum } from '../../entities/enum/catalog-name.enum';
import { EventsGateway } from '../../events/events.gateway';
import { ImportPublisher } from '../../events/publishers/import-publisher';
import { BaseImportEvent } from '../../events/types/base-import-event.interface';
import { ValidatorDataAccess } from '../../import/data-access/validator.data-access';
import { CaseRepository } from '../../repositories/data/case.repository';
import { CatalogColumnRepository } from '../../repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from '../../repositories/import/upload-history.repository';
import { InvoiceRepository } from '../../repositories/legal-ua/invoice.repository';
import { EventRuleLogRepository } from '../../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../../repositories/service-parameter.repository';
import { ApiClient } from '../api.client';
import { HttpMethod } from '../enums/http-method.enum';
import { ValidationResult } from '../validation/validation-result.type';
import { BaseImportInterface } from './base-import.interface';
import { BaseImportService } from './base-import.service';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';
import { ParameterRepository } from 'src/repositories/dictionary/parameter.repository';
import { In } from 'typeorm';

export abstract class BaseImportWithParameterService<T, V = undefined>
  extends BaseImportService<T, V>
  implements BaseImportInterface
{
  protected chunkSize: number;
  protected resultDetalization: { [key: number]: any[] } = {};
  protected postActionData: { [key: number]: any } = {};
  protected eventRules: { [key: number]: any[] } = {};

  protected useRowsCantBeClearedFeature = false;

  protected abstract catalogName: CatalogNameEnum;
  protected abstract publisher: ImportPublisher<BaseImportEvent>;

  protected constructor(
    protected readonly connection: AmqpConnectionManager,
    protected readonly config: ConfigService,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly dataTypeRepository: DataTypeRepository,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly eventRuleLogRepository: EventRuleLogRepository,
    protected readonly caseRepository: CaseRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
      parameterRepository,
      dataTypeRepository,
      eventRuleLogRepository,
    );
    this.chunkSize = this.config.get('chunk.importDefault', 100_000);
  }

  protected getValidateBySlugKey(): string {
    return 'TypeID';
  }

  protected formatRow(lines: string[], type: any): any[] {
    const array: any[] = [];
    for (const line of lines) {
      array.push(JSON.parse(line));
    }

    return plainToClass(type, array, {
      excludeExtraneousValues: false,
      enableImplicitConversion: true,
    });
  }

  protected async validateData(changedRecords: any, additionalParams: any) {
    const fields = Object.keys(changedRecords[0]);
    const caseIDs = changedRecords.map((x: any) => x.LegalCaseID);

    const legalCaseErrors = [];
    for (const [index, caseID] of caseIDs.entries()) {
      if (!caseID) {
        legalCaseErrors.push({
          column: 'LegalCaseID',
          errorCode: 'IsNotLegalCase',
          row: index + 2,
        });
      }
    }

    if (legalCaseErrors.length > 0) {
      return {
        success: false,
        errors: legalCaseErrors,
      } as ValidationResult;
    }

    const { errors, multiselectSlugs } =
      await this.replaceMultiselectParameters(changedRecords);

    if (errors.length > 0) {
      return {
        success: false,
        errors,
      } as ValidationResult;
    }

    let rowsCantBeCleared: boolean[] = [];
    if (this.useRowsCantBeClearedFeature) {
      const claimStatusId =
        await this.serviceParameterRepository.getGlobalParameterValueByName<number>(
          'ClaimCaseStatusID',
        );
      const cases = new Set(
        (
          await this.caseRepository.find({
            select: ['id'],
            where: {
              id: In(caseIDs),
              statusId: claimStatusId ?? 51,
            },
          })
        ).map((c) => c.id),
      );

      rowsCantBeCleared = changedRecords.map((x: any) =>
        cases.has(String(x.LegalCaseID)),
      );
    }

    for (const field of fields) {
      let errors = [];

      switch (field) {
        case 'LegalCaseID':
          errors = await this.validateLegalCaseID(
            changedRecords.map((x: any) => x[field]),
            field,
          );
          break;
        case 'DocumentID':
          errors = await this.validateDocumentID(
            changedRecords.map((x: any) => x[field]),
            field,
          );
          break;
        default:
          errors = await this.validateBySlug(
            changedRecords.map((x: any) => x[field]),
            field,
            additionalParams && additionalParams['typeId']
              ? Number(additionalParams['typeId'])
              : null,
            !multiselectSlugs.includes(field),
            rowsCantBeCleared,
          );
          break;
      }

      if (errors.length > 0) {
        return {
          success: false,
          errors,
        } as ValidationResult;
      }
    }

    return { success: true } as ValidationResult;
  }

  protected async validateLegalCaseID(
    values: number[],
    field: string,
  ): Promise<any[]> {
    const duplicates: { [key: number]: boolean } = {};
    const legalCases = await this.invoiceRepository
      .createQueryBuilder('invoice')
      .select('invoice.caseId', 'caseId')
      .where('invoice.caseId IN (:...values)', { values })
      .andWhere('invoice.isDeleted = :isDeleted', { isDeleted: 0 })
      .groupBy('invoice.caseId, invoice.id')
      .getRawMany();

    const mapping: any = {};
    for (const legalCase of legalCases) {
      mapping[legalCase.caseId] = 1;
    }

    const errors = [];

    for (const [index, value] of values.entries()) {
      if (duplicates[value] ?? false) {
        errors.push({
          column: field,
          errorCode: 'DuplicateLegalCase',
          row: index + 2,
        });
      } else {
        duplicates[value] = true;
      }

      if (!mapping[value]) {
        errors.push({
          column: field,
          errorCode: 'IsNotLegalCase',
          row: index + 2,
        });
      }
    }

    return errors;
  }

  protected async validateCaseID(
    values: number[],
    field: string,
  ): Promise<any[]> {
    const duplicates: { [key: number]: boolean } = {};
    const cases = await this.caseRepository
      .createQueryBuilder('case')
      .select('case.id')
      .where('case.id IN (:...values)', { values })
      .andWhere('case.isDeleted = :isDeleted', { isDeleted: 0 })
      .getRawMany();

    const mapping: any = {};
    for (const caseModel of cases) {
      mapping[caseModel.case_ID] = 1;
    }

    const errors = [];

    for (const [index, value] of values.entries()) {
      if (duplicates[value] ?? false) {
        errors.push({
          column: field,
          errorCode: 'DuplicateCase',
          row: index + 2,
        });
      } else {
        duplicates[value] = true;
      }

      if (!mapping[value]) {
        errors.push({
          column: field,
          errorCode: 'CaseNotExistsError',
          row: index + 2,
        });
      }
    }

    return errors;
  }

  protected async validateBySlug(
    values: number[],
    slug: string,
    typeId: number | null,
    validateByName = true,
    rowsCantBeCleared: boolean[] = [],
  ): Promise<any[]> {
    try {
      const key = this.getValidateBySlugKey();
      const errors = [];

      const caseServiceURL = this.config.get<string>('caseService.url');
      let url = caseServiceURL + '/parameter-validation/' + slug;

      if (typeId) {
        url += `?${key}=` + typeId;
      }

      const externalErrors: any = await this.apiClient.sendRequest(
        HttpMethod.PUT,
        url,
        {
          timeout: 15_000,
          body: { values: values, nullable: true, config: { validateByName } },
        },
      );

      for (const [index, value] of externalErrors[slug].entries()) {
        if (value) {
          errors.push({
            column: slug,
            errorCode: value,
            row: index + 2,
          });
        } else {
          if (
            this.useRowsCantBeClearedFeature &&
            String(values[index]).toLowerCase() === 'null' &&
            rowsCantBeCleared[index]
          ) {
            errors.push({
              column: slug,
              errorCode: 'CanNotDeleteWithClaimStatus',
              row: index + 2,
            });
          }
        }
      }
      return errors;
    } catch {
      return [
        {
          column: slug,
          errorCode: 'ValidationFailed',
          row: 1,
        },
      ];
    }
  }

  protected async validateDocumentID(
    values: number[],
    field: string,
  ): Promise<any[]> {
    const errors = [];

    for (const [index, value] of values.entries()) {
      if (!value) {
        errors.push({
          column: field,
          errorCode: 'DocumentIDEmptyError',
          row: index + 2,
        });
      }
    }

    return errors;
  }

  async postActionHandler(id: number): Promise<void> {
    await this.callAppellateCourtMapping(id);
    await this.callCloseTasksByActivity(id, this.postActionData[id]?.userId);
  }
}
