import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { getManager, In } from 'typeorm';
import { CatalogNameEnum as CatalogName } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum as ImportCatalog } from '../../../entities/enum/catalog.enum';
import { LegalCaseParameterPreImportDataDTO } from '../../../events/listeners/case-parameter-listener/dto/legal-case-parameter-pre-import-data.dto';
import { BaseImportEvent } from '../../../events/types/base-import-event.interface';
import { EventsGateway } from '../../../events/events.gateway';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { UploadHistoryRepository } from '../../../repositories/import/upload-history.repository';
import { BaseImportListenerService } from './base-import-listener.service';
import { CaseParameterPreImportDataDTO } from 'src/events/listeners/case-parameter-listener/dto/case-parameter-pre-import-data.dto';
import { History } from 'src/entities/activity/history.entity';
import { HistoryParameter } from 'src/entities/activity/history-parameter.entity';

export abstract class BaseImportWithParameterListenerService<
  T extends BaseImportEvent,
  K = undefined,
> extends BaseImportListenerService<T, K> {
  abstract catalog: CatalogName;
  abstract catalogId: ImportCatalog;

  constructor(
    protected readonly connection: AmqpConnectionManager,
    protected readonly messageGateway: EventsGateway,
    protected readonly redis: Redis,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly parameterRepository: ParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
  }

  async getParameterBySlug() {
    const parameters = await this.parameterRepository.find({
      where: {
        isDeleted: 0,
      },
    });

    const map: { [key: string]: number } = {};
    for (const parameter of parameters) {
      map[parameter.slug] = parameter.id;
    }

    return map;
  }

  protected hasAppellateCourtMapping(
    value: any,
    parameterId: number,
    list: Set<number>,
  ) {
    return [1, '1', 'yes', 'true'].includes(value) && list.has(parameterId);
  }

  protected hasParameter(value: any, parameterId: number, list: Set<number>) {
    return value && list.has(parameterId);
  }

  protected formatSlugValue(
    row: any,
    slugs: string[],
    dataTypeMap: { [key: string]: string },
    slugDictionary: { [key: string]: { [key: string]: number } },
    userId: number,
    uploadHistoryId: string,
  ): any {
    const skipValues = new Set(['null', '']);
    for (const slug of slugs) {
      if (skipValues.has(String(row[slug]).toLowerCase())) {
        continue;
      }

      if (row[slug] && dataTypeMap[slug] === 'numeric-range') {
        row[slug] = String(row[slug]).replace(',', '.');
        row[slug] = Number(row[slug]);
      }

      if (row[slug] && dataTypeMap[slug] === 'integer') {
        row[slug] = Number(row[slug]);
      }

      if (row[slug] && dataTypeMap[slug] === 'date') {
        const parsedDate = parse(String(row[slug]), 'dd.MM.yyyy', new Date());
        try {
          row[slug] = format(parsedDate, 'yyyy-MM-dd');
        } catch (error) {
          console.log(
            'parsedDate',
            parsedDate,
            'slug',
            slug,
            'value',
            row[slug],
            'user',
            userId,
            'id',
            uploadHistoryId,
          );
          throw error;
        }
      }

      if (row[slug] && dataTypeMap[slug] === 'select') {
        row[slug] = slugDictionary[slug][row[slug]];
      }

      if (
        row[slug] &&
        (dataTypeMap[slug] === 'checkbox-select' ||
          dataTypeMap[slug] === 'boolean')
      ) {
        const booleanPositiveAvailableValues = new Set(['1', 'true', 'yes']);
        const booleanNegativeAvailableValues = new Set(['0', 'false', 'no']);

        if (
          booleanPositiveAvailableValues.has(String(row[slug]).toLowerCase())
        ) {
          row[slug] = true;
        }

        if (
          booleanNegativeAvailableValues.has(String(row[slug]).toLowerCase())
        ) {
          row[slug] = false;
        }
      }
    }

    return row;
  }

  protected async getDataTypeMap(
    slugs: string[],
  ): Promise<{ [key: string]: string }> {
    const dataType = await this.parameterRepository.find({
      where: {
        slug: In(slugs),
        isDeleted: 0,
      },
      relations: ['dataType'],
    });

    const dataTypeMap: { [key: string]: string } = {};
    for (const parameter of dataType) {
      dataTypeMap[parameter.slug] = parameter.dataType.name;
    }

    return dataTypeMap;
  }

  protected getSlugs(row: any): string[] {
    return Object.keys(row).filter((c) => c !== 'LegalCaseID');
  }

  protected async formatParameters(
    importData: any,
    userId: number,
    uploadHistoryId: string,
  ): Promise<any> {
    const slugs = this.getSlugs(importData[0]);

    const dataTypeMap = await this.getDataTypeMap(slugs);
    const slugDictionary = await this.parameterRepository.getSlugDictionary(
      slugs,
    );

    for (const [index, row] of importData.entries()) {
      importData[index] = this.formatSlugValue(
        row,
        slugs,
        dataTypeMap,
        slugDictionary,
        userId,
        uploadHistoryId,
      );
    }

    return importData;
  }

  protected async createLegalContactInHistoryForParameters(
    importData: LegalCaseParameterPreImportDataDTO[],
    userId: number,
  ) {
    return await this.createContactInHistory(importData, userId, 'LegalCaseID');
  }

  protected async createContactInHistoryForParameters(
    importData: CaseParameterPreImportDataDTO[],
    userId: number,
  ) {
    return await this.createContactInHistory(importData, userId, 'CaseID');
  }

  protected async createContactInHistory(
    importData: any[],
    userId: number,
    caseField: string,
  ) {
    const parameterSlugs = Object.keys(importData[0]);

    if (parameterSlugs.length === 0) {
      return;
    }

    const parametersToUpdate = await this.parameterRepository
      .createQueryBuilder('p')
      .where('p.slug IN (:...slugs)', {
        slugs: parameterSlugs,
      })
      .andWhere(`"AdditionalData" ->> 'ShouldCreateContactInHistory' = '1'`)
      .andWhere('p.isDeleted = 0')
      .getMany();

    if (parametersToUpdate.length === 0) {
      return;
    }

    const queryRunner = getManager().connection.createQueryRunner('master');
    await queryRunner.connect();

    const nextvalsCount = importData.length * parametersToUpdate.length;
    // Prepare and reserve ids in db to insert bulk
    const historyNextvals = (await queryRunner.query(
      `SELECT nextval('"Activity"."History_ID_seq"') FROM generate_series(1, ${nextvalsCount});`,
    )) as Array<{ nextval: string }>;

    const preparedHistoryIds = historyNextvals.map(
      (row: { nextval: string }) => row.nextval,
    );

    const parameterChangeHistoryTypeValue =
      await this.parameters.getGlobalParameterValueByName(
        'parameterChangeHistoryType',
      );

    const historyParameters = [];
    const histories = [];

    for (const data of importData) {
      for (const parameter of parametersToUpdate) {
        const historyId = preparedHistoryIds.shift();
        histories.push({
          id: historyId,
          caseId: data['LegalCaseID'],
          typeId: parameterChangeHistoryTypeValue as number,
          creationUserID: userId,
        });

        historyParameters.push({
          historyId: historyId,
          parameterId: parameter.id,
          value: data[parameter.slug] as any,
        });
      }
    }

    try {
      await queryRunner.startTransaction();

      await queryRunner.manager.insert(History, histories);
      await queryRunner.manager.insert(HistoryParameter, historyParameters);

      await queryRunner.commitTransaction();
    } catch {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }
}
