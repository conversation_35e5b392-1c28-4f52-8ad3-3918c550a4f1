import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum as CatalogName } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum as ImportCatalog } from '../../../entities/enum/catalog.enum';
import { ImportListener } from '../../../events/listeners/import-listener';
import { BaseImportEvent } from '../../../events/types/base-import-event.interface';
import { EventsGateway } from '../../../events/events.gateway';
import { UploadStatus } from '../../../import/upload-status.enum';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { UploadHistoryRepository } from '../../../repositories/import/upload-history.repository';

export abstract class BaseImportListenerService<
  T extends BaseImportEvent,
  K = undefined,
> extends ImportListener<T, K> {
  abstract catalog: CatalogName;
  abstract catalogId: ImportCatalog;

  constructor(
    protected readonly connection: AmqpConnectionManager,
    protected readonly messageGateway: EventsGateway,
    protected readonly redis: Redis,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
  ) {
    super(connection);
  }

  protected async emitProgress(id: number, progress: number): Promise<void> {
    const progressKey = REDIS_KEYS.progress(this.catalog, id);
    const channel = `import:${this.catalogId}`;

    await this.redis.incrbyfloat(progressKey, progress);
    const updatedProgress = +((await this.redis.get(progressKey)) || 0);

    const payload = {
      CatalogID: this.catalogId,
      UploadHistoryID: id,
      StatusID: UploadStatus.InProgress,
      Progress:
        Math.ceil(updatedProgress) > 100 ? 100 : Math.ceil(updatedProgress),
    };

    this.messageGateway.server.in(channel).emit('progress', payload);
  }

  protected async markAsCompleted(id: number, page: number): Promise<void> {
    await this.redis.hset(
      REDIS_KEYS.results(this.catalog, id),
      `status:${page}`,
      1,
    );
  }

  protected async saveResult(id: number, page: number, result: any) {
    await this.redis.hset(
      REDIS_KEYS.results(this.catalog, id),
      `page:${page}`,
      JSON.stringify(result),
    );
  }

  protected sliceIntoChunks(items: any[], chunkSize: number) {
    const result = [];
    for (let index = 0; index < items.length; index += chunkSize) {
      const chunk = items.slice(index, index + chunkSize);
      result.push(chunk);
    }
    return result;
  }
}
