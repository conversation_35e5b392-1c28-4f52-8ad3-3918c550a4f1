import { Transform } from 'class-transformer';
import {
  IsNumber,
  IsNumberString,
  IsOptional,
  Validate,
  ValidateNested,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

import { LegalCaseDto } from './legal-case.dto';

function transformNumberString(value: any): any {
  if (IsNumberString(value)) {
    return Number(value);
  }
  return value;
}

@ValidatorConstraint({
  name: 'packageIdRequiredWhenSinglePackage',
  async: false,
})
class PackageIdConstraint implements ValidatorConstraintInterface {
  validate(packageID: number, args: ValidationArguments) {
    const object = args.object as ImportLegalCaseDto;
    if (object.multiPackage === undefined) {
      return true;
    }

    return object.multiPackage === 0
      ? typeof packageID === 'number' && !Number.isNaN(packageID)
      : packageID === undefined || packageID === null;
  }

  defaultMessage(args: ValidationArguments) {
    const object = args.object as ImportLegalCaseDto;
    return object.multiPackage === 0
      ? 'PackageID is required when multiPackage is 0'
      : 'PackageID must be empty when multiPackage is enabled';
  }
}

export class ImportLegalCaseDto {
  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  userID: number;

  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  @IsOptional()
  multiPackage?: number;

  @Transform(({ value }) => transformNumberString(value))
  @Validate(PackageIdConstraint)
  PackageID?: number;

  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  @IsOptional()
  actionID?: number;

  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  @IsOptional()
  stageID?: number;

  @ValidateNested({ each: true })
  importData: LegalCaseDto[];

  importFile: string;
}
