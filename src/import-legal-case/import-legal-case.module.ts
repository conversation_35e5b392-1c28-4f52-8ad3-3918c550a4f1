import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseStageChangeLogRepository } from '../repositories/data/case-stage-change-log.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { ContactPersonRelationRepository } from '../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportLegalCaseController } from './import-legal-case.controller';
import { ImportLegalCaseService } from './import-legal-case.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      DebtorRepository,
      CaseRepository,
      ContactPersonRepository,
      ContactPersonRelationRepository,
      LegalInvoiceRepository,
      CourtProcessRepository,
      CaseStageChangeLogRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportLegalCaseController],
  providers: [
    ImportLegalCaseService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportLegalCaseModule {}
