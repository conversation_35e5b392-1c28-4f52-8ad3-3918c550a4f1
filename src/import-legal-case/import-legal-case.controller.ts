import {
  Body,
  Controller,
  Get,
  UnprocessableEntityException,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportLegalCaseService } from './import-legal-case.service';
import { LegalCaseDto } from './dto/legal-case.dto';
import { ImportLegalCaseDto } from './dto/import-legal-case.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-legal-case')
export class ImportLegalCaseController {
  constructor(private importLegalCaseService: ImportLegalCaseService) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; multiPackage?: number },
  ): Promise<any> {
    const readStream = await this.importLegalCaseService.getGeneratedCsv(
      query.id,
      query.multiPackage,
    );

    response.set(
      disposition,
      'attachment; filename=ImportLegalCase - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file')
  async import(@Body() importLegalCaseDto: ImportLegalCaseDto) {
    const { importFile, userID, PackageID, actionID, multiPackage } =
      importLegalCaseDto;

    const errors = await this.importLegalCaseService.validateRequest(
      importLegalCaseDto,
    );
    if (errors.length > 0) {
      throw new UnprocessableEntityException(errors);
    }

    return await this.importLegalCaseService.importLargeFile(
      importFile,
      userID,
      LegalCaseDto,
      { actionID, PackageID, multiPackage },
    );
  }

  @Post('from-request')
  // eslint-disable-next-line radar/no-identical-functions
  importFromRequest(@Body() importLegalCaseDto: ImportLegalCaseDto) {
    const { importData, userID, PackageID, actionID } = importLegalCaseDto;
    return this.importLegalCaseService.import(importData, userID, {
      actionID,
      PackageID: Number(PackageID),
    });
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importLegalCaseService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
