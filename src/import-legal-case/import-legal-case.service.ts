import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { getManager, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { ErrorItem } from '../common/validation/interfaces';
import { ValidationResult } from '../common/validation/validation-result.type';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportLegalCasePublisher } from '../events/publishers/import-legal-case-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseStageChangeLogRepository } from '../repositories/data/case-stage-change-log.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { ContactPersonRelationRepository } from '../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportLegalCaseDto } from './dto/import-legal-case.dto';
import { LegalCaseDto } from './dto/legal-case.dto';

@Injectable()
export class ImportLegalCaseService extends BaseImportService<LegalCaseDto> {
  catalog: CatalogEnum = CatalogEnum.LegalCase;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.LegalCase;
  protected publisher: ImportPublisher<any> = new ImportLegalCasePublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly httpService: HttpService,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly debtorRepository: DebtorRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly contactPersonRepository: ContactPersonRepository,
    protected readonly contactPersonRelationRepository: ContactPersonRelationRepository,
    protected readonly legalInvoiceRepository: LegalInvoiceRepository,
    protected readonly courtProcessRepository: CourtProcessRepository,
    protected readonly caseStageChangeLogRepository: CaseStageChangeLogRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  protected async callCustomServiceValidation(
    changedRecords: LegalCaseDto[],
    additionalParams: {
      actionID?: number;
      PackageID?: number;
      multiPackage: number;
    },
  ): Promise<ValidationResult> {
    const errors: ErrorItem[] = [];

    if ((await this.useMultiPackageImport()) && additionalParams.multiPackage) {
      const packageIDs = changedRecords
        .map((index) => Number(index.PackageID))
        .filter((index) => index);

      const legalPackages = new Set<number>(
        packageIDs.length > 0
          ? (
              await this.caseRepository.query(
                `SELECT "ID" FROM "Data"."Package" WHERE "ID" IN (${packageIDs.join(
                  ',',
                )}) AND "IsDeleted" = 0 AND "IsLegal" = 1`,
              )
            ).map((row: any) => row.ID)
          : [],
      );

      for (const [index, changedRecord] of changedRecords.entries()) {
        if (!changedRecord.PackageID) {
          errors.push({
            column: 'PackageID',
            errorCode: 'Required',
            errorParams: null,
            row: index,
          });
        } else {
          if (!legalPackages.has(changedRecord.PackageID)) {
            errors.push({
              column: 'PackageID',
              errorCode: 'PackageIsNotLegal',
              errorParams: null,
              row: index,
            });
          }
        }
      }
    }

    if (
      (await this.useMultiPackageImport()) &&
      !additionalParams.multiPackage
    ) {
      for (const [index, changedRecord] of changedRecords.entries()) {
        if (changedRecord.PackageID) {
          errors.push({
            column: 'PackageID',
            errorCode: 'UnsupportedColumn',
            errorParams: null,
            row: index,
          });
        }
      }
    }

    return {
      success: errors.length <= 0,
      errors: errors,
    };
  }

  protected getPageSize(): number {
    return 1000;
  }

  protected async processRows(
    rl: any,
    batch: any[],
    pageSize: number,
    lastValidationResult: ValidationResult,
    callback: any,
  ) {
    const fileData: { CaseID: number; GroupID: number; InvoiceID: number }[] =
      [];

    for await (const line of rl) {
      const lineData = JSON.parse(line);
      fileData.push(lineData);
    }

    const id = Math.ceil(Math.random() * 1000);
    console.time('sort file: ' + id);
    fileData.sort((a, b) => a.CaseID - b.CaseID);
    console.timeEnd('sort file: ' + id);

    for (const lineData of fileData) {
      const previousData =
        batch.length > 0 ? JSON.parse(batch[batch.length - 1]) : null;

      if (previousData && previousData['GroupID'] === lineData['GroupID']) {
        batch.push(JSON.stringify(lineData));
        continue;
      }

      if (batch.length >= pageSize && lastValidationResult.success) {
        await callback();
        batch.length = 0;
      }
      batch.push(JSON.stringify(lineData));
    }
  }

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);

    const newDebtors = result.newDebtors;
    const newCases = result.newCases;
    const newContactPersons = result.newContactPersons;
    const newContactPersonRelations = result.newContactPersonRelations;
    const newLegalInvoice = result.newLegalInvoice;
    const newCourtProcess = result.newCourtProcess;

    this.savePostActionData(id, result);

    if (newDebtors.length > 0) {
      const models = newDebtors.map((item: any) =>
        this.debtorRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (newCases.length > 0) {
      const models = newCases.map((item: any) =>
        this.caseRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }

      const useCaseStageChangeLog =
        await this.serviceParameterRepository.findOne({
          where: {
            name: 'useCaseStageChangeLog',
            isDeleted: 0,
          },
        });

      if (useCaseStageChangeLog?.value) {
        const now = new Date();
        const stageLogs = newCases.map((item: any) =>
          this.caseStageChangeLogRepository.create({
            caseId: item.id,
            stageId: item.stageId,
            fromDate: now,
            toDate: new Date(2100, 0, 1),
            insertedUserId: result.metaData.userId,
            updatedUserId: result.metaData.userId,
          }),
        );

        const chunks = this.sliceIntoChunks(stageLogs, 500);
        for (const chunk of chunks) {
          await queryRunner.manager.save(chunk);
        }
      }
    }

    if (newContactPersons.length > 0) {
      const models = newContactPersons.map((item: any) =>
        this.contactPersonRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (newContactPersonRelations.length > 0) {
      const models = newContactPersonRelations.map((item: any) =>
        this.contactPersonRelationRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (newLegalInvoice.length > 0) {
      const models = newLegalInvoice.map((item: any) =>
        this.legalInvoiceRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (newCourtProcess.length > 0) {
      const models = newCourtProcess.map((item: any) =>
        this.courtProcessRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = result.mappingResults;
    } else {
      this.resultDetalization[id].push(...result.mappingResults);
    }
  }

  protected savePostActionData(id: number, result: any) {
    const metaData = result?.metaData;
    if (metaData) {
      if (this.postActionData[id]) {
        if (!this.postActionData[id].userId) {
          this.postActionData[id].userId = metaData.userId;
        }
      } else {
        this.postActionData[id] = {
          userId: metaData.userId,
        };
      }
    }

    if (metaData && metaData.actionID) {
      if (this.postActionData[id]) {
        if (!this.postActionData[id].actionID) {
          this.postActionData[id].actionID = metaData.actionID;
        }
      } else {
        this.postActionData[id] = {
          actionID: metaData.actionID,
        };
      }
    }

    const legalCases = result?.legalCases;
    if (legalCases) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].legalCases) {
          this.postActionData[id].legalCases = [
            ...this.postActionData[id].legalCases,
            ...legalCases,
          ];
        } else {
          this.postActionData[id].legalCases = legalCases;
        }
      } else {
        this.postActionData[id] = {
          legalCases: legalCases,
        };
      }
    }

    const preLegalCases = result?.preLegalCases;
    if (preLegalCases) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].preLegalCases) {
          this.postActionData[id].preLegalCases = [
            ...this.postActionData[id].preLegalCases,
            ...preLegalCases,
          ];
        } else {
          this.postActionData[id].preLegalCases = preLegalCases;
        }
      } else {
        this.postActionData[id] = {
          preLegalCases: preLegalCases,
        };
      }
    }

    const mappingResults = result?.mappingResults;
    if (mappingResults) {
      if (this.postActionData[id]) {
        if (this.postActionData[id].results) {
          this.postActionData[id].results = [
            ...this.postActionData[id].results,
            ...mappingResults,
          ];
        } else {
          this.postActionData[id].results = mappingResults;
        }
      } else {
        this.postActionData[id] = {
          results: mappingResults,
        };
      }
    }
  }

  async postActionHandler(id: number) {
    await this.legalInvoiceRepository.changeStageAndStatuses(
      this.serviceParameterRepository,
      this.connection,
      this.postActionData[id].legalCases,
      this.postActionData[id].preLegalCases,
    );

    await this.createLegalTask(
      this.postActionData[id].results,
      this.postActionData[id].userId,
    );

    if (this.postActionData[id].actionID) {
      await this.updateActionHistory(this.postActionData[id].actionID, 2);
    }
  }

  async failPostActionHandler(id: number) {
    if (this.postActionData[id].actionID) {
      await this.updateActionHistory(this.postActionData[id].actionID, 4);
    }
  }

  protected async updateActionHistory(
    actionID: number,
    statusID: number,
  ): Promise<void> {
    await getManager().query(
      'UPDATE "Action"."UploadHistory" set "StatusID" = $1 where "ID" = $2;',
      [statusID, actionID],
    );
  }

  async createLegalTask(report: any[], userId: number): Promise<void> {
    try {
      if (report.length > 0) {
        const caseServiceURL = this.config.get<string>('caseService.url');
        await this.httpService
          .post(caseServiceURL + '/legal-task-manager', {
            strategy: 'legal-case',
            CaseIDs: report.map((index) => index.LegalCaseID),
            userId,
          })
          .toPromise();
      }
    } catch (error: any) {
      console.log(error);
    }
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }

  private async useMultiPackageImport(): Promise<boolean> {
    const parameter = await this.serviceParameterRepository.findOne({
      where: {
        name: 'useMultiPackageImport',
        isDeleted: 0,
      },
    });

    if (parameter && parameter.value) {
      return true;
    }

    return false;
  }

  public async validateRequest(
    importLegalCaseDto: ImportLegalCaseDto,
  ): Promise<any[]> {
    const errors = [];
    const useMultiPackageImport = await this.useMultiPackageImport();
    if (useMultiPackageImport) {
      if (importLegalCaseDto.multiPackage === undefined) {
        errors.push('multiPackage can not be empty');
      }

      if (
        importLegalCaseDto.multiPackage === 1 &&
        importLegalCaseDto.PackageID
      ) {
        errors.push('PackageID should be empty when multiPackage enabled');
      }

      if (
        importLegalCaseDto.multiPackage === 0 &&
        !importLegalCaseDto.PackageID
      ) {
        errors.push('PackageID should be selected when multiPackage disabled');
      }
    } else {
      if (!importLegalCaseDto.PackageID) {
        errors.push('PackageID can not be empty');
      }
    }
    return errors;
  }
}
