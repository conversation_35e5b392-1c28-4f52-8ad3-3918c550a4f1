import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';
export class MaskedPhoneDTO {
  @Expose()
  @IsNumber()
  public MaskedPhoneID: number;

  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  public MaskedNumber: string;

  @Expose()
  @IsNumber()
  public IsActive: number;

  @Expose()
  public MaskedPhoneNote: string;

  @Expose()
  public MaskedPartyType: string;
}
