import { Inject, Injectable } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { Import } from 'src/import/import.service';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { MaskedPhonePublisher } from 'src/events/publishers/masked-phone-publisher';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { PartyTypeRepository } from '../repositories/party-type.repository';
import { PhoneTypeRepository } from '../repositories/phone-type.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { MaskedPhoneDTO } from './dto/masked-phone.dto';

@Injectable()
export class MaskedPhoneService extends Import<MaskedPhoneDTO> {
  catalog: CatalogEnum = CatalogEnum.MaskedPhone;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly partyTypeRepository: PartyTypeRepository,
    protected readonly phoneTypeRepository: PhoneTypeRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super();
  }

  preValidate = undefined;

  public async publish(
    importData: MaskedPhoneDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<void> {
    new MaskedPhonePublisher(this.connection).publish({
      importData,
      userId,
      uploadHistoryId,
    });
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    const usePhone2Feature =
      await this.serviceParameterRepository.getGlobalParameterValueByName<boolean>(
        'usePhone2Feature',
      );

    if (usePhone2Feature) {
      data.MaskedPartyType = (
        await this.partyTypeRepository.getPartyType2List()
      ).map((item) => item.name);
    } else {
      data.MaskedPhoneType = (await this.phoneTypeRepository.getList()).map(
        (item) => item.name,
      );
      data.MaskedPartyType = (
        await this.partyTypeRepository.getPartyTypeList()
      ).map((item) => item.name);
    }

    return data;
  }
}
