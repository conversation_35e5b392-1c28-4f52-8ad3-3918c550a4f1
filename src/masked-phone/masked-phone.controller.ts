import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { Response } from 'express';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { MaskedPhoneService } from './masked-phone.service';
import { MaskedPhoneDTO } from './dto/masked-phone.dto';
import { ImportMaskedPhoneDTO } from './dto/import-masked-phone.dto';

@Controller('admin/import-masked-phone')
export class MaskedPhoneController {
  constructor(private maskedPhoneService: MaskedPhoneService) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.maskedPhoneService.getGeneratedCsv(query.id);

    response.set(
      'Content-disposition',
      'attachment; filename=ImportMaskedPhone - ' + query.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file', MaskedPhoneDTO)
  import(@Body() importMaskedPhoneDTO: ImportMaskedPhoneDTO) {
    const { importData, userID } = importMaskedPhoneDTO;
    return this.maskedPhoneService.import(importData, userID);
  }
}
