import 'source-map-support/register';
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as Sentry from '@sentry/node';
import { Logger } from 'nestjs-pino';
import { SocketIoV2Adapter } from './socket-io-v2.adapter';
import { AppModule } from './app.module';
import { EventEmitter } from 'events';

EventEmitter.defaultMaxListeners = 58;

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { logger: true });
  app.useLogger(app.get(Logger));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      errorHttpStatusCode: 422,
      validateCustomDecorators: true,
    }),
  );
  app.useWebSocketAdapter(new SocketIoV2Adapter(app));
  const configService = app.get(ConfigService);
  const PORT = configService.get('port') as number;

  Sentry.init({
    dsn: configService.get('sentry.dsn'),
    debug: configService.get('environment') !== 'production',
    environment: configService.get('environment'),
    release: 'nestjs-base@' + (configService.get('release') || 'unknown'),
  });

  const config = new DocumentBuilder()
    .setTitle('API title')
    .setDescription('API description')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(PORT);
}
bootstrap();
