import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CommentRepository } from '../repositories/activity/comment.repository';
import { ContactWithResultRepository } from '../repositories/activity/contact-with-result.repository';
import { HistoryRepository } from '../repositories/activity/history.repository';
import { InvoiceDiscountRepository } from '../repositories/data/invoice-discount.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportDiscountController } from './import-discount.controller';
import { ImportDiscountService } from './import-discount.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      InvoiceDiscountRepository,
      HistoryRepository,
      ContactWithResultRepository,
      CommentRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportDiscountController],
  providers: [
    ImportDiscountService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportDiscountModule {}
