import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { DiscountDTO } from './dto/discount.dto';
import { ImportDiscountService } from './import-discount.service';
import { ImportDiscountDTO } from './dto/import-discount.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-discount')
export class ImportDiscountController {
  constructor(private importDiscountService: ImportDiscountService) {}

  @Post()
  @CsvImport('file', DiscountDTO)
  import(@Body() importDiscountDTO: ImportDiscountDTO) {
    return this.importDiscountService.apply(importDiscountDTO);
  }
  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importDiscountService.getGeneratedCsv(
      query.id,
    );

    response.set(
      disposition,
      'attachment; filename=ImportDiscount - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importDiscountService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
