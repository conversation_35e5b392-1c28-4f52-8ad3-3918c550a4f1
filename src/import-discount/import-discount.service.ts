import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { InvoiceDiscount } from '../entities/data/invoice-discount.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportDiscountPublisher } from '../events/publishers/import-discount-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CommentRepository } from '../repositories/activity/comment.repository';
import { ContactWithResultRepository } from '../repositories/activity/contact-with-result.repository';
import { HistoryRepository } from '../repositories/activity/history.repository';
import { InvoiceDiscountRepository } from '../repositories/data/invoice-discount.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { DiscountDTO } from './dto/discount.dto';

const INSERTED_NUMBER = 'Number of inserted discounts';
const DELETED_NUMBER = 'Number of deleted discounts';

@Injectable()
export class ImportDiscountService extends BaseImportService<DiscountDTO> {
  public catalog: CatalogEnum = CatalogEnum.Discount;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.Discount;
  protected publisher: ImportPublisher<any> = new ImportDiscountPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly invoiceDiscountRepository: InvoiceDiscountRepository,
    protected readonly historyRepository: HistoryRepository,
    protected readonly contactWithResultRepository: ContactWithResultRepository,
    protected readonly commentRepository: CommentRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertItems = result?.insertItems;
    const deleteItems = result?.deleteItems;

    const newDiscountActivities = result?.newDiscountActivities;
    const deleteDiscountActivities = result?.deleteDiscountActivities;
    const activityResults = result?.activityResults;
    const activityComments = result?.activityComments;

    const metaData = result?.metaData;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_NUMBER]: 0,
          [DELETED_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_NUMBER]: this.resultDetalization[id][0][INSERTED_NUMBER],
      [DELETED_NUMBER]: this.resultDetalization[id][0][DELETED_NUMBER],
    };

    if (deleteItems.length > 0) {
      const chunks = this.sliceIntoChunks(deleteItems, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          InvoiceDiscount,
          { invoiceId: In(chunk), isDeleted: 0 },
          { isDeleted: 1, updatedUserId: metaData.userId },
        );
      }
      detalization[DELETED_NUMBER] += deleteItems.length;
    }

    if (insertItems.length > 0) {
      const models = insertItems.map((item: any) =>
        this.invoiceDiscountRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (newDiscountActivities.length > 0) {
      const models = newDiscountActivities.map((item: any) =>
        this.historyRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (deleteDiscountActivities.length > 0) {
      const models = deleteDiscountActivities.map((item: any) =>
        this.historyRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (activityResults.length > 0) {
      const models = activityResults.map((item: any) =>
        this.contactWithResultRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    if (activityComments.length > 0) {
      const models = activityComments.map((item: any) =>
        this.commentRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
      }
    }

    this.resultDetalization[id][0][INSERTED_NUMBER] =
      detalization[INSERTED_NUMBER];
    this.resultDetalization[id][0][DELETED_NUMBER] =
      detalization[DELETED_NUMBER];
  }
}
