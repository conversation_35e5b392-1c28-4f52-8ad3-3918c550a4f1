import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseStateHistoryRepository } from '../repositories/data/case-state-history.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportCaseStateController } from './import-case-state.controller';
import { ImportCaseStateService } from './import-case-state.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      CaseStateHistoryRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportCaseStateController],
  providers: [
    ImportCaseStateService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportCaseStateModule {}
