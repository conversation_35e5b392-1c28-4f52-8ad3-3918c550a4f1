import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { getManager, In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CaseStateHistory } from '../entities/data/case-state-history.entity';
import { TagForCase } from '../entities/data/tag-for-case.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportCaseStatePublisher } from '../events/publishers/import-case-state-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseStateHistoryRepository } from '../repositories/data/case-state-history.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { CaseStateDto } from './dto/case-state.dto';

const INSERTED_NUMBER = 'Number of inserted tags';
const DELETED_NUMBER = 'Number of deleted tags';

@Injectable()
export class ImportCaseStateService extends BaseImportService<CaseStateDto> {
  public catalog: CatalogEnum = CatalogEnum.CaseState;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.CaseState;
  protected publisher: ImportPublisher<any> = new ImportCaseStatePublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly caseStateHistoryRepository: CaseStateHistoryRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);

    const caseState = result?.caseStateHistories;
    const caseStateDelete = result?.caseStateHistoriesDelete;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_NUMBER]: 0,
          [DELETED_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_NUMBER]: this.resultDetalization[id][0][INSERTED_NUMBER],
      [DELETED_NUMBER]: this.resultDetalization[id][0][DELETED_NUMBER],
    };

    if (caseStateDelete.length > 0) {
      const chunks = this.sliceIntoChunks(caseStateDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          CaseStateHistory,
          { id: In(chunk), isDeleted: 0 },
          { isDeleted: 1 },
        );
      }

      detalization[DELETED_NUMBER] += caseStateDelete.length;
    }

    if (caseState.length > 0) {
      const models = caseState.map((item: any) =>
        this.caseStateHistoryRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    this.resultDetalization[id][0][INSERTED_NUMBER] =
      detalization[INSERTED_NUMBER];
    this.resultDetalization[id][0][DELETED_NUMBER] =
      detalization[DELETED_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    const caseStates = await getManager().query(`select "Name"
from "Dictionary"."CaseState" as dl
where dl."IsDeleted" = 0
order by 1;`);

    data.CaseStates = caseStates.map((index: any) => index.Name);

    return data;
  }
}
