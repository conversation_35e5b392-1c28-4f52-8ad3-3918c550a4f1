import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { CaseStateDto } from './dto/case-state.dto';
import { ImportCaseStateService } from './import-case-state.service';
import { ImportCaseStateDto } from './dto/import-case-state.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-case-state')
export class ImportCaseStateController {
  constructor(
    private readonly importCaseStateService: ImportCaseStateService,
  ) {}

  @Post()
  @CsvImport('file', CaseStateDto)
  import(@Body() importCaseStateDTO: ImportCaseStateDto) {
    return this.importCaseStateService.apply(importCaseStateDTO);
  }
  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importCaseStateService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportCaseState - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportCaseState - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importCaseStateService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
