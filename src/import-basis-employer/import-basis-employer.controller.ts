import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { EmployerDTO } from './dto/employer.dto';
import { ImportBasisEmployerService } from './import-basis-employer.service';
import { ImportEmployerDTO } from './dto/import-employer.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-basis-employer')
export class ImportBasisEmployerController {
  constructor(private importBasisEmployerService: ImportBasisEmployerService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importEmployerDTO: ImportEmployerDTO) {
    const { importFile, userID } = importEmployerDTO;

    return this.importBasisEmployerService.importLargeFile(
      importFile,
      userID,
      EmployerDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream = await this.importBasisEmployerService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportEmployer - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportEmployer - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importBasisEmployerService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
