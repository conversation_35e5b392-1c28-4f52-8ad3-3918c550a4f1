import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class EmployerDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  @IsNumber()
  public BasisForPaymentID: number;

  @Expose()
  @IsString()
  public DeliveryDate: string;

  @Expose()
  @IsString()
  public RequestToOriginalDocumentation: string;

  @Expose()
  @IsString()
  public WeDontHaveOriginalDocumentation: string;

  @Expose()
  @IsString()
  public WaitingOriginalDocumentation: string;

  @Expose()
  @IsString()
  public SentOriginalDocumentation: string;

  @Expose()
  @IsString()
  public ReturnedOriginalDocumentation: string;

  @Expose()
  @IsString()
  public DeliveryStatus: string;

  @Expose()
  @IsNumber()
  public DeliveryStatusID: number;

  @Expose()
  @IsString()
  public IsProcessed: string | boolean | null;
}
