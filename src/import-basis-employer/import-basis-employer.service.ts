import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { Custom004EEnforcementRequestToEmployer } from '../entities/legal/custom004-enforcement-request-to-employer.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportBasisEmployerPublisher } from '../events/publishers/import-basis-employer-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { Custom004EEnforcementRequestToEmployerRepository } from '../repositories/legal/custom004-enforcement-request-to-employer.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { EmployerDTO } from './dto/employer.dto';

const INSERTED_EMPLOYER_NUMBER = 'Number of inserted employers';
const UPDATED_EMPLOYER_NUMBER = 'Number of updated employers';

@Injectable()
export class ImportBasisEmployerService extends BaseImportService<EmployerDTO> {
  catalog: CatalogEnum = CatalogEnum.BasisEmployer;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.BasisEmployer;
  protected publisher: ImportPublisher<any> = new ImportBasisEmployerPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly custom004EEnforcementRequestDeliveryStatusRepository: Custom004EEnforcementRequestDeliveryStatusRepository,
    protected readonly custom004EEnforcementRequestToEmployerRepository: Custom004EEnforcementRequestToEmployerRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertEmployers = result?.insertEmployer;
    const deactivateEmployersByBasis = result?.deactivateEmployerByBasis;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_EMPLOYER_NUMBER]: 0,
          [UPDATED_EMPLOYER_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_EMPLOYER_NUMBER]:
        this.resultDetalization[id][0][INSERTED_EMPLOYER_NUMBER],
      [UPDATED_EMPLOYER_NUMBER]:
        this.resultDetalization[id][0][UPDATED_EMPLOYER_NUMBER],
    };

    if (deactivateEmployersByBasis.length > 0) {
      await queryRunner.manager.update(
        Custom004EEnforcementRequestToEmployer,
        { BasisID: In(deactivateEmployersByBasis), IsDeleted: 0 },
        { IsProcessed: false },
      );
      detalization[UPDATED_EMPLOYER_NUMBER] +=
        deactivateEmployersByBasis.length;
    }

    if (insertEmployers.length > 0) {
      await this.custom004EEnforcementRequestToEmployerRepository.save(
        insertEmployers,
      );
      detalization[INSERTED_EMPLOYER_NUMBER] += insertEmployers.length;
    }

    this.resultDetalization[id][0][INSERTED_EMPLOYER_NUMBER] =
      detalization[INSERTED_EMPLOYER_NUMBER];
    this.resultDetalization[id][0][UPDATED_EMPLOYER_NUMBER] =
      detalization[UPDATED_EMPLOYER_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    data.DeliveryStatus = (
      await this.custom004EEnforcementRequestDeliveryStatusRepository.find({
        where: {
          isDeleted: 0,
        },
      })
    ).map((item) => item.name);

    data.IsProcessed = ['Yes', 'No'];

    return data;
  }
}
