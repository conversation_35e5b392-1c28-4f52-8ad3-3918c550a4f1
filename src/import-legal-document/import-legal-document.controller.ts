import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { LegalDocumentDTO } from './dto/legal-document.dto';
import { ImportLegalDocumentService } from './import-legal-document.service';
import { ImportLegalDocumentDTO } from './dto/import-legal-document.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-legal-document')
export class ImportLegalDocumentController {
  constructor(private importLegalDocumentService: ImportLegalDocumentService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importLegalDocumentDTO: ImportLegalDocumentDTO) {
    const { importFile, userID, typeID } = importLegalDocumentDTO;

    const validationResult =
      await this.importLegalDocumentService.checkFileTemplate(
        typeID,
        importFile,
      );

    if (!validationResult.success) {
      return validationResult;
    }

    return this.importLegalDocumentService.importLargeFile(
      importFile,
      userID,
      LegalDocumentDTO,
      { typeId: typeID },
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream =
      await this.importLegalDocumentService.getGeneratedCsvFields(
        query.id,
        query.parameters,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportLegalDocument - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportLegalDocument - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importLegalDocumentService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
