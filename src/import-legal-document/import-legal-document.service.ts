import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import fs from 'fs';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import readline from 'readline';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import stream from 'stream';
import { getManager, QueryRunner } from 'typeorm';
import { HttpMethod } from '../common/enums/http-method.enum';
import { ToolSourceEnum } from '../common/enums/tool-source.enum';
import { splitOnChunks } from '../common/helpers/split-on-chunks';
import { BaseImportWithParameterService } from '../common/imports/base-import-with-parameter.service';
import { REDIS } from '../common/redis/constants';
import { ValidationResult } from '../common/validation/validation-result.type';
import { History } from '../entities/activity/history.entity';
import { DocumentParameter } from '../entities/data/document-parameter.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportLegalDocumentPublisher } from '../events/publishers/import-legal-document-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseRepository } from '../repositories/data/case.repository';
import { DocumentParameterRepository } from '../repositories/data/document-parameter.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { LegalDocumentDTO } from './dto/legal-document.dto';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

const INSERTED_DOCUMENT_NUMBER = 'Number of inserted documents';
const INSERTED_PARAMETER_NUMBER = 'Number of inserted parameters';

@Injectable()
export class ImportLegalDocumentService extends BaseImportWithParameterService<LegalDocumentDTO> {
  catalog: CatalogEnum = CatalogEnum.LegalDocument;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.LegalDocument;
  protected publisher: ImportPublisher<any> = new ImportLegalDocumentPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly documentRepository: DocumentRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly documentParameterRepository: DocumentParameterRepository,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly dataTypeRepository: DataTypeRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly eventRuleLogRepository: EventRuleLogRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
      invoiceRepository,
      parameterRepository,
      dataTypeRepository,
      serviceParameterRepository,
      eventRuleLogRepository,
      caseRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertDocuments = result?.documents;
    const insertDocumentParameters = result?.documentParameters;
    const insertDocumentParameterToCaseID = result?.documentParameterToCaseID;
    const casesToActivateLegalRules = result?.casesToActivateLegalRules;
    const metaData = result?.metaData;

    this.saveAppellateCourt(id, result);

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_DOCUMENT_NUMBER]: 0,
          [INSERTED_PARAMETER_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_DOCUMENT_NUMBER]:
        this.resultDetalization[id][0][INSERTED_DOCUMENT_NUMBER],
      [INSERTED_PARAMETER_NUMBER]:
        this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER],
    };

    if (insertDocuments.length > 0) {
      const documents = insertDocuments.map((item: any) => {
        return this.documentRepository.create({
          ...item,
          levelId: metaData.levelId,
          typeId: metaData.typeId,
          insertedUserId: metaData.userId,
          updatedUserId: metaData.userId,
        });
      });

      await queryRunner.manager.save(documents);
      this.saveDocumentIDs(
        id,
        documents.map((item: any) => item.id),
      );
      detalization[INSERTED_DOCUMENT_NUMBER] += documents.length;
    }

    if (insertDocumentParameters.length > 0) {
      const documentParameters = insertDocumentParameters.map((item: any) => {
        return this.documentParameterRepository.create(item);
      });

      const parameters = await queryRunner.manager.save(documentParameters);
      this.postActionData[id].userId = metaData.userId;
      if (this.postActionData[id].parameters) {
        this.postActionData[id].parameters = [
          ...this.postActionData[id].parameters,
          ...parameters.map((item: DocumentParameter) => item.id),
        ];
      } else {
        this.postActionData[id].parameters = parameters.map(
          (item: DocumentParameter) => item.id,
        );
      }

      if (casesToActivateLegalRules.length > 0) {
        const caseChanges = await this.getCaseChanges(
          insertDocumentParameters,
          insertDocumentParameterToCaseID,
        );

        this.activateLegalRules(
          casesToActivateLegalRules,
          caseChanges,
          id,
          metaData.userId,
        );
      }

      detalization[INSERTED_PARAMETER_NUMBER] += documentParameters.length;
    }

    this.resultDetalization[id][0][INSERTED_DOCUMENT_NUMBER] =
      detalization[INSERTED_DOCUMENT_NUMBER];
    this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER] =
      detalization[INSERTED_PARAMETER_NUMBER];
  }

  public async getGeneratedCsvFields(
    id: string,
    parameters: string[],
  ): Promise<stream.PassThrough> {
    if (id === 'dictionary') {
      const dictionaries = await this.parameterRepository.getSlugDictionary(
        parameters,
      );
      return this.generateExcelFile(dictionaries);
    }
    let csvBody = '';
    csvBody =
      parameters && parameters.length > 0
        ? [
            'LegalCaseID',
            ...parameters.map((parameter) => `${parameter}`),
          ].join(';')
        : ['LegalCaseID'].join(';');

    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  async checkFileTemplate(
    typeID: number,
    filePath: string,
  ): Promise<ValidationResult> {
    const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Number.POSITIVE_INFINITY,
    });

    let headers: string[] = [];
    for await (const line of rl) {
      headers = Object.keys(JSON.parse(line));
      break;
    }

    headers = headers.filter((header) => header !== 'LegalCaseID');

    if (headers.length === 0) {
      return {
        success: true,
        errors: [],
      } as ValidationResult;
    }

    type parameter = {
      ID: number;
      Name: string;
      Slug: string;
      IsRequired: number;
    };
    try {
      const dictionaryServiceURL = this.config.get<string>(
        'dictionaryService.url',
      );
      const availableParams = await this.apiClient.sendRequest<parameter[]>(
        HttpMethod.GET,
        dictionaryServiceURL + '/document-parameter-by-type?TypeID=' + typeID,
        { timeout: 5000 },
      );

      const requiredSlugs = new Set(
        availableParams
          .filter((parameter: parameter) => parameter.IsRequired)
          .map((parameter: parameter) => parameter.Slug),
      );

      for (const requiredSlug of requiredSlugs) {
        if (!headers.includes(requiredSlug)) {
          return {
            success: false,
            errors: [
              {
                column: requiredSlug,
                errorCode: 'HeaderRequired',
                row: 1,
              },
            ],
          } as ValidationResult;
        }
      }

      const availableSlugs = new Set(
        availableParams.map((parameter: parameter) => parameter.Slug),
      );

      for (const header of headers) {
        if (!availableSlugs.has(header)) {
          return {
            success: false,
            errors: [
              {
                column: header,
                errorCode: 'HeaderIsNotAcceptable',
                row: 1,
              },
            ],
          } as ValidationResult;
        }
      }
    } catch {
      return {
        success: false,
        errors: [
          {
            column: 'Headers',
            errorCode: 'HeaderValidationFailed',
            row: 1,
          },
        ],
      } as ValidationResult;
    }

    return {
      success: true,
    } as ValidationResult;
  }

  async createLegalTask(parameters: number[], userId: number): Promise<void> {
    try {
      if (parameters.length > 0) {
        const caseServiceURL = this.config.get<string>('caseService.url');
        await this.httpService
          .post(caseServiceURL + '/legal-task-manager', {
            strategy: 'parameter',
            ParameterIDs: parameters,
            userId,
          })
          .toPromise();
      }
    } catch (error: any) {
      console.log(error);
    }
  }

  async postActionHandler(id: number) {
    await this.createLegalTask(
      this.postActionData[id].parameters,
      this.postActionData[id].userId,
    );
    await this.callAppellateCourtMapping(id);
    await this.callCloseTasksByDocuments(id, this.postActionData[id].userId);
  }

  protected async getCaseChanges(
    create: DocumentParameter[],
    insertDocumentParameterToCaseID: any,
  ): Promise<{ [key: number]: any }> {
    const caseChanges: { [key: number]: any } = {};

    for (const record of create) {
      const caseId = insertDocumentParameterToCaseID[record.documentId];
      caseChanges[caseId] = caseChanges[caseId] ?? [];
      caseChanges[caseId].push({
        ParameterID: record.parameterId,
        ParameterValue: record.value,
      });
    }

    return caseChanges;
  }

  protected getToolSource() {
    return ToolSourceEnum.ImportDocumentParameter;
  }
}
