import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { TerminusModule } from '@nestjs/terminus';
import { RavenInterceptor, RavenModule } from 'nest-raven';
import { LoggerModule } from 'nestjs-pino';
import { EventsGateway } from './events/events.gateway';

import configuration from 'src/config/configuration';
import { loggerModuleOptions } from 'src/config/async-options/logger-module.options';
import { typeormModuleOptions } from 'src/config/async-options/typeorm-module.options';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { RedisModule } from 'src/common/redis/redis.module';
import { HealthModule } from 'src/health/health.module';
import { ListenerModule } from 'src/events/listeners/listener.module';
import { ImportActivityParameterModule } from './import-activity-parameter/import-activity-parameter.module';
import { ImportAttachmentModule } from './import-attachment/import-attachment.module';
import { ImportBasisComplaintModule } from './import-basis-complaint/import-basis-complaint.module';
import { ImportBasisCpiioModule } from './import-basis-cpiio/import-basis-cpiio.module';
import { ImportBasisEmployerModule } from './import-basis-employer/import-basis-employer.module';
import { ImportBasisFinaModule } from './import-basis-fina/import-basis-fina.module';
import { ImportCaseAttachmentModule } from './import-case-attachment/import-case-attachment.module';
import { ImportCaseCloningModule } from './import-case-cloning/import-case-cloning.module';
import { ImportCaseDiscountModule } from './import-case-discount/import-case-discount.module';
import { ImportCaseParameterModule } from './import-case-parameter/import-case-parameter.module';
import { ImportCaseStateModule } from './import-case-state/import-case-state.module';
import { ImportContactPersonModule } from './import-contact-person/import-contact-person.module';
import { ImportContractDataModule } from './import-contract-data/import-contract-data.module';
import { ImportCourtParameterModule } from './import-court-parameter/import-court-parameter.module';
import { ImportCronModule } from './import-cron/import-cron.module';
import { ImportDeleteInteractionModule } from './import-delete-interaction/import-delete-interaction.module';
import { ImportDeleteLegalCaseModule } from './import-delete-legal-case/import-delete-legal-case.module';
import { ImportDiscountModule } from './import-discount/import-discount.module';
import { ImportExtraInfoModule } from './import-extra-info/import-extra-info.module';
import { ImportSmsActivityModule } from './import-sms-activity/import-sms-activity.module';
import { ImportTagModule } from './import-tag/import-tag.module';
import { ImportTransferToCollectionAgencyModule } from './import-transfer-to-collection-agency/import-transfer-to-collection-agency.module';
import { ImportInteractionModule } from './import-interaction/import-interaction.module';
import { ImportLegalAppellateCourtModule } from './import-legal-appellate-court/import-legal-appellate-court.module';
import { ImportLegalCaseModule } from './import-legal-case/import-legal-case.module';
import { ImportLegalCourtModule } from './import-legal-court/import-legal-court.module';
import { ImportLegalDocumentModule } from './import-legal-document/import-legal-document.module';
import { ImportLegalInvoiceModule } from './import-legal-invoice/import-legal-invoice.module';
import { ImportLetterPrintHouseDataModule } from './import-letter-print-house-data/import-letter-print-house-data.module';
import { MaskedPhoneModule } from './masked-phone/masked-phone.module';
import { UpdateLegalDocumentModule } from './update-legal-document/update-legal-document.module';
import { UploadHistoryModule } from './upload-history/upload-history.module';
import { ImportPhoneModule } from './import-phone/import-phone.module';

@Module({
  imports: [
    ConfigModule.forRoot({ load: [configuration] }),
    LoggerModule.forRootAsync(loggerModuleOptions),
    PrometheusModule.register(),
    TerminusModule,
    RavenModule,
    RabbitmqModule,
    RedisModule,
    TypeOrmModule.forRootAsync(typeormModuleOptions),
    HealthModule,
    ListenerModule,
    MaskedPhoneModule,
    ImportContactPersonModule,
    ImportLetterPrintHouseDataModule,
    UploadHistoryModule,
    ImportCaseAttachmentModule,
    ImportInteractionModule,
    ImportCaseCloningModule,
    ImportExtraInfoModule,
    ImportContractDataModule,
    ImportLegalCaseModule,
    ImportLegalInvoiceModule,
    UpdateLegalDocumentModule,
    ImportCronModule,
    ImportLegalDocumentModule,
    ImportCourtParameterModule,
    ImportLegalCourtModule,
    ImportLegalAppellateCourtModule,
    ImportBasisFinaModule,
    ImportBasisCpiioModule,
    ImportBasisEmployerModule,
    ImportBasisComplaintModule,
    ImportTransferToCollectionAgencyModule,
    ImportActivityParameterModule,
    ImportDiscountModule,
    ImportCaseDiscountModule,
    ImportTagModule,
    ImportCaseParameterModule,
    ImportAttachmentModule,
    ScheduleModule.forRoot(),
    ImportPhoneModule,
    ImportDeleteLegalCaseModule,
    ImportDeleteInteractionModule,
    ImportCaseStateModule,
    ImportSmsActivityModule,
  ],
  providers: [
    EventsGateway,
    {
      provide: APP_INTERCEPTOR,
      useValue: new RavenInterceptor(),
    },
  ],
})
export class AppModule {}
