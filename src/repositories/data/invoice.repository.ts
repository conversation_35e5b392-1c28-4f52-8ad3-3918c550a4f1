import { EntityRepository, Repository } from 'typeorm';

import { Invoice } from 'src/entities/data/invoice.entity';

@EntityRepository(Invoice)
export class InvoiceRepository extends Repository<Invoice> {
  async findActiveInvoices(caseIDs: number[]): Promise<Invoice[]> {
    return this.createQueryBuilder('di')
      .select([
        'di."ID" as "id"',
        'di."InvoiceAgreementCloseDate" as "invoiceAgreementCloseDate"',
        'di."IsDeleted" as "isDeleted"',
        'di."CaseID" as "caseId"',
      ])
      .where(
        'COALESCE(di."InvoiceAgreementCloseDate", :defaultDate) > CURRENT_DATE',
        {
          defaultDate: '2100-01-01',
        },
      )
      .andWhere('di."IsDeleted" = :isDeleted', { isDeleted: 0 })
      .andWhere('di."CaseID" in (:...cases)', { cases: caseIDs })
      .getRawMany();
  }

  async getCaseAmount(
    caseIDs: number[],
  ): Promise<{ CaseID: number; ActualSum: number }[]> {
    return this.manager.query(`SELECT fc."CaseID",
    (sum(CASE
               WHEN fc."UseToCaseActualAmount" = 1 THEN fc."CaseCurrencyAmount"
               ELSE 0 END) * (-1.0)) :: NUMERIC(12, 2) "ActualSum"
FROM "Financial"."Custom004GetTransactionAmount"(array [${caseIDs.join(
      ',',
    )}]::bigint[]) fc
GROUP BY fc."CaseID";`);
  }

  async getInvoiceAmount(
    caseIDs: number[],
  ): Promise<{ InvoiceID: number; ActualSum: number }[]> {
    return this.manager.query(`SELECT di."ID" as "InvoiceID", (sum(CASE
    WHEN fc."UseToCaseActualAmount" = 1 THEN fc."CaseCurrencyAmount"
    ELSE 0 END) * (-1.0)) :: NUMERIC(12, 2) "ActualSum"
    FROM "Financial"."Custom004GetTransactionAmount"(array [${caseIDs.join(
      ',',
    )}]::bigint[]) fc
    join "Data"."Invoice" "di" on di."ID" = fc."InvoiceID" AND di."IsDeleted" = 0
    GROUP BY di."ID";`);
  }
}
