import { EntityRepository, getManager, Repository } from 'typeorm';

import { Phone } from '../../entities/data/phone.entity';

@EntityRepository(Phone)
export class PhoneRepository extends Repository<Phone> {
  async getPhonesByNumbers(
    numbers: Array<string | unknown>,
    debtorId?: number | string,
  ): Promise<Phone[]> {
    const builder = this.createQueryBuilder('dp').where(
      'dp."PhoneNumber" IN (:...numbers)',
      { numbers },
    );

    if (debtorId) {
      builder.andWhere('dp."DebtorID" = :debtorId', { debtorId });
    }

    return builder.getMany();
  }

  async getPhonesByNumbersAndDebtor(
    phoneDebtorPair: Record<string, string>,
  ): Promise<Phone[]> {
    const phoneDebtorQueryString = Object.entries(phoneDebtorPair)
      .map(([phoneNumber, debtorId]) => {
        return `('${phoneNumber}', '${debtorId}')`;
      })
      .join(',');

    return await getManager().query(
      `SELECT 
          dp."ID" as "id",
          dp."PhoneNumber" as "phoneNumber",
          dp."DebtorID" as "debtorId"
        FROM "Data"."Phone" as dp 
        WHERE (dp."PhoneNumber", dp."DebtorID") in (${phoneDebtorQueryString})`,
    );
  }
}
