import { EntityRepository, get<PERSON><PERSON><PERSON>, Repository } from 'typeorm';

import { Case } from 'src/entities/data/case.entity';
import { CloneCase } from '../../entities/activity/clone-case.entity';
import { History } from '../../entities/activity/history.entity';
import { CaseCloningPreImportDataDTO } from '../../events/listeners/case-cloning-listener/dto/case-cloning-pre-import-data.dto';
import { CloneCaseRepository } from '../activity/clone-case.repository';
import { HistoryRepository } from '../activity/history.repository';

@EntityRepository(Case)
export class CaseRepository extends Repository<Case> {
  async runCloneCase(
    cases: CaseCloningPreImportDataDTO[],
    userId: number,
    historyRepository: HistoryRepository,
    cloneCaseRepository: CloneCaseRepository,
    historyTypeId: string,
  ) {
    const result: { success: boolean; report: any[] } = {
      success: true,
      report: [],
    };

    const results = [];

    const chunkSize = 1000;
    const chunks: CaseCloningPreImportDataDTO[][] = this.sliceIntoChunks(
      cases,
      chunkSize,
    );

    const queryRunner = this.manager.connection.createQueryRunner('master');
    await queryRunner.startTransaction();

    try {
      for (const chunk of chunks) {
        const cases = chunk.map((row) => {
          return {
            CaseID: row.ParentCaseID,
            PackageID: row.NewPackageID,
          };
        });
        const json = JSON.stringify(cases);
        const result = await queryRunner.manager.query(
          `select * from "Data"."CloneCase"('${json}'::text::jsonb);`,
        );
        results.push(result);
        const [importHistoryData, importCloneCaseData] =
          await this.saveActivity(
            result,
            userId,
            historyTypeId,
            historyRepository,
            cloneCaseRepository,
          );

        await queryRunner.manager.save(History, importHistoryData);
        await queryRunner.manager.save(CloneCase, importCloneCaseData);
      }

      await queryRunner.commitTransaction();
    } catch {
      result.success = false;
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }

    if (result.success) {
      result.report = results.flat();
    }

    return result;
  }

  sliceIntoChunks(items: any[], chunkSize: number) {
    const result = [];
    for (let index = 0; index < items.length; index += chunkSize) {
      const chunk = items.slice(index, index + chunkSize);
      result.push(chunk);
    }
    return result;
  }

  async saveActivity(
    report: any[],
    userId: number,
    historyTypeId: string,
    historyRepository: HistoryRepository,
    cloneCaseRepository: CloneCaseRepository,
  ) {
    const importHistoryData: any = [];
    const importCloneCaseData: any = [];

    const pool = await historyRepository.getPoolHistoryIds(report.length);
    for (const [index, row] of report.entries()) {
      const historyAttributes: Partial<History> = {
        id: String(pool[index]),
        typeId: Number(historyTypeId),
        caseId: row.ParentCaseID,
        creationUserID: userId,
      };

      const cloneCaseAttributes: Partial<CloneCase> = {
        historyId: String(pool[index]),
        newCaseID: row.NewCaseID,
        newPackageID: row.NewPackageID,
      };

      const historyModel = historyRepository.create(historyAttributes);
      const cloneCaseModel = cloneCaseRepository.create(cloneCaseAttributes);

      importHistoryData.push(historyModel);
      importCloneCaseData.push(cloneCaseModel);
    }

    return [importHistoryData, importCloneCaseData];
  }

  public async getPoolCaseIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"Data"."Case_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "Data"."Case_ID_seq") as last_sequence`,
      [count],
    );
    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
