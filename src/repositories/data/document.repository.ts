import { EntityRepository, Repository } from 'typeorm';

import { Document } from '../../entities/data/document.entity';
@EntityRepository(Document)
export class DocumentRepository extends Repository<Document> {
  async getDocumentIDPool(count: number): Promise<number[]> {
    const [sequence] = await this.query(
      `
      SELECT last_value + 1 AS from, setval('"Data"."Document_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "Data"."Document_ID_seq") as last_sequence`,
      [count],
    );

    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
