import { EntityRepository, getManager } from 'typeorm';
import { <PERSON><PERSON>erson } from '../../entities/data/contact-person.entity';
import { BaseRepository } from '../base-repository';

@EntityRepository(ContactPerson)
export class ContactPersonRepository extends BaseRepository<ContactPerson> {
  public async getPoolIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"Data"."ContactPerson_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "Data"."ContactPerson_ID_seq") as last_sequence`,
      [count],
    );
    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
