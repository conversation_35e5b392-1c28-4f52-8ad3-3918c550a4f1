import { EntityRepository, In } from 'typeorm';
import { BaseRepository } from '../base-repository';
import { Custom001PhoneStatus } from 'src/entities/dictionary/custom001-phone-status.entity';

@EntityRepository(Custom001PhoneStatus)
export class Custom001PhoneStatusRepository extends BaseRepository<Custom001PhoneStatus> {
  async getListByNames(
    names: Array<string | unknown>,
  ): Promise<Custom001PhoneStatus[]> {
    return this.find({
      where: { name: In(names) },
    });
  }
}
