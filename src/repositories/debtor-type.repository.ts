import { EntityRepository, Repository } from 'typeorm';

import { DebtorType } from '../entities/dictionary/debtor-type.entity';

@EntityRepository(DebtorType)
export class DebtorTypeRepository extends Repository<DebtorType> {
  public async getList(): Promise<Pick<DebtorType, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('ddt')
      .select(['ddt."ID" as "id"', 'ddt."Name" as "name"'])
      .where('ddt."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
