import { EntityRepository, Repository } from 'typeorm';
import { PhoneCode } from 'src/entities/dictionary/phone-code.entity';

@EntityRepository(PhoneCode)
export class PhoneCodeRepository extends Repository<PhoneCode> {
  async getNonMobileCodes(): Promise<PhoneCode[]> {
    return this.find({
      where: { isMobile: 0 },
    });
  }

  async getMobileCodes(): Promise<PhoneCode[]> {
    return this.find({
      where: { isMobile: 1 },
    });
  }
}
