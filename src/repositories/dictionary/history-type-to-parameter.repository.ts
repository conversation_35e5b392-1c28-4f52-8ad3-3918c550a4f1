import { EntityRepository, getManager, Repository } from 'typeorm';
import { HistoryTypeToParameter } from '../../entities/dictionary/history-type-to-parameter.entity';

@EntityRepository(HistoryTypeToParameter)
export class HistoryTypeToParameterRepository extends Repository<HistoryTypeToParameter> {
  async getParameterSlugs(typeId: number): Promise<string[]> {
    const queryRunner = getManager().connection.createQueryRunner();

    const rows = await queryRunner.manager.query(
      `select dp."Slug" from "Dictionary"."HistoryTypeToParameter" dh
           left join "Dictionary"."Parameter" dp on dh."ParameterID" = dp."ID"
           where dh."HistoryTypeID" = ${typeId} and dh."IsDeleted" = 0;`,
    );

    await queryRunner.release();

    return rows.map((r: any) => r.Slug);
  }
}
