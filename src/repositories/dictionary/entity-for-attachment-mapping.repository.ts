import { EntityRepository, Repository } from 'typeorm';
import { EntityForAttachmentMapping } from '../../entities/dictionary/entity-for-attachment-mapping.entity';

@EntityRepository(EntityForAttachmentMapping)
export class EntityForAttachmentMappingRepository extends Repository<EntityForAttachmentMapping> {
  public async getList(): Promise<
    Pick<EntityForAttachmentMapping, 'id' | 'name'>[]
  > {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
