import { EntityRepository, Repository } from 'typeorm';
import { Custom005LetterSender } from '../../entities/dictionary/custom005-letter-sender.entity';

@EntityRepository(Custom005LetterSender)
export class Custom005LetterSenderRepository extends Repository<Custom005LetterSender> {
  public async getList(): Promise<
    Pick<Custom005LetterSender, 'id' | 'name'>[]
  > {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
