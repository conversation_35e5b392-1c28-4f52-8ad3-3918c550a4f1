import { EntityRepository, Repository } from 'typeorm';
import { DataSource } from '../../entities/dictionary/data-source.entity';

@EntityRepository(DataSource)
export class DataSourceRepository extends Repository<DataSource> {
  async getContactPersonList() {
    const queryBuilder = this.createQueryBuilder('dpt').select([
      'dpt."ID" as "id"',
      'dpt."Name" as "name"',
    ]);

    queryBuilder.andWhere("dpt.\"Availability\"->>'ContactPerson'::text = '1'");

    return queryBuilder.execute();
  }

  async getPhoneSourceList(
    names?: Array<string | unknown>,
  ): Promise<DataSource[]> {
    const queryBuilder = this.createQueryBuilder('ds')
      .select(['ds."ID" as "id"', 'ds."Name" as "name"'])
      .where(`ds."Availability"->'Phone' = '1'`);

    if (names) {
      queryBuilder.andWhere('ds."Name" IN (:...phoneSources)', {
        phoneSources: [...names],
      });
    }

    return queryBuilder.execute();
  }
}
