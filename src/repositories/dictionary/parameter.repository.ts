import { EntityRepository, get<PERSON><PERSON>ger, Repository } from 'typeorm';

import { Parameter } from '../../entities/dictionary/parameter.entity';

@EntityRepository(Parameter)
export class ParameterRepository extends Repository<Parameter> {
  async getSlugDictionary(
    slugs: string[],
  ): Promise<{ [key: string]: { [key: string]: number } }> {
    const map: { [key: string]: { [key: string]: number } } = {};
    const queryRunner = getManager().connection.createQueryRunner();
    const placeholders = slugs.map((_, index) => `$${index + 1}`).join(', ');

    let rows = [];
    if (placeholders.length > 0) {
      rows = await queryRunner.manager.query(
        `select dp."Slug", dpdt."SourceTable" from "Dictionary"."Parameter" dp
           left join "Dictionary"."DataType" ddt on dp."DataTypeID" = ddt."ID"
           left join "Dictionary"."ParameterDictionaryTable" dpdt on dp."DictionaryTableID" = dpdt."ID"
           where dp."Slug" in (${placeholders}) and dpdt."SourceTable" is not null;`,
        slugs,
      );
    }

    for (const row of rows) {
      map[row.Slug] = {};
      const dictionaryValues = await queryRunner.manager.query(
        `select "ID", "Name" from ${row.SourceTable} where "IsDeleted" = 0;`,
      );
      for (const dictionaryValue of dictionaryValues) {
        map[row.Slug][dictionaryValue.Name] = dictionaryValue.ID;
      }
    }

    await queryRunner.release();

    return map;
  }

  async getParametersHasActivateCourtMapping() {
    return new Set(
      (
        await this.createQueryBuilder('parameter')
          .select('parameter."ID"')
          .where(
            `(parameter."AdditionalData" ->> 'ActivateAppellateCourtMapping')::text::int = :value`,
            { value: 1 },
          )
          .andWhere('parameter."IsDeleted" = :isDeleted', { isDeleted: 0 })
          .getRawMany()
      ).map((row) => row.ID),
    );
  }

  async getParametersHasDebtorRegrouping() {
    return new Set(
      (
        await this.createQueryBuilder('parameter')
          .select('parameter."ID"')
          .where(
            `(parameter."AdditionalData" ->> 'TriggerDebtorRegrouping')::text::int = :value`,
            { value: 1 },
          )
          .andWhere('parameter."IsDeleted" = :isDeleted', { isDeleted: 0 })
          .getRawMany()
      ).map((row) => row.ID),
    );
  }
}
