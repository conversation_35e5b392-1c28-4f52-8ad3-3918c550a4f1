import { EntityRepository, Repository } from 'typeorm';
import { Region } from '../../entities/dictionary/region.entity';

@EntityRepository(Region)
export class RegionRepository extends Repository<Region> {
  public async getList(): Promise<Pick<Region, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
