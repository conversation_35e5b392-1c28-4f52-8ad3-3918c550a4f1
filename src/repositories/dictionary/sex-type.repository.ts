import { EntityRepository, Repository } from 'typeorm';

import { SexType } from '../../entities/dictionary/sex-type.entity';

@EntityRepository(SexType)
export class SexTypeRepository extends Repository<SexType> {
  public async getList(): Promise<Pick<SexType, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
