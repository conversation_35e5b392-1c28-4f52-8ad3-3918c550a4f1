import { EntityRepository, Repository } from 'typeorm';
import { AvailabilityOption } from '../../entities/dictionary/availability-option.entity';
import { Region } from '../../entities/dictionary/region.entity';

@EntityRepository(AvailabilityOption)
export class AvailabilityOptionRepository extends Repository<AvailabilityOption> {
  public async getList(): Promise<Pick<Region, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
