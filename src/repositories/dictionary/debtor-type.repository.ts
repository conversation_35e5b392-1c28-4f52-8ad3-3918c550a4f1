import { EntityRepository, Repository } from 'typeorm';
import { DebtorType } from '../../entities/dictionary/debtor-type.entity';

@EntityRepository(DebtorType)
export class DebtorTypeRepository extends Repository<DebtorType> {
  public async getMap(): Promise<{ [key: string]: number }> {
    const queryBuilder = this.createQueryBuilder('dst').select([
      'dst."ID" as "id"',
      'dst."Name" as "name"',
    ]);

    const items = await queryBuilder.execute();
    const map: { [key: string]: number } = {};
    for (const item of items) {
      map[item['name']] = item['id'];
    }
    return map;
  }
}
