import { EntityRepository, In, Repository } from 'typeorm';

import { PhoneType } from 'src/entities/dictionary/phone-type.entity';

@EntityRepository(PhoneType)
export class PhoneTypeRepository extends Repository<PhoneType> {
  public async getList(): Promise<Pick<PhoneType, 'id' | 'name'>[]> {
    return this.find({ select: ['id', 'name'], where: { isDeleted: 0 } });
  }

  public async getListByNames(
    names: Array<string | unknown>,
  ): Promise<PhoneType[]> {
    return this.find({
      where: { name: In(names) },
    });
  }
}
