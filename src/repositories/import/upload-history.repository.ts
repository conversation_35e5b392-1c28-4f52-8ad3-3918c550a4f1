import { EntityRepository, Repository } from 'typeorm';

import { UploadHistory } from 'src/entities/import/upload-history.entity';

@EntityRepository(UploadHistory)
export class UploadHistoryRepository extends Repository<UploadHistory> {
  async getFromMaster(id: string) {
    const queryRunner = this.manager.connection.createQueryRunner('master');
    const uploadHistory = await queryRunner.manager.findOne(UploadHistory, id);
    await queryRunner.release();
    return uploadHistory;
  }
}
