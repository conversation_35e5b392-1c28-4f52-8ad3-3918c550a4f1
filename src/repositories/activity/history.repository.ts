import { EntityRepository, get<PERSON><PERSON><PERSON>, In, Repository } from 'typeorm';

import { History } from '../../entities/activity/history.entity';

@EntityRepository(History)
export class HistoryRepository extends Repository<History> {
  public async getExistContactsMapping(
    contactIDs: number[],
  ): Promise<{ [key: number]: History }> {
    if (contactIDs.length === 0) {
      return {};
    }

    const contacts = await this.find({
      select: ['id', 'caseId', 'isDeleted'],
      where: {
        id: In(contactIDs),
      },
    });

    const mapping: { [key: number]: History } = {};
    for (const contact of contacts) {
      mapping[Number(contact.id)] = contact;
    }

    return mapping;
  }

  public async getPoolHistoryIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"Activity"."History_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "Activity"."History_ID_seq") as last_sequence`,
      [count],
    );

    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
