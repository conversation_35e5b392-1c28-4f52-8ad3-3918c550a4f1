import { EntityManager, Repository, SaveOptions } from 'typeorm';

export class BaseRepository<T> extends Repository<T> {
  public async transactionSave(
    manager: EntityManager,
    entities: T,
    options?: SaveOptions | undefined,
  ): Promise<T>;
  public async transactionSave(
    manager: EntityManager,
    entities: T[],
    options?: SaveOptions | undefined,
  ): Promise<T[]>;

  public async transactionSave(
    manager: EntityManager,
    entities: any,
    options?: SaveOptions | undefined,
  ): Promise<any> {
    return manager.save(entities, options);
  }
}
