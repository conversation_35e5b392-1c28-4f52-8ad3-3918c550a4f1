import { NotFoundException } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { EntityRepository, In, Repository } from 'typeorm';
import { Case } from '../../entities/data/case.entity';
import { Contact<PERSON>erson } from '../../entities/data/contact-person.entity';
import { CourtProcess } from '../../entities/legal/court-process.entity';

import { LegalInvoice } from '../../entities/legal/legal-invoice.entity';
import { ChangeStageHandler } from '../../events/change-stage-event/change-stage-handler';
import { ChangeStatusHandler } from '../../events/change-status-event/change-status-handler';
import { LegalCasePreImportDataDto } from '../../events/listeners/legal-case-listener/dto/legal-case-pre-import-data.dto';
import { LegalInvoicePreImportDataDto } from '../../events/listeners/legal-invoice-listener/dto/legal-invoice-pre-import-data.dto';
import { CaseRepository } from '../data/case.repository';
import { ContactPersonRelationRepository } from '../data/contact-person-relation.repository';
import { ContactPersonRepository } from '../data/contact-person.repository';
import { DebtorRepository } from '../data/debtor.repository';
import { ServiceParameterRepository } from '../service-parameter.repository';
import { CourtProcessRepository } from './court-process.repository';

@EntityRepository(LegalInvoice)
export class LegalInvoiceRepository extends Repository<LegalInvoice> {
  public async createLegalCases(
    cases: LegalCasePreImportDataDto[],
    userId: number,
    packageID: number,
    caseRepository: CaseRepository,
    debtorRepository: DebtorRepository,
    courtProcessRepository: CourtProcessRepository,
    contactPersonRepository: ContactPersonRepository,
    contactPersonRelationRepository: ContactPersonRelationRepository,
  ): Promise<any> {
    const results = [];
    const importDebtorData = [];
    const importContactPersonData = [];
    const importCaseData = [];
    const importLegalInvoiceData = [];
    const importCourtProcessData = [];
    const importContactPersonRelationData = [];
    const casesPreLegal = [];
    const casesLegal = [];
    const uniqueCaseIDs = this.getUniqueCaseIDs(cases);
    const casesToClone = await caseRepository.find({
      where: {
        id: In(uniqueCaseIDs),
      },
      relations: ['debtor'],
    });

    let contactPersons: ContactPerson[] = [];
    const debtorIds = [];
    const debtorIdToCpId: { [key: number]: number } = {};

    for (const row of cases) {
      const caseEnt = this.findEntityById(casesToClone, row.CaseID);
      debtorIds.push(caseEnt.debtorId);
    }

    if (debtorIds.length > 0) {
      contactPersons = await contactPersonRepository
        .createQueryBuilder('contactPerson')
        .where("contactPerson.value ->> 'DebtorID' IN (:...debtorIds)", {
          debtorIds,
        })
        .andWhere('contactPerson.isDebtor is true')
        .getMany();
    }

    for (const contactPerson of contactPersons) {
      if (contactPerson.value) {
        debtorIdToCpId[contactPerson.value.DebtorID] = Number(contactPerson.id);
      }
    }

    const map: { [key: number]: number } = {};
    const pool = await debtorRepository.getPoolDebtorIds(cases.length);
    const poolCaseIDs = await caseRepository.getPoolCaseIds(cases.length);
    const poolContactPersonIDs = await contactPersonRepository.getPoolIds(
      cases.length,
    );

    for (const [index, row] of cases.entries()) {
      if (!map[row.GroupID]) {
        const caseModelOld = this.findEntityById(casesToClone, row.CaseID);
        const debtorModelNew = Object.assign({}, caseModelOld.debtor);
        debtorModelNew['id'] = String(pool[index]);

        if (debtorIdToCpId[Number(caseModelOld.debtorId)]) {
          const contactPersonModelNew = {
            id: String(poolContactPersonIDs[index]),
            typeId: 1,
            lastName: caseModelOld.debtor.lastName,
            firstName: caseModelOld.debtor.firstName,
            middleName: caseModelOld.debtor.middleName,
            passportNumber: caseModelOld.debtor.passportNumber,
            passportInfo: caseModelOld.debtor.passportInfo,
            taxNumber: caseModelOld.debtor.taxNumber,
            birthDate: caseModelOld.debtor.birthDate,
            isDebtor: true,
            value: { DebtorID: Number(pool[index]) },
          };

          const contactPersonRelationAttributes = {
            parentContactPersonId:
              debtorIdToCpId[Number(caseModelOld.debtorId)],
            contactPersonId: poolContactPersonIDs[index],
          };

          const contactPersonModel = contactPersonRepository.create(
            contactPersonModelNew,
          );

          const contactPersonRelationModel =
            contactPersonRelationRepository.create(
              contactPersonRelationAttributes,
            );
          importContactPersonData.push(contactPersonModel);
          importContactPersonRelationData.push(contactPersonRelationModel);
        }

        const { debtor, ...caseModelNew } = caseModelOld;

        caseModelNew['debtorId'] = String(pool[index]);
        caseModelNew['packageId'] = !row.PackageID ? packageID : row.PackageID;
        caseModelNew['id'] = String(poolCaseIDs[index]);

        const courtProcessAttributes: Partial<CourtProcess> = {
          caseId: Number(poolCaseIDs[index]),
          insertedUserId: Number(userId),
          isActual: 1,
        };

        const debtorModel = debtorRepository.create(debtorModelNew);

        const caseModel = caseRepository.create(caseModelNew);
        const courtProcessModel = courtProcessRepository.create(
          courtProcessAttributes,
        );

        importDebtorData.push(debtorModel);
        importCaseData.push(caseModel);
        importCourtProcessData.push(courtProcessModel);

        casesPreLegal.push(row.CaseID);
        map[row.GroupID] = Number(poolCaseIDs[index]);
        casesLegal.push(Number(poolCaseIDs[index]));
      } else {
        poolCaseIDs[index] = map[row.GroupID];
      }

      const legalUAAttributes: Partial<LegalInvoice> = {
        invoiceId: Number(row.InvoiceID),
        caseId: Number(poolCaseIDs[index]),
        insertedUserId: Number(userId),
      };

      results.push({
        invoiceId: legalUAAttributes.invoiceId,
        LegalCaseID: legalUAAttributes.caseId,
        insertedUserId: legalUAAttributes.insertedUserId,
        parentCaseID: row.CaseID,
      });

      const legalInvoiceModel = this.create(legalUAAttributes);
      importLegalInvoiceData.push(legalInvoiceModel);
    }

    return {
      importDebtorData,
      importCaseData,
      importContactPersonData,
      importContactPersonRelationData,
      importLegalInvoiceData,
      importCourtProcessData,
      casesLegal,
      casesPreLegal,
      results,
    };
  }

  public async createLegalInvoices(
    cases: LegalInvoicePreImportDataDto[],
    userId: number,
    connection: AmqpConnectionManager,
    serviceParameterRepository: ServiceParameterRepository,
  ): Promise<any> {
    const result: { success: boolean; report: any[] } = {
      success: true,
      report: [],
    };
    const results = [];
    const importLegalInvoiceData = [];
    const casesLegal: number[] = [];
    const casesPreLegal: number[] = [];
    const queryRunner = this.manager.connection.createQueryRunner('master');
    try {
      for (const row of cases) {
        casesPreLegal.push(row.CaseID);
        const legalUAAttributes: Partial<LegalInvoice> = {
          invoiceId: Number(row.InvoiceID),
          caseId: Number(row.LegalCaseID),
          insertedUserId: Number(userId),
        };
        const legalInvoiceModel = this.create(legalUAAttributes);
        importLegalInvoiceData.push(legalInvoiceModel);
        results.push(legalUAAttributes);
      }
      await queryRunner.manager.save(LegalInvoice, importLegalInvoiceData);
      await this.changeStageAndStatuses(
        serviceParameterRepository,
        connection,
        casesLegal,
        casesPreLegal,
      );
    } catch (error) {
      result.success = false;
      throw error;
    } finally {
      await queryRunner.release();
    }

    if (result.success) {
      result.report = results.flat();
    }

    return result;
  }

  getUniqueCaseIDs(data: LegalCasePreImportDataDto[]): number[] {
    // Create a Set to store unique CaseIDs
    const uniqueCaseIDsSet = new Set<number>();
    // Iterate over the array and add each CaseID to the Set
    for (const item of data) {
      uniqueCaseIDsSet.add(item.CaseID);
    }
    // Convert the Set back to an array and return it
    return [...uniqueCaseIDsSet];
  }

  findEntityById(entities: Case[], id: number): Case {
    // Find the entity in the array by its ID
    const entity = entities.find((entity) => Number(entity.id) === id);

    // If entity is not found, throw NotFoundException
    if (!entity) {
      throw new NotFoundException(`Entity with ID ${id} not found`);
    }

    return entity;
  }

  public async changeStageAndStatuses(
    serviceParameterRepository: ServiceParameterRepository,
    connection: AmqpConnectionManager,
    caseIdsLegal: number[],
    caseIdsPreLegal: number[],
  ) {
    const systemUserId =
      await serviceParameterRepository.getGlobalParameterValueByName<number>(
        'SystemUserID',
      );
    const changeStatusHandler = new ChangeStatusHandler(
      connection,
      systemUserId ?? 1,
    );
    if (caseIdsLegal.length > 0) {
      const historyResultPreparationOfTheLawsuit =
        await serviceParameterRepository.getGlobalParameterValueByName<number>(
          'historyResultPreparationOfTheLawsuit',
        );
      const statusReasonPreparationOfTheLawsuit =
        await serviceParameterRepository.getGlobalParameterValueByName<number>(
          'statusReasonPreparationOfTheLawsuit',
        );
      const legalStageID =
        await serviceParameterRepository.getGlobalParameterValueByName<number>(
          'legalStageID',
        );
      changeStatusHandler.changeStatus(
        caseIdsLegal,
        historyResultPreparationOfTheLawsuit,
        statusReasonPreparationOfTheLawsuit ?? null,
        1,
      );
      const changeStageHandler = new ChangeStageHandler(
        connection,
        systemUserId ?? 1,
      );
      changeStageHandler.changeStage(caseIdsLegal, legalStageID);
    }
    if (caseIdsPreLegal.length > 0) {
      const historyResultInWork =
        await serviceParameterRepository.getGlobalParameterValueByName<number>(
          'historyResultInWork',
        );
      const legalControlStageID =
        await serviceParameterRepository.getGlobalParameterValueByName<number>(
          'changeCaseToLegalStageID',
        );
      if (legalControlStageID) {
        changeStatusHandler.changeStatus(
          caseIdsPreLegal,
          historyResultInWork,
          null,
          1,
        );
        const changeStageHandler = new ChangeStageHandler(
          connection,
          systemUserId ?? 1,
        );
        changeStageHandler.changeStage(caseIdsPreLegal, legalControlStageID);
      }
    }
  }
}
