import { EntityRepository, getManager, Repository } from 'typeorm';
import { EventRuleLog } from '../../entities/list/event-rule-log.entity';

@EntityRepository(EventRuleLog)
export class EventRuleLogRepository extends Repository<EventRuleLog> {
  public async getPoolIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"List"."EventRuleExecutionLog_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "List"."EventRuleExecutionLog_ID_seq") as last_sequence`,
      [count],
    );

    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
