import { EntityRepository, Repository } from 'typeorm';
import { CourtType } from '../../entities/legal-ua/court-type.entity';

@EntityRepository(CourtType)
export class CourtTypeRepository extends Repository<CourtType> {
  public async getList(): Promise<Pick<CourtType, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dst')
      .select(['dst."ID" as "id"', 'dst."Name" as "name"'])
      .where('dst."IsDeleted" = 0');

    return queryBuilder.execute();
  }
}
