import { EntityRepository, Repository } from 'typeorm';

import { ServiceParameter } from 'src/entities/dictionary/service-parameter.entity';

const GLOBAL_CONFIG_ID = 12;
const CASE_CONFIG_ID = 1;
const IMPORT_SERVICE_ID = 4;
@EntityRepository(ServiceParameter)
export class ServiceParameterRepository extends Repository<ServiceParameter> {
  public async getServiceParameterByName<T>(
    name: string,
  ): Promise<T | undefined> {
    const parameter = await this.findOne({
      select: ['value'],
      where: { name },
    });
    return parameter ? (parameter.value as T) : undefined;
  }

  public async getGlobalParameterValueByName<T>(
    name: string,
  ): Promise<T | undefined> {
    return this.getParameterValueByNameAndService(name, GLOBAL_CONFIG_ID);
  }

  public async getCaseServiceParameterValueByName<T>(
    name: string,
  ): Promise<T | undefined> {
    return this.getParameterValueByNameAndService(name, CASE_CONFIG_ID);
  }

  public async getImportServiceParameterValueByName<T>(
    name: string,
  ): Promise<T | undefined> {
    return this.getParameterValueByNameAndService(name, IMPORT_SERVICE_ID);
  }

  private async getParameterValueByNameAndService<T>(
    name: string,
    serviceId: number,
  ): Promise<T | undefined> {
    const parameter = await this.findOne({
      select: ['value'],
      where: { name, serviceId },
    });
    return parameter ? (parameter.value as T) : undefined;
  }
}
