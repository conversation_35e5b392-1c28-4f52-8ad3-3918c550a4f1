import { EntityRepository, Repository } from 'typeorm';

import { PartyType } from 'src/entities/dictionary/party-type.entity';

@EntityRepository(PartyType)
export class PartyTypeRepository extends Repository<PartyType> {
  public async getList(filter = ''): Promise<Pick<PartyType, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dpt')
      // eslint-disable-next-line radar/no-duplicate-string
      .select(['dpt."ID" as "id"', 'dpt."Name" as "name"'])
      // eslint-disable-next-line radar/no-duplicate-string
      .where('dpt."IsDeleted" = 0');

    if (filter === 'contact-person') {
      queryBuilder.andWhere(
        "dpt.\"Availability\"->>'ContactPerson'::text = '1'",
      );
    }

    //   switch ($filter) {
    //     case 'croatia':
    //         $query->where(['IsDeleted' => 0])
    //             ->andWhere('"Availability" ->> \'Custom004DebtorNetworkID\' = \'1\'')
    //             ->orderBy('ID');
    //         break;
    //     case 'side2':
    //         $query->where(['IsDeleted' => 0])
    //             ->andWhere('"Availability" ->> \'Phone2ImportPackage\' = \'1\'');
    //         break;
    // }

    return queryBuilder.execute();
  }

  public async getPartyTypeList(
    names?: Array<string | unknown>,
  ): Promise<PartyType[]> {
    const queryBuilder = this.createQueryBuilder('dpt')
      .select(['dpt."ID" as "id"', 'dpt."Name" as "name"'])
      .where('dpt."IsDeleted" = 0')
      .andWhere('"Availability" -> \'Phone\' @> 1 :: TEXT :: JSONB');

    if (names) {
      queryBuilder.andWhere('dpt."Name" IN (:...partyTypes)', {
        partyTypes: [...names],
      });
    }

    return queryBuilder.execute();
  }

  public async getPartyType2List(): Promise<Pick<PartyType, 'id' | 'name'>[]> {
    const queryBuilder = this.createQueryBuilder('dpt')
      .select(['dpt."ID" as "id"', 'dpt."Name" as "name"'])
      .where('dpt."IsDeleted" = 0')
      .andWhere("\"Availability\" ->> 'Phone2ImportMaskedPhone' = '1'");

    return queryBuilder.execute();
  }
}
