import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class ComplaintDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  @IsNumber()
  public BasisForPaymentID: number;

  @Expose()
  @IsString()
  public ComplaintDate: string;

  @Expose()
  @IsString()
  public ReceivedDocumentation: string | boolean | null | number;

  @Expose()
  @IsString()
  public Expires: string | boolean | null | number;

  @Expose()
  @IsString()
  public Suspension: string;

  @Expose()
  @IsString()
  public Complaint: string | boolean | number | null;

  @Expose()
  @IsString()
  public FinalJudgment: string;

  @Expose()
  @IsString()
  public SentForDocumentation: string;

  @Expose()
  @IsString()
  public DocumentationSentToLawyer: string | boolean | null | number;

  @Expose()
  @IsString()
  public Foreigner: string | boolean | number | null;

  @Expose()
  @IsString()
  public FirstInstanceDecision: string;

  @Expose()
  @IsString()
  public SecondInstanceDecision: string;

  @Expose()
  @IsString()
  public Result: string | boolean | number | null;
}
