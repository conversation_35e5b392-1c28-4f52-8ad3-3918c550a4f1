import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { ComplaintDTO } from './complaint.dto';

export class ImportComplaintDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: ComplaintDTO[];

  importFile: string;
}
