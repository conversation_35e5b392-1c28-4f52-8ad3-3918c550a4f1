import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { HttpMethod } from '../common/enums/http-method.enum';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { Case } from '../entities/data/case.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { Custom004EEnforcementComplaint } from '../entities/legal/custom004-enforcement-complaint.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportBasisComplaintPublisher } from '../events/publishers/import-basis-complaint-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { AvailabilityOptionRepository } from '../repositories/dictionary/availability-option.repository';
import { Custom004EEnforcementComplaintRepository } from '../repositories/legal/custom004-enforcement-complaint.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ComplaintDTO } from './dto/complaint.dto';

const INSERTED_COMPLAINS_NUMBER = 'Number of inserted complaints';

@Injectable()
export class ImportBasisComplaintService extends BaseImportService<ComplaintDTO> {
  catalog: CatalogEnum = CatalogEnum.BasisComplaint;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.BasisComplaint;
  protected publisher: ImportPublisher<any> = new ImportBasisComplaintPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly custom004EEnforcementComplaintRepository: Custom004EEnforcementComplaintRepository,
    protected readonly availabilityOptionRepository: AvailabilityOptionRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertComplaint = result?.insertComplaint;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_COMPLAINS_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_COMPLAINS_NUMBER]:
        this.resultDetalization[id][0][INSERTED_COMPLAINS_NUMBER],
    };

    if (insertComplaint.length > 0) {
      const suspendCases = insertComplaint.map(
        (item: Custom004EEnforcementComplaint) => item.CaseID,
      );
      const suspendStatus =
        (await this.serviceParameterRepository.getServiceParameterByName<number>(
          'LegalSuspendedStatusID',
        )) ?? 47;
      await this.custom004EEnforcementComplaintRepository.save(insertComplaint);

      const filteredSuspendedCases = await this.applyStatusTransition(
        suspendCases,
        suspendStatus,
        1,
      );

      await queryRunner.manager.update(
        Case,
        { id: In(filteredSuspendedCases) },
        { statusId: suspendStatus },
      );
      detalization[INSERTED_COMPLAINS_NUMBER] += insertComplaint.length;
    }

    this.resultDetalization[id][0][INSERTED_COMPLAINS_NUMBER] =
      detalization[INSERTED_COMPLAINS_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};
    data.ReceivedDocumentation = ['Yes', 'No', 'Partially'];
    data.Expires = ['Yes', 'No', 'Partially'];
    data.Complaint = ['Yes', 'No'];
    data.DocumentationSentToLawyer = ['Yes', 'No', 'Partially'];
    data.Foreigner = ['Yes', 'No', 'Partially'];
    data.Result = ['Yes', 'No', 'Partially'];

    return data;
  }

  private async applyStatusTransition(
    cases: number[],
    statusId: number,
    toolSourceId: number,
  ): Promise<number[]> {
    try {
      const useTransition =
        await this.serviceParameterRepository.getGlobalParameterValueByName(
          'useStatusTransitionFunction',
        );
      if (useTransition) {
        const filteredCases: number[] = [];
        const mapping: any = await this.apiClient.sendRequest(
          HttpMethod.POST,
          'http://action-node-service/case-status-transition/check',
          {
            timeout: 5000,
            body: {
              CaseIDs: cases,
              ToolSourceID: toolSourceId,
              StatusID: statusId,
            },
          },
        );

        for (const caseID of cases) {
          if (
            mapping.hasOwnProperty(caseID) &&
            mapping[caseID]['IsAllowed'] === false
          ) {
            // skip
          } else {
            filteredCases.push(caseID);
          }
        }
        return filteredCases;
      }
      return cases;
    } catch (error) {
      console.log(error);
    }
    return [];
  }
}
