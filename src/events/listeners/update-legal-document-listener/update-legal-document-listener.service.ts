import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportWithParameterListenerService } from '../../../common/imports/listeners/base-import-with-parameter-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { DocumentParameterRepository } from '../../../repositories/data/document-parameter.repository';
import { UpdateLegalDocumentDto } from '../../../update-legal-document/dto/update-legal-document.dto';
import { DocumentRepository } from '../../../repositories/data/document.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { UpdateLegalDocumentEvent } from '../../types/update-legal-document-event';
import { LegalDocumentPreImportDataDto } from './dto/legal-document-pre-import-data.dto';

@Injectable()
export class UpdateLegalDocumentListenerService extends BaseImportWithParameterListenerService<
  UpdateLegalDocumentEvent,
  LegalDocumentPreImportDataDto
> {
  queueName: Queues.UpdateLegalDocument = Queues.UpdateLegalDocument;
  catalog: CatalogNameEnum = CatalogNameEnum.UpdateLegalDocument;
  catalogId: CatalogEnum = CatalogEnum.UpdateLegalDocument;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly documentRepository: DocumentRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly documentParameterRepository: DocumentParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
      parameterRepository,
    );
    this.listen();
  }

  async preImport(
    importData: UpdateLegalDocumentDto['importData'],
    userId: number,
    uploadHistoryId: string,
  ): Promise<LegalDocumentPreImportDataDto[]> {
    return this.formatParameters(importData, userId, uploadHistoryId);
  }

  async messageHandler(
    importData: LegalDocumentPreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
    additionalParams: { typeId: number },
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const parametersToCreate: any[] = [];
      const parametersToDelete: string[] = [];
      const activateAppellateCourtMapping: any[] = [];
      const skippedRows: number[] = [];
      const documentParameterToCaseID: any = {};

      const chunks: LegalDocumentPreImportDataDto[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const slugToId = await this.getParameterBySlug();
      const parametersHasActivateCourtMapping =
        await this.parameterRepository.getParametersHasActivateCourtMapping();

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const [chunkIndex, chunk] of chunks.entries()) {
        const existsDocumentIDToCase = await this.checkExistsDocumentToCase(
          chunk,
        );

        const documentToParameterMapping =
          await this.getDocumentToParameterMapping(chunk, slugToId);

        for (const [index, row] of chunk.entries()) {
          const documentId = row.DocumentID;
          if (
            existsDocumentIDToCase[documentId] &&
            existsDocumentIDToCase[documentId] == row.LegalCaseID
          ) {
            const parameters = Object.keys(row).filter(
              (c) => !['LegalCaseID', 'DocumentID'].includes(c),
            );

            for (const parameter of parameters) {
              const parameterId = slugToId[parameter];

              if (row[parameter] === '') {
                continue;
              }

              if (
                documentToParameterMapping[documentId] &&
                documentToParameterMapping[documentId][parameterId]
              ) {
                parametersToDelete.push(
                  documentToParameterMapping[documentId][parameterId],
                );
              }

              if (String(row[parameter]).toLowerCase() !== 'null') {
                const documentParameter = {
                  documentId,
                  parameterId,
                  value: row[parameter],
                };
                parametersToCreate.push(documentParameter);
                documentParameterToCaseID[documentId] = row.LegalCaseID;

                if (
                  this.hasAppellateCourtMapping(
                    row[parameter],
                    parameterId,
                    parametersHasActivateCourtMapping,
                  )
                ) {
                  activateAppellateCourtMapping.push(row.LegalCaseID);
                }
              }
            }
          } else {
            const offset = page > 1 ? (page - 1) * 1000 : 1;
            skippedRows.push(index * (1 + chunkIndex) * offset);
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
        await this.createLegalContactInHistoryForParameters(chunk, userId);
      }

      const casesToActivateLegalRules = importData.map((r) => r.LegalCaseID);
      await this.saveResult(Number(uploadHistoryId), page, {
        parametersToCreate,
        parametersToDelete,
        activateAppellateCourtMapping,
        documentParameterToCaseID,
        skippedRows,
        metaData: {
          userId,
        },
        casesToActivateLegalRules,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }

  async checkExistsDocumentToCase(
    rows: LegalDocumentPreImportDataDto[],
  ): Promise<{ [key: string]: number }> {
    const documentIDs = rows.map((index) => index.DocumentID);
    const documents = await this.documentRepository.find({
      select: ['id', 'levelValue'],
      where: {
        id: In(documentIDs),
        isDeleted: 0,
        levelId: 4,
      },
    });

    const documentsMap: { [key: string]: number } = {};
    for (const document of documents) {
      documentsMap[document.id] = document.levelValue;
    }

    return documentsMap;
  }

  async getDocumentToParameterMapping(
    rows: LegalDocumentPreImportDataDto[],
    slugToId: { [key: string]: number },
  ): Promise<{
    [key: number]: { [key: number]: string };
  }> {
    const slugs = Object.keys(rows[0]).filter(
      (index) => !['LegalCaseID', 'DocumentID'].includes(index),
    );
    const parameterIDs = slugs.map((index) => slugToId[index]);
    const documentIDs = rows.map((index) => index.DocumentID);

    const documentToParameterMapping: {
      [key: number]: { [key: number]: string };
    } = {};

    const parameters = await this.documentParameterRepository.find({
      parameterId: In(parameterIDs),
      documentId: In(documentIDs),
      isDeleted: 0,
    });

    for (const parameter of parameters) {
      if (!documentToParameterMapping[parameter.documentId]) {
        documentToParameterMapping[parameter.documentId] = {};
      }
      documentToParameterMapping[parameter.documentId][parameter.parameterId] =
        parameter.id;
    }

    return documentToParameterMapping;
  }
}
