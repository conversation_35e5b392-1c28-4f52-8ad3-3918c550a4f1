import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom005CaseAdditionalInfoRepository } from '../../../repositories/data/custom005-case-additional-info.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { DocumentParameterRepository } from '../../../repositories/data/document-parameter.repository';
import { DocumentRepository } from '../../../repositories/data/document.repository';
import { DebtorTypeRepository } from '../../../repositories/dictionary/debtor-type.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { TransactionRepository } from '../../../repositories/financial/transaction.repository';
import { EventsGateway } from '../../events.gateway';
import { UpdateLegalDocumentListenerService } from './update-legal-document-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      DebtorRepository,
      HistoryRepository,
      DebtorTypeRepository,
      Custom005CaseAdditionalInfoRepository,
      TransactionRepository,
      DocumentRepository,
      DocumentParameterRepository,
      ParameterRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [UpdateLegalDocumentListenerService, EventsGateway],
})
export class UpdateLegalDocumentListenerModule {}
