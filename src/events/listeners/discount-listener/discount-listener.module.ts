import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { InvoiceDiscountRepository } from '../../../repositories/data/invoice-discount.repository';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { DiscountListenerService } from './discount-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      UploadHistoryRepository,
      HistoryRepository,
      ServiceParameterRepository,
      InvoiceRepository,
      InvoiceDiscountRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [DiscountListenerService, EventsGateway],
})
export class DiscountListenerModule {}
