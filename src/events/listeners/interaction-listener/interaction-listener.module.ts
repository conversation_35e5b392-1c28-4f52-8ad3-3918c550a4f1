import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ApiClient } from '../../../common/api.client';
import { CaseRepository } from '../../../repositories/data/case.repository';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { InteractionListenerService } from './interaction-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      ServiceParameterRepository,
      UploadHistoryRepository,
      CaseRepository,
      InvoiceRepository,
    ]),
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [InteractionListenerService, EventsGateway, ApiClient],
})
export class InteractionListenerModule {}
