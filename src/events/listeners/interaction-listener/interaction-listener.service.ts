import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import path from 'path';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { In } from 'typeorm';
import { appendToFile } from '../../../common/helpers/append-to-file';
import { checkIfExists } from '../../../common/helpers/check-if-exists';
import { mkDir } from '../../../common/helpers/mk-dir';
import { Case } from '../../../entities/data/case.entity';
import { ImportDataDTO } from '../../../import-interaction/dto/import-data.dto';
import { UploadDTO } from '../../../import-interaction/dto/upload.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { CaseRepository } from '../../../repositories/data/case.repository';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { InteractionEvent } from '../../types/interaction-event';
import { ImportListener } from '../import-listener';

@Injectable()
export class InteractionListenerService extends ImportListener<
  InteractionEvent,
  UploadDTO
> {
  queueName: Queues.Interaction = Queues.Interaction;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly httpService: HttpService,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly caseRepository: CaseRepository,
    private readonly invoiceRepository: InvoiceRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(importData: UploadDTO[]): Promise<UploadDTO[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => void {
    // const updateImportProgressPublisher = new UpdateImportProgressPublisher(
    //   this.connection,
    // );
    return (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
      // updateImportProgressPublisher.publish({
      //   event: 'UpdateImportProgress',
      //   payload,
      // });
    };
  }

  async messageHandler(
    importData: UploadDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    if (!uploadHistory || (uploadHistory && !uploadHistory.importListId)) {
      return false;
    }

    const importParams = importData[0];

    const failed: string[] = [];
    const success: string[] = [];
    const errors: { [key: string]: any } = {};

    const casesMapping = await this.getCaseListMapping(
      importParams.importData,
      importParams.type,
      importParams.ActionForMultipleCases,
    );

    const noteMapping = this.getNoteListMapping(
      importParams.importData,
      importParams.type,
    );

    const creationDateMapping = this.getCreationDateListMapping(
      importParams.importData,
      importParams.type,
    );

    const cases = Object.keys(casesMapping);

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    let currentCaseNumber = 0;
    let previousPercent = 0;

    const searchServiceURL = this.config.get<string>('searchService.url');

    for (const caseID of cases) {
      if (!caseID) {
        continue;
      }

      const jsonBody = Object.assign(importParams.params, {
        userID: userId,
        CaseID: Number(caseID),
        content: noteMapping[casesMapping[caseID]] || null,
        CreationDateTime: creationDateMapping[casesMapping[caseID]] || null,
      });

      try {
        const response = await this.httpService
          .post(searchServiceURL + '/case-history', jsonBody)
          .toPromise();
        success.push(caseID);
      } catch (error: any) {
        if (error.response?.status === 422) {
          failed.push(caseID);
          errors[caseID] = JSON.stringify(error.response.data);
        } else {
          console.log(error);
        }
      }

      const progress = Math.round((currentCaseNumber / cases.length) * 100);
      if (previousPercent !== progress) {
        updateImportProgress(progress);
        previousPercent = progress;
      }
      currentCaseNumber++;
    }

    await this.saveImportDetails(
      uploadHistory!.id,
      success,
      failed,
      errors,
      userId,
      casesMapping,
      importParams.type,
    );

    updateImportProgress(100);

    await (failed.length === 0
      ? this.uploadHistoryRepository.update(
          { id: uploadHistory!.id },
          { statusId: UploadStatus.Finished },
        )
      : this.uploadHistoryRepository.update(
          { id: uploadHistory!.id },
          { statusId: UploadStatus.Failed },
        ));

    return true;
  }

  async getCaseListMapping(
    values: ImportDataDTO[],
    type: string,
    action: string,
  ): Promise<{ [key: string]: number | string }> {
    const mapping: { [key: string]: number | string } = {};
    const inverseMapping: { [key: string]: string[] } = {};
    let filteredItems: { [key: string]: (string | number)[] } = {};

    switch (type) {
      case 'case':
        for (const caseId of values.map((index) => index.CaseID)) {
          if (caseId) {
            mapping[caseId] = caseId;
          }
        }
        break;
      case 'client':
        const contragentCaseIDs = values.map((index) => index.ClientCaseID);
        for (const index of await this.caseRepository.find({
          select: ['id', 'contragentCaseId'],
          where: {
            contragentCaseId: In(contragentCaseIDs),
          },
        })) {
          if (index.contragentCaseId) {
            if (inverseMapping[index.contragentCaseId]) {
              inverseMapping[index.contragentCaseId].push(index.id);
            } else {
              inverseMapping[index.contragentCaseId] = [index.id];
            }
          }
        }

        filteredItems = await this.applyActionForMultipleCases(
          inverseMapping,
          action,
        );

        for (const key in filteredItems) {
          for (const caseId of filteredItems[key]) {
            mapping[caseId] = key;
          }
        }
        break;
      case 'invoice':
        const invoiceNums = values.map((index) => index.InvoiceNum);
        for (const index of await this.invoiceRepository.find({
          select: ['caseId', 'invoiceNum'],
          where: {
            invoiceNum: In(invoiceNums),
          },
        })) {
          if (index.invoiceNum) {
            if (inverseMapping[index.invoiceNum]) {
              inverseMapping[index.invoiceNum].push(index.caseId);
            } else {
              inverseMapping[index.invoiceNum] = [index.caseId];
            }
          }
        }

        filteredItems = await this.applyActionForMultipleCases(
          inverseMapping,
          action,
        );

        for (const key in filteredItems) {
          for (const caseId of filteredItems[key]) {
            mapping[caseId] = key;
          }
        }
        break;
    }

    return mapping;
  }

  getNoteListMapping(
    values: ImportDataDTO[],
    type: string,
  ): { [key: string]: number | string } {
    const mapping: { [key: string]: number | string } = {};

    switch (type) {
      case 'case':
        for (const item of values) {
          if (item.CaseID && item.Note) {
            mapping[item.CaseID] = item.Note;
          }
        }
        break;
      case 'client':
        for (const item of values) {
          if (item.ClientCaseID && item.Note) {
            mapping[item.ClientCaseID] = item.Note;
          }
        }
        break;
      case 'invoice':
        for (const item of values) {
          if (item.InvoiceNum && item.Note) {
            mapping[item.InvoiceNum] = item.Note;
          }
        }
        break;
    }

    return mapping;
  }

  getCreationDateListMapping(
    values: ImportDataDTO[],
    type: string,
  ): { [key: string]: number | string } {
    const mapping: { [key: string]: number | string } = {};

    switch (type) {
      case 'case':
        for (const item of values) {
          if (item.CaseID && item.CreationDateTime) {
            mapping[item.CaseID] = item.CreationDateTime;
          }
        }
        break;
      case 'client':
        for (const item of values) {
          if (item.ClientCaseID && item.CreationDateTime) {
            mapping[item.ClientCaseID] = item.CreationDateTime;
          }
        }
        break;
      case 'invoice':
        for (const item of values) {
          if (item.InvoiceNum && item.CreationDateTime) {
            mapping[item.InvoiceNum] = item.CreationDateTime;
          }
        }
        break;
    }

    return mapping;
  }

  private async applyActionForMultipleCases(
    items: { [key: string]: (number | string)[] },
    action: string,
  ) {
    const filtered: { [key: string]: (string | number)[] } = {};
    switch (action) {
      case 'dont_upload':
        for (const key in items) {
          if (items[key].length === 1) {
            filtered[key] = items[key];
          }
        }
        return filtered;
      case 'upload_recent':
        let caseIDs: (number | string)[] = [];
        for (const key in items) {
          caseIDs = [...caseIDs, ...items[key]];
        }
        const uniqueCases = new Set(caseIDs);
        let cases: Case[] = [];
        if (uniqueCases.size > 0) {
          cases = await this.caseRepository
            .createQueryBuilder('dc')
            .where('dc."ID" IN (:...ids)', {
              ids: [...uniqueCases],
            })
            .orderBy('dc."Inserted"', 'DESC')
            .getMany();
        }

        const activeCases = new Set(
          cases
            .filter((caseModel) => caseModel.caseProcess === 1)
            .map((caseModel) => Number(caseModel.id)),
        );

        const inactiveCases = new Set(
          cases
            .filter((caseModel) => caseModel.caseProcess !== 1)
            .map((caseModel) => Number(caseModel.id)),
        );

        attachmentsLoop: for (const key in items) {
          if (items[key].length > 1) {
            for (const caseID of items[key]) {
              if (activeCases.has(Number(caseID))) {
                items[key] = [caseID];
                continue attachmentsLoop;
              }
            }
            for (const caseID of items[key]) {
              if (inactiveCases.has(Number(caseID))) {
                items[key] = [caseID];
                continue attachmentsLoop;
              }
            }
          }
        }

        return items;
      case 'upload_all':
      default:
        return items;
    }
  }

  async saveImportDetails(
    historyId: string,
    success: string[],
    failed: string[],
    errors: { [key: string]: any },
    userId: number,
    casesMapping: { [key: string]: any },
    type: string,
  ) {
    let searchType = 'CaseID';
    switch (type) {
      case 'client':
        searchType = 'ClientCaseID';
        break;
      case 'invoice':
        searchType = 'InvoiceNum';
        break;
    }

    const relativePath = `output/${userId}`;
    const filePath = path.join(relativePath, `${historyId}.csv`);
    const resultPath = path.join(process.cwd(), relativePath);
    if (!(await checkIfExists(resultPath))) {
      await mkDir(resultPath, {
        recursive: true,
      });
    }
    const data: { [key: string]: any }[] = [];
    const failedNames = new Set(failed.map((caseId) => caseId));
    const filteredSuccess = success.filter((caseId) => {
      return !failedNames.has(caseId);
    });

    for (const caseId of failed) {
      let item: { [key: string]: any } = {
        CaseID: caseId,
        Status: 'error',
        Message: errors[caseId],
      };
      if (searchType !== 'CaseID') {
        item = Object.assign({ [searchType]: casesMapping[caseId] }, item);
      }
      data.push(item);
    }

    for (const caseId of filteredSuccess) {
      let item: { [key: string]: any } = {
        CaseID: caseId,
        Status: 'Ok',
        Message: null,
      };
      if (searchType !== 'CaseID') {
        item = Object.assign({ [searchType]: casesMapping[caseId] }, item);
      }
      data.push(item);
    }

    const csvBody = this.generateCsv(data);
    await appendToFile(filePath, csvBody, 'utf8');

    await this.uploadHistoryRepository.update(
      { id: historyId },
      { resultFilePath: filePath },
    );
  }
}
