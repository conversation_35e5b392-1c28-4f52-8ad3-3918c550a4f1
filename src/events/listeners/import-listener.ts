import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel, ConsumeMessage } from 'amqplib';
import path from 'path';
import { BaseImportEvent } from 'src/events/types/base-import-event.interface';
import { appendToFile } from '../../common/helpers/append-to-file';
import { checkIfExists } from '../../common/helpers/check-if-exists';
import { mkDir } from '../../common/helpers/mk-dir';

type ImportDataType<T extends BaseImportEvent> = T['data']['importData'];
type PreImportType<T extends BaseImportEvent, K> = K extends undefined
  ? ImportDataType<T>
  : K[];

export abstract class ImportListener<T extends BaseImportEvent, K = undefined> {
  abstract queueName: T['queueName'];
  public prefetchNumber = 5;
  protected channel: ChannelWrapper;
  protected connection: AmqpConnectionManager;

  abstract preImport?(
    data: ImportDataType<T>,
    userId: number,
    uploadHistoryId: string,
  ): Promise<PreImportType<T, K>>;
  abstract messageHandler(
    importData: PreImportType<T, K>,
    userId: T['data']['userId'],
    uploadHistoryId: T['data']['uploadHistoryId'],
    chunk: number,
    additionalParams: any,
  ): Promise<boolean>;
  abstract postImport?(): any;

  constructor(connection: AmqpConnectionManager) {
    this.connection = connection;
    this.channel = this.connection.createChannel({
      setup: (channel: ConfirmChannel) =>
        Promise.all([
          channel.assertQueue(this.queueName, {
            durable: true,
          }),
          channel.prefetch(this.prefetchNumber),
          // eslint-disable-next-line @typescript-eslint/no-misused-promises
          channel.consume(this.queueName, this.onMessage.bind(this), {
            noAck: false,
          }),
        ]),
    });
  }

  async listen(prefetchMessagesNumber = 0) {
    // this.channel = this.connection.createChannel({
    //   setup: (channel: ConfirmChannel) =>
    //     Promise.all([
    //       channel.assertQueue(this.queueName, {
    //         durable: true,
    //       }),
    //       prefetchMessagesNumber > 0
    //         ? channel.prefetch(prefetchMessagesNumber)
    //         : null,
    //       channel.consume(this.queueName, this.onMessage.bind(this), {
    //         noAck: false,
    //       }),
    //     ]),
    // });
  }

  async onMessage(message: ConsumeMessage | null): Promise<void> {
    if (!message) {
      return;
    }

    const data = this.parseMessage(message);
    const { importData, userId, uploadHistoryId, chunk, additionalParams } =
      data;

    const preImportData = this.preImport
      ? await this.preImport(importData, userId, uploadHistoryId)
      : (importData as PreImportType<T, K>);

    try {
      const isCompleted = await this.messageHandler(
        preImportData,
        userId,
        uploadHistoryId,
        chunk ?? 1,
        additionalParams ?? null,
      );

      if (isCompleted) {
        this.channel.ack(message);
      } else {
        this.channel.nack(message, undefined, false);
      }
    } catch (error) {
      console.log(error);
    }
  }

  private parseMessage(message: ConsumeMessage): T['data'] {
    const data = message.content;

    return typeof data === 'string'
      ? JSON.parse(data)
      : JSON.parse(data.toString('utf8'));
  }

  generateCsv(data: any[]) {
    const headers = Object.keys(data[0] ?? []);
    const headersString = headers.join(';') + '\n';
    let csvBody = '';
    csvBody = data
      .map((row) => {
        return headers.map((column) => row[column]).join(';');
      })
      .join('\n');
    return '\uFEFF' + headersString + csvBody;
  }

  protected async saveReport(report: any[], userId: number, historyId: string) {
    const relativePath = this.getReportPath(userId);
    const filePath = path.join(relativePath, `${historyId}.csv`);
    const resultPath = path.join(process.cwd(), relativePath);
    if (!(await checkIfExists(resultPath))) {
      await mkDir(resultPath, {
        recursive: true,
      });
    }

    const csvBody = this.generateCsv(report);
    await appendToFile(filePath, csvBody, 'utf8');
    return filePath;
  }

  protected getReportPath(userId: number): string {
    return `output/${userId}`;
  }
}
