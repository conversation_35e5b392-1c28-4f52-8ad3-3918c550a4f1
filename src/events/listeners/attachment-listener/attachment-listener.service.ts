/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-assertion */

import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import FormData from 'form-data';
import * as fs from 'fs';
import { Redis } from 'ioredis';

// eslint-disable-next-line unicorn/import-style
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { checkIfExists } from '../../../common/helpers/check-if-exists';
import { rmDir } from '../../../common/helpers/rm-dir';
import { REDIS } from '../../../common/redis/constants';
import { AttachmentDTO } from '../../../import-attachment/dto/attachment.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { Custom005DocumentRepository } from '../../../repositories/data/custom005-document.repository';
import { DocumentAdditionalInfoRepository } from '../../../repositories/data/document-additional-info.repository';
import { DocumentNoteRepository } from '../../../repositories/data/document-note.repository';
import { DocumentRepository } from '../../../repositories/data/document.repository';
import { EventsGateway } from '../../events.gateway';
import { AttachmentEvent } from '../../types/attachment-event';
import { ImportListener } from '../import-listener';

@Injectable()
export class AttachmentListenerService extends ImportListener<
  AttachmentEvent,
  AttachmentDTO
> {
  queueName: Queues.Attachment = Queues.Attachment;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly httpService: HttpService,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly documentRepository: DocumentRepository,
    protected readonly documentAdditionalInfoRepository: DocumentAdditionalInfoRepository,
    protected readonly documentNoteRepository: DocumentNoteRepository,
    protected readonly custom005DocumentRepository: Custom005DocumentRepository,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(importData: AttachmentDTO[]): Promise<AttachmentDTO[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number, statusId: number) => Promise<void> {
    return async (Progress: number, statusId: number) => {
      const progressKey = `import:25:order:${UploadHistoryID}:progress`;
      await this.redis.incrbyfloat(progressKey, Progress);

      const progressValue = +((await this.redis.get(progressKey)) || 0);

      const channel = `import:${CatalogID}`;
      const payload: any = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress: progressValue > 100 ? 100 : Math.round(progressValue),
      };

      if (statusId) {
        payload['StatusID'] = statusId;
      }

      this.messageGateway.server.in(channel).emit('progress', payload);
    };
  }

  async messageHandler(
    importData: AttachmentDTO[],
    userId: number,
    uploadHistoryId: string,
    chunk: number,
    additionalInfo: any,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    if (!uploadHistory || (uploadHistory && !uploadHistory.importListId)) {
      console.log('undefined history id', uploadHistoryId);
      return false;
    }

    if (uploadHistory.statusId !== UploadStatus.InProgress) {
      return false;
    }

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    const files = additionalInfo.files;
    const importAttachmentDTO = additionalInfo.importAttachmentDTO;
    const removeAfterProcessing = additionalInfo.removeAfterProcessing;

    const chunkPercent = additionalInfo.chunkPercent;
    const isLastChunk = additionalInfo.isLastChunk;

    if (files.length > 0) {
      for (const { CaseID } of importData) {
        const result = await this.saveFiles(files, CaseID, importAttachmentDTO);

        if (!result) {
          await this.uploadHistoryRepository.update(
            { id: uploadHistory.id },
            { statusId: UploadStatus.Failed },
          );
          await updateImportProgress(100, UploadStatus.Failed);

          for (const file of removeAfterProcessing) {
            if (await checkIfExists(file)) {
              await rmDir(file, {
                recursive: true,
              });
            }
          }
          return false;
        }
      }

      await updateImportProgress(chunkPercent, UploadStatus.InProgress);
    }

    if (isLastChunk) {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.Finished },
      );
      await updateImportProgress(100, UploadStatus.Finished);

      for (const file of removeAfterProcessing) {
        if (await checkIfExists(file)) {
          await rmDir(file, {
            recursive: true,
          });
        }
      }
    }

    return true;
  }

  protected async saveFiles(
    files: any[],
    CaseID: number,
    params: any,
  ): Promise<any[] | boolean> {
    const result: any[] = [];
    const caseServiceURL = this.config.get<string>('caseService.url');

    for (const fileID of files) {
      const formData = new FormData();
      formData.append('userID', String(params.userID));

      try {
        formData.append('FileID', fileID);
        formData.append('CaseID', String(CaseID));

        for (const key in params) {
          if (params[key]) {
            formData.append(key, params[key]);
          }
        }

        const response = await this.httpService
          .post(caseServiceURL + '/case-document', formData, {
            headers: { ...formData.getHeaders() },
            maxContentLength: 2048 * 1024 * 1024,
            maxBodyLength: 2048 * 1024 * 1024,
            timeout: 1_000_000,
          })
          .toPromise();

        result.push(response.data);
      } catch (error) {
        console.log('error Case / File', CaseID, fileID);
        console.log('error', error);
      }
    }

    return result;
  }

  private async getCurrentProgress(id: string): Promise<number> {
    const progressKey = `import:25:order:${id}:progress`;
    return +((await this.redis.get(progressKey)) || 0);
  }
}
