/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ApiClient } from '../../../common/api.client';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { Custom005Document } from '../../../entities/data/custom005-document.entity';
import { ArchiveService } from '../../../import-case-attachment/archive.service';
import { DocumentAdditionalInfoRepository } from '../../../repositories/data/document-additional-info.repository';
import { DocumentNoteRepository } from '../../../repositories/data/document-note.repository';
import { DocumentRepository } from '../../../repositories/data/document.repository';
import { EventsGateway } from '../../events.gateway';
import { AttachmentListenerService } from './attachment-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      ServiceParameterRepository,
      UploadHistoryRepository,
      DocumentRepository,
      DocumentAdditionalInfoRepository,
      DocumentNoteRepository,
      Custom005Document,
    ]),
    ImportRedisModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AttachmentListenerService, EventsGateway, ApiClient],
})
export class AttachmentListenerModule {}
