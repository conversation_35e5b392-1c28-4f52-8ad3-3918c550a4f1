import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { Custom004EEnforcementRequestToFina } from '../../../entities/legal/custom004-enforcement-request-to-fina.entity';
import { ImportFinaDTO } from '../../../import-basis-fina/dto/import-fina.dto';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../../../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { Custom004EEnforcementRequestToFinaRepository } from '../../../repositories/legal/custom004-enforcement-request-to-fina.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisFinaEvent } from '../../types/basis-fina-event';
import { FinaPreImportDataDTO } from './dto/fina-pre-import-data.dto';

const ImportFormat = 'dd.MM.yyyy';
const SystemFormat = 'yyyy-MM-dd';

@Injectable()
export class BasisFinaListenerService extends BaseImportListenerService<
  BasisFinaEvent,
  FinaPreImportDataDTO
> {
  queueName: Queues.BasisFina = Queues.BasisFina;
  catalog: CatalogNameEnum = CatalogNameEnum.BasisFina;
  catalogId: CatalogEnum = CatalogEnum.BasisFina;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly custom004EEnforcementRequestToFinaRepository: Custom004EEnforcementRequestToFinaRepository,
    protected readonly custom004EEnforcementRequestDeliveryStatusRepository: Custom004EEnforcementRequestDeliveryStatusRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportFinaDTO['importData'],
  ): Promise<FinaPreImportDataDTO[]> {
    const finaPreImportDataDTO: FinaPreImportDataDTO[] = [];
    const deliveryStatuses =
      await this.custom004EEnforcementRequestDeliveryStatusRepository.find({
        isDeleted: 0,
      });

    const deliveryStatusMapping: { [key: string]: number } = {};
    const yesNoMapping: { [key: string]: boolean } = { yes: true, no: false };
    for (const deliveryStatus of deliveryStatuses) {
      deliveryStatusMapping[deliveryStatus.name] = deliveryStatus.id;
    }

    for (const [index, data] of importData.entries()) {
      if (data.DeliveryStatus) {
        data.DeliveryStatusID = deliveryStatusMapping[data.DeliveryStatus];
      }

      if (data.DeliveryDate !== '' && data.DeliveryDate !== 'null') {
        const parsedDate = parse(
          String(data.DeliveryDate),
          ImportFormat,
          new Date(),
        );
        data.DeliveryDate = format(parsedDate, SystemFormat);
      }

      if (data.DateOfDisposal !== '' && data.DateOfDisposal !== 'null') {
        const parsedDate = parse(
          String(data.DateOfDisposal),
          ImportFormat,
          new Date(),
        );
        data.DateOfDisposal = format(parsedDate, SystemFormat);
      }

      if (data.DateOfElimination !== '' && data.DateOfElimination !== 'null') {
        const parsedDate = parse(
          String(data.DateOfElimination),
          ImportFormat,
          new Date(),
        );
        data.DateOfElimination = format(parsedDate, SystemFormat);
      }

      if (data.ProcessedByFina !== '') {
        data.ProcessedByFina =
          yesNoMapping[String(data.ProcessedByFina).toLowerCase()];
      }

      if (data.ReturnedByFina !== '') {
        data.ReturnedByFina =
          yesNoMapping[String(data.ReturnedByFina).toLowerCase()];
      }

      if (data.IncludedInFinaPriority !== '') {
        data.IncludedInFinaPriority =
          yesNoMapping[String(data.IncludedInFinaPriority).toLowerCase()];
      }

      finaPreImportDataDTO.push(data);
    }

    return finaPreImportDataDTO;
  }

  async messageHandler(
    importData: FinaPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertFina: Custom004EEnforcementRequestToFina[] = [];
    const deactivateFinaByBasis: number[] = [];

    if (importData.length > 0) {
      const chunks: FinaPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        for (const row of chunk) {
          const model = {
            CaseID: row.CaseID,
            BasisID: row.BasisForPaymentID,
            DeliveryDate: this.insertItem(row.DeliveryDate),
            DeliveryStatusID: this.insertItem(row.DeliveryStatusID),
            IsReturnedByFina: this.insertItem(row.ReturnedByFina),
            ReturningReason: this.insertItem(row.ReturningReason),
            IsIncludedInFinaPriority: this.insertItem(
              row.IncludedInFinaPriority,
            ),
            IsProcessedByFina: this.insertItem(row.ProcessedByFina),
            DateOfDisposal: this.insertItem(row.DateOfDisposal),
            DateOfElimination: this.insertItem(row.DateOfElimination),
            InsertedUserID: userId,
            UpdatedUserID: userId,
          } as Custom004EEnforcementRequestToFina;

          insertFina.push(model);
          deactivateFinaByBasis.push(row.BasisForPaymentID);
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertFina,
        deactivateFinaByBasis,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  insertItem(newValue: any): any {
    if (newValue !== null) {
      if (newValue === 'null') {
        return null;
      } else if (newValue !== '') {
        return newValue;
      }
    }
    return null;
  }
}
