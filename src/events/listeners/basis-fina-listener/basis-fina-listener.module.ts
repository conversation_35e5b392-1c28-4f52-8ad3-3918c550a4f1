import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../../../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { Custom004EEnforcementRequestToFinaRepository } from '../../../repositories/legal/custom004-enforcement-request-to-fina.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisFinaListenerService } from './basis-fina-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      Custom004EEnforcementRequestToFinaRepository,
      Custom004EEnforcementRequestDeliveryStatusRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [BasisFinaListenerService, EventsGateway],
})
export class BasisFinaListenerModule {}
