import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { EntityManager, EntitySchema, getMetadataArgsStorage } from 'typeorm';
import { Custom004EEnforcementRequestToFina } from '../../../../entities/legal/custom004-enforcement-request-to-fina.entity';

function getAllColumns(model: any): string[] {
  const columns = getMetadataArgsStorage().columns.filter(
    (column) => column.target === model,
  );
  return columns.map((column) => column.propertyName);
}

const TemporaryFinaEntity = new EntitySchema({
  name: 'TemporaryFina',
  columns: {},
});

const tableName = 'TemporaryFina';

export class TemporaryFina extends TemporaryTable<Custom004EEnforcementRequestToFina> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected columnList: string[],
    protected tableEntity = TemporaryFinaEntity,
  ) {
    super();
  }

  async manualInitialize(): Promise<void> {
    const columns = getMetadataArgsStorage().columns.filter(
      (column) => column.target === Custom004EEnforcementRequestToFina,
    );
    const columnsList = columns
      .map((column) => {
        if (this.columnList.includes(column.propertyName)) {
          return `"${column.propertyName}" ${column.options.type}`;
        }
        return false;
      })
      .filter((index) => index)
      .join(', ');

    const createTableSQL =
      `CREATE TEMPORARY TABLE "${tableName}"(` + columnsList + ');';
    await this.manager.query(`DROP TABLE if EXISTS "${tableName}";`);
    await this.manager.query(createTableSQL);
  }
  protected decompose(data: Custom004EEnforcementRequestToFina[]): any[] {
    const fieldsToSync = getAllColumns(Custom004EEnforcementRequestToFina);
    return data.map((v: any) => {
      const itemValues: any = {};
      for (const field of fieldsToSync) {
        if (v[field] !== undefined) {
          itemValues[field] = v[field];
        }
      }
      return itemValues;
    });
  }

  async updateOriginTable(): Promise<void> {
    const excludeSync = new Set(['ID', 'Inserted', 'Updated']);
    const fieldsToSync = getAllColumns(
      Custom004EEnforcementRequestToFina,
    ).filter((f) => !excludeSync.has(f));
    const setValue = fieldsToSync
      .map((field) => {
        if (this.columnList.includes(field)) {
          return field;
        }
        return false;
      })
      .filter((index) => index)
      .map((field) => `"${field}" = temp."${field}"`)
      .join(', ');
    return this.manager.query(`
    UPDATE "Legal"."Custom004EEnforcementRequestToFina" as original
                        set
                            ${setValue}
                        from "${tableName}" "temp" 
                        WHERE temp."ID" = original."ID";
    `);
  }
}
