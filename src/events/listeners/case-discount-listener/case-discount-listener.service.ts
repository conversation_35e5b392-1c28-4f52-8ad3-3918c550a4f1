import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { ContactWithResult } from '../../../entities/activity/contact-with-result.entity';
import { History } from '../../../entities/activity/history.entity';
import { Comment } from '../../../entities/activity/comment.entity';
import { CaseDiscount } from '../../../entities/data/case-discount.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportDiscountDTO } from '../../../import-case-discount/dto/import-discount.dto';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { CaseDiscountRepository } from '../../../repositories/data/case-discount.repository';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { CaseDiscountEvent } from '../../types/case-discount-event';
import { DiscountPreImportDataDTO } from './dto/discount-pre-import-data.dto';

const systemDateFormat = 'yyyy-MM-dd';
@Injectable()
export class CaseDiscountListenerService extends BaseImportListenerService<
  CaseDiscountEvent,
  DiscountPreImportDataDTO
> {
  queueName: Queues.CaseDiscount = Queues.CaseDiscount;
  catalog: CatalogNameEnum = CatalogNameEnum.CaseDiscount;
  catalogId: CatalogEnum = CatalogEnum.CaseDiscount;
  postImport = undefined;

  constructor(
    protected readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly caseDiscountRepository: CaseDiscountRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly historyRepository: HistoryRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportDiscountDTO['importData'],
  ): Promise<DiscountPreImportDataDTO[]> {
    for (const [index, row] of importData.entries()) {
      if (
        row.DiscountValue &&
        String(row.DiscountValue).toLowerCase() !== 'null'
      ) {
        importData[index].DiscountValue = Number(row.DiscountValue);
      }

      if (
        row.DiscountPercent &&
        String(row.DiscountPercent).toLowerCase() !== 'null'
      ) {
        importData[index].DiscountPercent =
          Number.parseFloat(String(row.DiscountPercent)) / 100;
      }

      if (
        row.DiscountDayFrom &&
        String(row.DiscountDayFrom).toLowerCase() !== 'null'
      ) {
        try {
          importData[index].DiscountDayFrom = format(
            parse(row.DiscountDayFrom, 'dd.MM.yyyy', new Date()),
            systemDateFormat,
          );
        } catch {
          importData[index].DiscountDayFrom = format(
            parse(row.DiscountDayFrom, systemDateFormat, new Date()),
            systemDateFormat,
          );
        }
      }

      if (
        row.DiscountDayTo &&
        String(row.DiscountDayTo).toLowerCase() !== 'null'
      ) {
        try {
          importData[index].DiscountDayTo = format(
            parse(row.DiscountDayTo, 'dd.MM.yyyy', new Date()),
            systemDateFormat,
          );
        } catch {
          importData[index].DiscountDayTo = format(
            parse(row.DiscountDayTo, systemDateFormat, new Date()),
            systemDateFormat,
          );
        }
      }
    }

    return importData;
  }

  async messageHandler(
    importData: DiscountPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const importConfig: {
      HistoryTypeID: number;
      HistoryResultID: number;
      DeleteDiscountComment: string;
    } = await this.parameters.getGlobalParameterValueByName<any>(
      'importDiscountConfig',
    );

    if (importData.length > 0) {
      const insertItems: CaseDiscount[] = [];
      const deleteItems: number[] = [];

      const newDiscountActivities: History[] = [];
      const deleteDiscountActivities: History[] = [];

      const activityResults: ContactWithResult[] = [];
      const activityComments: Comment[] = [];

      const skippedItems: number[] = [];

      const casesHaveNewDiscountActivities: number[] = [];
      const casesHaveDeleteDiscountActivities: number[] = [];

      const chunks: DiscountPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = (Number(jobPercent) ?? 100) / chunks.length;

      for (const chunk of chunks) {
        const caseIDs = chunk.map((index) => index.CaseID);
        const caseAmounts = await this.getCaseAmounts(caseIDs);
        const existCaseDiscount = await this.getExistCaseDiscount(caseIDs);

        const historyPool = await this.historyRepository.getPoolHistoryIds(
          caseIDs.length,
        );
        const historyCreatePool =
          await this.historyRepository.getPoolHistoryIds(caseIDs.length);
        const historyToCaseMapping: { [key: number]: number } = {};
        const historyToCreateCaseMapping: { [key: number]: number } = {};

        for (const [index, row] of chunk.entries()) {
          const caseId = row.CaseID;
          historyToCaseMapping[caseId] = historyPool[index];
          historyToCreateCaseMapping[caseId] = historyCreatePool[index];

          if (existCaseDiscount[caseId]) {
            deleteItems.push(caseId);
            if (!casesHaveDeleteDiscountActivities.includes(row.CaseID)) {
              deleteDiscountActivities.push({
                id: String(historyToCaseMapping[row.CaseID]),
                caseId: row.CaseID,
                typeId: Number(importConfig.HistoryTypeID),
                creationUserID: userId,
                isDeleted: 0,
                creationDate: new Date(),
              } as History);

              activityResults.push({
                historyId: String(historyToCaseMapping[row.CaseID]),
                partyId: 0,
                resultId: importConfig.HistoryResultID,
              } as ContactWithResult);

              activityComments.push({
                historyId: String(historyToCaseMapping[row.CaseID]),
                value: importConfig.DeleteDiscountComment,
              } as Comment);

              casesHaveDeleteDiscountActivities.push(row.CaseID);
            }
          }

          if (!this.isEmptyFields(row)) {
            const discountAmount: number = this.getPayAmount(
              row,
              caseAmounts[row.CaseID],
            );

            let percent = null;
            if (
              row.DiscountPercent &&
              typeof row.DiscountPercent === 'number'
            ) {
              percent = row.DiscountPercent;
            }

            if (discountAmount > 0) {
              const discount = this.caseDiscountRepository.create({
                caseId: String(caseId),
                fromDate: row.DiscountDayFrom,
                toDate: row.DiscountDayTo,
                payAmount: discountAmount,
                actualPayAmount: discountAmount,
                discountPercent: percent,
                note: row.Note,
                insertedUserId: userId,
                updatedUserId: userId,
                isDeleted: 0,
              });

              insertItems.push(discount);

              if (!casesHaveNewDiscountActivities.includes(row.CaseID)) {
                newDiscountActivities.push({
                  id: String(historyToCreateCaseMapping[row.CaseID]),
                  caseId: row.CaseID,
                  typeId: Number(importConfig.HistoryTypeID),
                  creationUserID: userId,
                  isDeleted: 0,
                  creationDate: new Date(),
                } as History);

                activityResults.push({
                  historyId: String(historyToCreateCaseMapping[row.CaseID]),
                  partyId: 0,
                  resultId: importConfig.HistoryResultID,
                } as ContactWithResult);

                if (row.Note) {
                  activityComments.push({
                    historyId: String(historyToCreateCaseMapping[row.CaseID]),
                    value: row.Note,
                  } as Comment);
                }

                casesHaveNewDiscountActivities.push(row.CaseID);
              } else {
                skippedItems.push(caseId);
              }
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertItems,
        deleteItems,
        newDiscountActivities,
        deleteDiscountActivities,
        activityResults,
        activityComments,
        skippedItems,
        metaData: {
          userId,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  isEmptyFields(row: DiscountPreImportDataDTO): boolean {
    return (
      (row.DiscountValue === 'null' || row.DiscountPercent === 'null') &&
      row.DiscountDayTo === 'null' &&
      row.DiscountDayFrom === 'null'
    );
  }

  getPayAmount(row: DiscountPreImportDataDTO, caseAmount: number): number {
    if (row.DiscountValue && typeof row.DiscountValue === 'number') {
      return row.DiscountValue;
    }

    if (row.DiscountPercent && typeof row.DiscountPercent === 'number') {
      return caseAmount * row.DiscountPercent;
    }

    return 0;
  }

  async getCaseAmounts(caseIDs: number[]): Promise<{ [key: string]: number }> {
    const caseAmounts: { [key: string]: number } = {};

    const rows = await this.invoiceRepository.getCaseAmount(caseIDs);

    for (const row of rows) {
      caseAmounts[row.CaseID] = row.ActualSum;
    }

    for (const caseID of caseIDs) {
      if (!Object.keys(caseAmounts).includes(String(caseID))) {
        caseAmounts[caseID] = 0;
      }
    }

    return caseAmounts;
  }

  async getExistCaseDiscount(
    caseIDs: number[],
  ): Promise<{ [key: string]: CaseDiscount }> {
    const caseToDiscountMap: { [key: string]: CaseDiscount } = {};

    const rows: CaseDiscount[] = await this.caseDiscountRepository.find({
      where: {
        caseId: In(caseIDs),
        isDeleted: 0,
      },
    });

    for (const row of rows) {
      caseToDiscountMap[row.caseId] = row;
    }

    return caseToDiscountMap;
  }
}
