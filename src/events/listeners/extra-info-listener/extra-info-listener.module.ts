import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { AdditionalNoteRepository } from '../../../repositories/data/additional-note.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { ExtraInfoListenerDataAccess } from './extra-info-listener-data-access';
import { ExtraInfoListenerService } from './extra-info-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      UploadHistoryRepository,
      HistoryRepository,
      AdditionalNoteRepository,
      ServiceParameterRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [
    ExtraInfoListenerService,
    ExtraInfoListenerDataAccess,
    EventsGateway,
  ],
})
export class ExtraInfoListenerModule {}
