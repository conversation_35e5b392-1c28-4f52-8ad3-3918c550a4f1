import { Injectable } from '@nestjs/common';

import { In } from 'typeorm';
import { AdditionalNote } from '../../../entities/data/additional-note.entity';
import { AdditionalNoteRepository } from '../../../repositories/data/additional-note.repository';

@Injectable()
export class ExtraInfoListenerDataAccess {
  constructor(private additionalNoteRepository: AdditionalNoteRepository) {}

  public async getExistRecords(caseIDs: number[]): Promise<{
    [key: string]: AdditionalNote[];
  }> {
    const items = await this.additionalNoteRepository.find({
      select: ['id', 'attributeValue', 'fieldNumber'],
      where: {
        attributeId: 1,
        attributeValue: In(caseIDs),
      },
    });

    const result: {
      [key: string]: AdditionalNote[];
    } = {};

    for (const item of items) {
      if (!result[item.attributeValue]) {
        result[item.attributeValue] = [item];
      } else {
        result[item.attributeValue].push(item);
      }
    }

    return result;
  }
}
