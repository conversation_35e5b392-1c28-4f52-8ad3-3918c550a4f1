/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryAdditionalNoteInterface } from './interfaces/temporary-additional-note.interface';

const TemporaryAdditionalNoteEntity = new EntitySchema({
  name: 'TemporaryAdditionalNote',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryAdditionalNote',
  declaration: `
  "AdditionalNoteID" BIGINT PRIMARY KEY NOT NULL,
  "Value" VARCHAR(1000)
  `,
})
export class TemporaryAdditionalNote extends TemporaryTable<TemporaryAdditionalNoteInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryAdditionalNoteEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryAdditionalNoteInterface[]): any[] {
    return data.map((v: any) => {
      return {
        AdditionalNoteID: v['id'],
        Value: v['value'],
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."AdditionalNote" as dan
                        set
                            "Value" = tan."Value"
                        from "${this.tableName}" "tan" WHERE tan."AdditionalNoteID" = dan."ID";
    `);
  }
}
