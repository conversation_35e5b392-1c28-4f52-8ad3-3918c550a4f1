import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { AdditionalNote } from '../../../entities/data/additional-note.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportExtraInfoDto } from '../../../import-extra-info/dto/import-extra-info.dto';
import { AdditionalNoteRepository } from '../../../repositories/data/additional-note.repository';
import { EventsGateway } from '../../events.gateway';
import { ExtraInfoEvent } from '../../types/extra-info-event';
import { ExtraInfoPreImportDataDTO } from './dto/extra-info-pre-import-data.dto';
import { ExtraInfoListenerDataAccess } from './extra-info-listener-data-access';

@Injectable()
export class ExtraInfoListenerService extends BaseImportListenerService<
  ExtraInfoEvent,
  ExtraInfoPreImportDataDTO
> {
  queueName: Queues.ExtraInfo = Queues.ExtraInfo;
  catalog: CatalogNameEnum = CatalogNameEnum.ExtraInfo;
  catalogId: CatalogEnum = CatalogEnum.ExtraInfo;
  postImport = undefined;

  constructor(
    protected readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly extraInfoListenerDataAccess: ExtraInfoListenerDataAccess,
    protected readonly additionalNoteRepository: AdditionalNoteRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly messageGateway: EventsGateway,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportExtraInfoDto['importData'],
  ): Promise<ExtraInfoPreImportDataDTO[]> {
    return importData;
  }

  async messageHandler(
    importData: ExtraInfoPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const fieldNumbers = Object.keys(importData[0])
        .filter((field) => field !== 'CaseID')
        .map((field) => field.replace('CaseExtra', ''));

      const insertItems: (AdditionalNote | null)[] = [];
      const updateItems: { [key: string]: string } = {};

      const chunks: ExtraInfoPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        5000,
      );

      const chunkPercent = (Number(jobPercent) ?? 100) / chunks.length;

      for (const chunk of chunks) {
        const caseIDs = chunk.map((index) => index.CaseID);
        const existsExtraInfo =
          await this.extraInfoListenerDataAccess.getExistRecords(caseIDs);

        for (const row of chunk) {
          for (const fieldNumber of fieldNumbers) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            let value = String(row[`CaseExtra${fieldNumber}`]);
            if (value === '') {
              continue;
            }

            if (value === 'null') {
              value = '';
            }

            if (existsExtraInfo[row.CaseID]) {
              const existsRecord = existsExtraInfo[row.CaseID].find((index) => {
                return index.fieldNumber === Number(fieldNumber);
              });

              if (existsRecord) {
                updateItems[existsRecord.id] = value;
              } else {
                const newEntity = this.additionalNoteRepository.create({
                  attributeId: 1,
                  attributeValue: row.CaseID,
                  fieldNumber: Number(fieldNumber),
                  value,
                });
                insertItems.push(newEntity);
              }
            } else {
              const newEntity = this.additionalNoteRepository.create({
                attributeId: 1,
                attributeValue: row.CaseID,
                fieldNumber: Number(fieldNumber),
                value,
              });
              insertItems.push(newEntity);
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      const filteredInsertItems: AdditionalNote[] = insertItems.filter(
        (index): index is AdditionalNote => index !== null,
      );

      await this.saveResult(Number(uploadHistoryId), page, {
        insertItems: filteredInsertItems,
        updateItems: updateItems,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }
}
