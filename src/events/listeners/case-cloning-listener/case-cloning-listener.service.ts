import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportCaseCloningDTO } from '../../../import-case-cloning/dto/import-case-cloning.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { EventsGateway } from '../../events.gateway';
import { CaseCloningEvent } from '../../types/case-cloning-event';
import { CaseCloningListenerDataAccess } from './case-cloning-listener-data-access';
import { ImportListener } from '../import-listener';
import { CaseCloningPreImportDataDTO } from './dto/case-cloning-pre-import-data.dto';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';

@Injectable()
export class CaseCloningListenerService extends ImportListener<
  CaseCloningEvent,
  CaseCloningPreImportDataDTO
> {
  queueName: Queues.CaseCloning = Queues.CaseCloning;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly caseCloningListenerDataAccess: CaseCloningListenerDataAccess,
    private readonly serviceParameterRepository: ServiceParameterRepository,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: ImportCaseCloningDTO['importData'],
  ): Promise<CaseCloningPreImportDataDTO[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => void {
    return (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
    };
  }

  async messageHandler(
    importData: CaseCloningPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    const historyTypeId =
      await this.serviceParameterRepository.getGlobalParameterValueByName<string>(
        'cloneCaseHistoryTypeID',
      );

    const result = await this.caseCloningListenerDataAccess.cloneCases(
      importData,
      userId,
      historyTypeId ?? '155',
    );

    updateImportProgress(100);

    if (result.success) {
      const path = await this.saveReport(
        result.report,
        userId,
        uploadHistory!.id,
      );
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Finished, resultFilePath: path },
      );
    } else {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Failed },
      );
    }

    return true;
  }
}
