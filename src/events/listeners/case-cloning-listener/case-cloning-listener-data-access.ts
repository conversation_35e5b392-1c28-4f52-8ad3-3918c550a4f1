import { Injectable } from '@nestjs/common';

import { CaseRepository } from 'src/repositories/data/case.repository';
import { CloneCaseRepository } from '../../../repositories/activity/clone-case.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { CaseCloningPreImportDataDTO } from './dto/case-cloning-pre-import-data.dto';

@Injectable()
export class CaseCloningListenerDataAccess {
  constructor(
    private caseRepository: CaseRepository,
    private historyRepository: HistoryRepository,
    private cloneCaseRepository: CloneCaseRepository,
  ) {}

  public async cloneCases(
    cases: CaseCloningPreImportDataDTO[],
    userId: number,
    historyTypeId: string,
  ): Promise<any> {
    return await this.caseRepository.runCloneCase(
      cases,
      userId,
      this.historyRepository,
      this.cloneCaseRepository,
      historyTypeId,
    );
  }
}
