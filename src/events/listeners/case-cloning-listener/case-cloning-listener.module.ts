/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ToolSourceRepository } from 'src/repositories/dictionary/tool-source.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { CloneCaseRepository } from '../../../repositories/activity/clone-case.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../../../repositories/data/custom001-contact-person.repository';
import { Custom001DebtorRepository } from '../../../repositories/data/custom001-debtor.repository';
import { Custom005ContactPersonRepository } from '../../../repositories/data/custom005-contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { DebtorTypeRepository } from '../../../repositories/debtor-type.repository';
import { SexTypeRepository } from '../../../repositories/dictionary/sex-type.repository';
import { EventsGateway } from '../../events.gateway';
import { CaseCloningListenerDataAccess } from './case-cloning-listener-data-access';
import { CaseCloningListenerService } from './case-cloning-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      PartyTypeRepository,
      DebtorTypeRepository,
      ToolSourceRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      ContactPersonRepository,
      DebtorRepository,
      Custom001ContactPersonRepository,
      Custom001DebtorRepository,
      Custom005ContactPersonRepository,
      CloneCaseRepository,
      HistoryRepository,
      SexTypeRepository,
    ]),
    // EventsModule,
  ],
  providers: [
    CaseCloningListenerService,
    CaseCloningListenerDataAccess,
    EventsGateway,
  ],
})
export class CaseCloningListenerModule {}
