import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { TransferToCollectionAgency } from '../../../entities/data/transfer-to-collection-agency.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { TransferToCollectionAgencyDTO } from '../../../import-transfer-to-collection-agency/dto/transfer-to-collection-agency.dto';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { TransferToCollectionAgencyRepository } from '../../../repositories/data/transfer-to-collection-agency.repository';
import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { EventsGateway } from '../../events.gateway';

import { TransferToCollectionAgencyEvent } from '../../types/import-info-to-collection-agency-event';
import { InfoPreImportDataDto } from './dto/info-pre-import-data.dto';

const ImportFormat = 'dd.MM.yyyy';
const SystemFormat = 'yyyy-MM-dd';

@Injectable()
export class TransferToCollectionAgencyListenerService extends BaseImportListenerService<
  TransferToCollectionAgencyEvent,
  InfoPreImportDataDto
> {
  queueName: Queues.TransferToCollectionAgency =
    Queues.TransferToCollectionAgency;
  catalog: CatalogNameEnum = CatalogNameEnum.TransferToCollectionAgency;
  catalogId: CatalogEnum = CatalogEnum.TransferToCollectionAgency;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly legalInvoiceRepository: LegalInvoiceRepository,
    protected readonly transferToCollectionAgencyRepository: TransferToCollectionAgencyRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: TransferToCollectionAgencyDTO['importData'],
  ): Promise<InfoPreImportDataDto[]> {
    const employerPreImportDataDTO: InfoPreImportDataDto[] = [];

    for (const [index, data] of importData.entries()) {
      if (data.TransferDate !== '' && data.TransferDate !== 'null') {
        const parsedDate = parse(
          String(data.TransferDate),
          ImportFormat,
          new Date(),
        );
        data.TransferDate = format(parsedDate, SystemFormat);
      }

      employerPreImportDataDTO.push(data);
    }

    return employerPreImportDataDTO;
  }

  async messageHandler(
    importData: InfoPreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertTransfers: TransferToCollectionAgency[] = [];
    const updateTransfers: TransferToCollectionAgency[] = [];

    if (importData.length > 0) {
      const chunks: InfoPreImportDataDto[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const accountNums = chunk
          .map((index) => index.AccountNum)
          .filter((index) => index);

        const invoices = await this.invoiceRepository.find({
          where: {
            accountNum: In(accountNums),
            isDeleted: 0,
          },
        });

        const legalInvoices = await this.legalInvoiceRepository
          .createQueryBuilder('c')
          .select([
            'invoice."AccountNum" as "accountNum"',
            'CAST(c."CaseID" AS INTEGER) AS "caseId"',
            'CAST(c."InvoiceID" AS INTEGER) AS "id"',
          ])
          .leftJoin('c.invoice', 'invoice')
          .where('invoice."IsDeleted" = 0')
          .andWhere('c."IsDeleted" = 0')
          .andWhere('invoice."AccountNum" in (:...accountNums)', {
            accountNums: accountNums,
          })
          .execute();

        const invoiceIDs: string[] = [];
        const accountNumberToInvoiceIDMap: {
          [key: string]: { [key: string]: string };
        } = {};
        const accountNumberToLegalInvoiceIDMap: {
          [key: string]: { [key: string]: string };
        } = {};

        for (const invoice of invoices) {
          accountNumberToInvoiceIDMap[invoice.accountNum] = {
            [invoice.caseId]: invoice.id,
          };

          invoiceIDs.push(invoice.id);
        }

        for (const invoice of legalInvoices) {
          accountNumberToLegalInvoiceIDMap[invoice.accountNum] = {
            [invoice.caseId]: invoice.id,
          };

          invoiceIDs.push(invoice.id);
        }

        const existsRecordMapping: {
          [key: string]: TransferToCollectionAgency;
        } = {};

        if (invoiceIDs.length > 0) {
          const existsRecords =
            await this.transferToCollectionAgencyRepository.find({
              where: {
                InvoiceID: In(invoiceIDs),
                IsDeleted: 0,
              },
            });

          for (const existsRecord of existsRecords) {
            existsRecordMapping[existsRecord.InvoiceID] = existsRecord;
          }
        }

        for (const row of chunk) {
          const invoiceID =
            accountNumberToInvoiceIDMap[row.AccountNum][row.CaseID] ??
            accountNumberToLegalInvoiceIDMap[row.AccountNum][row.CaseID] ??
            null;

          if (existsRecordMapping[invoiceID]) {
            const item = existsRecordMapping[invoiceID];
            this.insertItem(row.TransferDate, 'TransferDate', item);
            this.insertItem(
              row.CollectionAgencyName,
              'CollectionAgencyName',
              item,
            );
            this.insertItem(
              row.CollectionAgencyEmail,
              'CollectionAgencyEmail',
              item,
            );
            this.insertItem(
              row.CollectionAgencyAddress,
              'CollectionAgencyAddress',
              item,
            );
            this.insertItem(row.EDRPOU, 'EDRPOU', item);

            item.UpdatedUserID = userId;
            updateTransfers.push(item);
          } else {
            const model = {
              InvoiceID: String(invoiceID),
              InsertedUserID: userId,
              UpdatedUserID: userId,
            } as TransferToCollectionAgency;

            this.insertItem(row.TransferDate, 'TransferDate', model);
            this.insertItem(
              row.CollectionAgencyName,
              'CollectionAgencyName',
              model,
            );
            this.insertItem(
              row.CollectionAgencyEmail,
              'CollectionAgencyEmail',
              model,
            );
            this.insertItem(
              row.CollectionAgencyAddress,
              'CollectionAgencyAddress',
              model,
            );
            this.insertItem(row.EDRPOU, 'EDRPOU', model);

            insertTransfers.push(model);
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertTransfers: insertTransfers,
        updateTransfers: updateTransfers,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  insertItem(value: any, field: string, object: any): any {
    if (value !== null) {
      if (value === 'null') {
        object[field] = null;
      } else if (value !== '') {
        object[field] = value;
      }
    }
  }
}
