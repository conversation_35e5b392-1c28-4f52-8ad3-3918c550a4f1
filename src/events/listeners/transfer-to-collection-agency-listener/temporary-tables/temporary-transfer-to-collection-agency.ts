import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { EntityManager, EntitySchema, getMetadataArgsStorage } from 'typeorm';
import { TransferToCollectionAgency } from '../../../../entities/data/transfer-to-collection-agency.entity';

function getAllColumns(model: any): string[] {
  const columns = getMetadataArgsStorage().columns.filter(
    (column) => column.target === model,
  );
  return columns.map((column) => column.propertyName);
}

const TemporaryTransferToCollectionAgencyEntity = new EntitySchema({
  name: 'TemporaryTransferToCollectionAgency',
  columns: {},
});

const tableName = 'TemporaryTransferToCollectionAgency';

export class TemporaryTransferToCollectionAgency extends TemporaryTable<TransferToCollectionAgency> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected columnList: string[],
    protected tableEntity = TemporaryTransferToCollectionAgencyEntity,
  ) {
    super();
  }

  async manualInitialize(): Promise<void> {
    const columns = getMetadataArgsStorage().columns.filter(
      (column) => column.target === TransferToCollectionAgency,
    );
    const columnsList = columns
      .map((column) => {
        if (this.columnList.includes(column.propertyName)) {
          return `"${column.propertyName}" ${column.options.type}`;
        }
        return false;
      })
      .filter((index) => index)
      .join(', ');
    const createTableSQL =
      `CREATE TEMPORARY TABLE "${tableName}"(` + columnsList + ');';
    await this.manager.query(`DROP TABLE if EXISTS "${tableName}";`);
    await this.manager.query(createTableSQL);
  }
  protected decompose(data: TransferToCollectionAgency[]): any[] {
    const fieldsToSync = getAllColumns(TransferToCollectionAgency);
    return data.map((v: any) => {
      const itemValues: any = {};
      for (const field of fieldsToSync) {
        if (v[field] !== undefined) {
          itemValues[field] = v[field];
        }
      }
      return itemValues;
    });
  }

  async updateOriginTable(): Promise<void> {
    const excludeSync = new Set(['ID', 'Inserted', 'Updated']);
    const fieldsToSync = getAllColumns(TransferToCollectionAgency).filter(
      (f) => !excludeSync.has(f),
    );
    const setValue = fieldsToSync
      .map((field) => {
        if (this.columnList.includes(field)) {
          return field;
        }
        return false;
      })
      .filter((index) => index)
      .map((field) => `"${field}" = temp."${field}"`)
      .join(', ');
    return this.manager.query(`
    UPDATE "Data"."TransferToCollectionAgency" as original
                        set
                            ${setValue}
                        from "${tableName}" "temp" 
                        WHERE temp."InvoiceID" = original."InvoiceID";
    `);
  }
}
