import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { InvoiceRepository } from '../../../repositories/data/invoice.repository';
import { TransferToCollectionAgencyRepository } from '../../../repositories/data/transfer-to-collection-agency.repository';
import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { TransferToCollectionAgencyListenerService } from './transfer-to-collection-agency-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      InvoiceRepository,
      LegalInvoiceRepository,
      TransferToCollectionAgencyRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [TransferToCollectionAgencyListenerService, EventsGateway],
})
export class TransferToCollectionAgencyListenerModule {}
