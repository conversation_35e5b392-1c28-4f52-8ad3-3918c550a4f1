import { HttpService, Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { LegalInvoice } from '../../../entities/legal/legal-invoice.entity';
import { ImportDeleteLegalCaseDto } from '../../../import-delete-legal-case/dto/import-delete-legal-case.dto';
import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { DeleteLegalCaseEvent } from '../../types/delete-legal-case-event';
import { LegalCasePreImportDataDto } from './dto/legal-case-pre-import-data.dto';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { In } from 'typeorm';

@Injectable()
export class LegalCaseDeleteListenerService extends BaseImportListenerService<
  DeleteLegalCaseEvent,
  LegalCasePreImportDataDto
> {
  queueName: Queues.DeleteLegalCase = Queues.DeleteLegalCase;
  catalog: CatalogNameEnum = CatalogNameEnum.DeleteLegalCase;
  catalogId: CatalogEnum = CatalogEnum.DeleteLegalCase;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly httpService: HttpService,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly legalInvoiceRepository: LegalInvoiceRepository,
    protected readonly messageGateway: EventsGateway,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen().catch((error) => console.log(error));
  }

  async preImport(
    importData: ImportDeleteLegalCaseDto['importData'],
  ): Promise<LegalCasePreImportDataDto[]> {
    return importData;
  }

  async messageHandler(
    importData: LegalCasePreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const deleteInvoices: number[] = [];
      const checkCases: Set<number> = new Set();
      const invoiceIDs = importData.map((index) => index.InvoiceID);
      const caseIDs = importData.map((index) => index.LegalCaseID);

      const existRecords: LegalInvoice[] =
        await this.legalInvoiceRepository.find({
          select: ['id', 'caseId', 'invoiceId'],
          where: {
            invoiceId: In(invoiceIDs),
            caseId: In(caseIDs),
            isDeleted: 0,
          },
        });
      const existRecordMapping: { [key: number]: { [key2: number]: number } } =
        {};

      for (const record of existRecords) {
        existRecordMapping[record.caseId] =
          existRecordMapping[record.caseId] ?? {};
        existRecordMapping[record.caseId][record.invoiceId] = Number(record.id);
      }

      for (const data of importData) {
        if (existRecordMapping?.[data.LegalCaseID]?.[data.InvoiceID] ?? false) {
          deleteInvoices.push(
            existRecordMapping[data.LegalCaseID][data.InvoiceID],
          );
          checkCases.add(data.LegalCaseID);
        }
      }

      await this.emitProgress(Number(uploadHistoryId), Number(jobPercent));
      await this.saveResult(Number(uploadHistoryId), page, {
        deleteInvoices,
        checkCases: [...checkCases],
        metaData: {
          userId,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }
}
