/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ToolSourceRepository } from 'src/repositories/dictionary/tool-source.repository';
import { ExtCustom002PhoneRepository } from 'src/repositories/ext-masked-data/ext-custom002-phone.repository';
import { ExtPhoneRepository } from 'src/repositories/ext-masked-data/ext-phone.repository';
import { PhoneToCaseRepository } from 'src/repositories/ext-masked-data/phone-to-case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { MaskedPhoneListenerDataAccess } from './mascked-phone-listener-data-access';
import { MaskedPhoneListenerService } from './masked-phone-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      PartyTypeRepository,
      ToolSourceRepository,
      ServiceParameterRepository,
      ExtPhoneRepository,
      ExtCustom002PhoneRepository,
      PhoneToCaseRepository,
      UploadHistoryRepository,
    ]),
    // EventsModule,
  ],
  providers: [
    MaskedPhoneListenerService,
    MaskedPhoneListenerDataAccess,
    EventsGateway,
  ],
})
export class MaskedPhoneListenerModule {}
