/* eslint-disable unicorn/prevent-abbreviations */
import { Injectable } from '@nestjs/common';
import { getManager, In } from 'typeorm';

import { Case } from 'src/entities/data/case.entity';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ExtPhoneRepository } from 'src/repositories/ext-masked-data/ext-phone.repository';
import { ToolSourceRepository } from 'src/repositories/dictionary/tool-source.repository';
import { MaskedPhoneWithCase } from './interfaces/masked-phone-with-case.interface';
import { ToolSource } from 'src/entities/dictionary/tool-source.entity';
import { Custom002MaskedPhonePartId } from './interfaces/custom002-masked-phone-part-id.interface';

@Injectable()
export class MaskedPhoneListenerDataAccess {
  constructor(
    private caseRepository: CaseRepository,
    private extPhone: ExtPhoneRepository,
    private toolSourceRepository: ToolSourceRepository,
  ) {}

  public async getCaseIdWithDebtor(
    caseIds: number[],
  ): Promise<Pick<Case, 'id' | 'debtorId'>[]> {
    return this.caseRepository.find({
      select: ['id', 'debtorId'],
      where: { id: In(caseIds) },
    });
  }

  public async getMaskedPhonesWithCaseByCaseIds(
    caseIds: number[],
  ): Promise<MaskedPhoneWithCase[]> {
    return this.extPhone
      .createQueryBuilder('p')
      .select([
        'c."ID" as "caseId"',
        'c."DebtorID" as "debtorId"',
        'p."MaskedNumber" as "maskedNumber"',
        'p."ID" as "maskedPhoneId"',
        'p."IsDeleted" as "isDeleted"',
      ])
      .innerJoin(Case, 'c', 'p."DebtorID" = c."DebtorID" and c."IsDeleted" = 0')
      .where(`c."ID" IN(:...caseIds)`, { caseIds })
      .execute();
  }

  public async getImportMaskToolSource(): Promise<ToolSource | undefined> {
    return this.toolSourceRepository.findOne({
      where: { name: 'Импорт масок телефонов' },
    });
  }

  public async getCanBeChangedToolSourceIds(): Promise<number[]> {
    const toolSources: Array<{ id: number }> = await this.toolSourceRepository
      .createQueryBuilder('t')
      .select(['t."ID" as "id"'])
      .where(`("AdditionalInfo"->>'CanBeChangedFromImport')::boolean is true`)
      .execute();

    return toolSources.map((t) => t.id);
  }

  public async getPoolMaskedPhoneIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"ExtMaskedData"."Phone_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "ExtMaskedData"."Phone_ID_seq") as last_sequence`,
      [count],
    );

    return this.range(Number(sequence.from), Number(sequence.to));
  }

  public async getAvailableToChangeCustom002MaskedPhone(
    maskedPhoneIds: number[],
  ): Promise<Custom002MaskedPhonePartId[]> {
    return getManager().query(
      `select * from "Activity"."Custom002GetMaskedPhonePartID"($1::BIGINT[])`,
      [maskedPhoneIds],
    );
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
