/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-assertion */

import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';
import { getManager, In } from 'typeorm';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { MaskedPhoneEvent } from 'src/events/types/masked-phone-event';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { MaskedPhoneListenerDataAccess } from './mascked-phone-listener-data-access';
import { ImportListener } from '../import-listener';
import { ImportMaskedPhoneDTO } from 'src/masked-phone/dto/import-masked-phone.dto';
import { MaskedPhonePreImportData } from './dto/masked-phone-pre-import-data.dto';
import { ExtPhoneRepository } from 'src/repositories/ext-masked-data/ext-phone.repository';
import { PhoneToCaseRepository } from 'src/repositories/ext-masked-data/phone-to-case.repository';
import { ExtPhone } from 'src/entities/ext-masked-data/ext-phone.entity';
import { PhoneToCase } from 'src/entities/ext-masked-data/phone-to-case.entity';
import { MaskedPhoneWithCase } from './interfaces/masked-phone-with-case.interface';
import { ExtCustom002PhoneRepository } from 'src/repositories/ext-masked-data/ext-custom002-phone.repository';
import { ExtCustom002Phone } from 'src/entities/ext-masked-data/ext-custom002-phone.entity';
import { ToolSource } from 'src/entities/dictionary/tool-source.entity';
import { TemporaryExtPhone } from './temporary-tables/temporary-ext-phone';
import { TemporaryCustom002Phone } from './temporary-tables/temporary-custom002-phone';
import { splitOnChunks } from 'src/common/helpers/split-on-chunks';
import { UpdateImportProgressPublisher } from 'src/events/publishers/update-import-progress-publisher';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { UploadStatus } from 'src/import/upload-status.enum';

@Injectable()
export class MaskedPhoneListenerService extends ImportListener<
  MaskedPhoneEvent,
  MaskedPhonePreImportData
> {
  queueName: Queues.MaskedPhone = Queues.MaskedPhone;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly maskedPhoneListenerDataAccess: MaskedPhoneListenerDataAccess,
    private readonly partyTypeRepository: PartyTypeRepository,
    private readonly serviceParameterRepository: ServiceParameterRepository,
    private readonly extPhoneRepository: ExtPhoneRepository,
    private readonly extCustom002PhoneRepository: ExtCustom002PhoneRepository,
    private readonly phoneToCaseRepository: PhoneToCaseRepository,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: ImportMaskedPhoneDTO['importData'],
  ): Promise<MaskedPhonePreImportData[]> {
    const partyTypes = await this.partyTypeRepository.getList();

    const maskedPhonePreImportData: MaskedPhonePreImportData[] = [];

    for (const record of importData) {
      const preImportRecord = plainToClass(MaskedPhonePreImportData, record, {
        excludeExtraneousValues: true,
      });

      if (record.MaskedPartyType) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        preImportRecord.MaskedPartyTypeID = partyTypes.find(
          (pt) => pt.name === record.MaskedPartyType,
        )!.id;
      }

      maskedPhonePreImportData.push(preImportRecord);
    }

    return maskedPhonePreImportData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => void {
    const updateImportProgressPublisher = new UpdateImportProgressPublisher(
      this.connection,
    );
    return (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
      updateImportProgressPublisher.publish({
        event: 'UpdateImportProgress',
        payload,
      });
    };
  }

  async messageHandler(
    importData: MaskedPhonePreImportData[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );
    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    const caseIds = this.getUniqueValuesInArray(importData, 'CaseID');

    const [
      caseIdToDebtorIdMap,
      maskedPhonesWithCase,
      toolSource,
      usePhonePartyAndType,
      usePhoneToFeature,
    ] = await Promise.all([
      this.getCaseIdToDebtorIdMap(caseIds),
      this.maskedPhoneListenerDataAccess.getMaskedPhonesWithCaseByCaseIds(
        caseIds,
      ),
      this.maskedPhoneListenerDataAccess.getImportMaskToolSource(),
      this.serviceParameterRepository.getGlobalParameterValueByName<boolean>(
        'usePhonePartyAndType',
      ),
      this.serviceParameterRepository.getGlobalParameterValueByName<boolean>(
        'usePhone2Feature',
      ),
    ]);

    updateImportProgress(10);

    const combiningPhonesByCase = this.combinePhoneByCase(
      maskedPhonesWithCase,
      caseIds,
    );

    const createMaskedPhones: ExtPhone[] = [];
    const createMaskedPhoneToCases: PhoneToCase[] = [];
    const updateMaskedPhones: ExtPhone[] = [];

    for (const data of importData) {
      if (combiningPhonesByCase[data.CaseID][data.MaskedNumber]) {
        const maskedPhoneId =
          combiningPhonesByCase[data.CaseID][data.MaskedNumber];

        const maskedPhone = maskedPhonesWithCase.find(
          (p) => p.maskedPhoneId === maskedPhoneId,
        );

        if (maskedPhone) {
          const updateMaskedPhone = this.extPhoneRepository.create({
            id: maskedPhoneId,
            ...(data.MaskedPhoneNote && { note: data.MaskedPhoneNote }),
            ...(maskedPhone.isDeleted === data.IsActive &&
              this.getAdditionalInfoByIsActive(data.IsActive, userId)),
            partId: data.MaskedPartyTypeID,
          } as ExtPhone);

          updateMaskedPhones.push(updateMaskedPhone);
        }
      } else {
        const isDeleted = data.IsActive ? 0 : 1;
        const importMaskedPhone = this.extPhoneRepository.create({
          debtorId: caseIdToDebtorIdMap.get(data.CaseID.toString()),
          maskedNumber: data.MaskedNumber,
          isDeleted,
          note: data.MaskedPhoneNote,
          insertedUserId: userId,
          deactivatedUserId: isDeleted ? userId : undefined,
          deactivatedDate: isDeleted ? new Date() : undefined,
          partId: data.MaskedPartyTypeID,
        });

        const phoneToCase = this.phoneToCaseRepository.create({
          caseId: data.CaseID.toString(),
          insertedUserId: userId,
        });

        createMaskedPhones.push(importMaskedPhone);
        createMaskedPhoneToCases.push(phoneToCase);
      }
    }

    if (createMaskedPhones.length > 0) {
      await this.setIdsToImportData(
        createMaskedPhones,
        createMaskedPhoneToCases,
      );
    }

    const { createCustom002Phones, updateCustom002Phones } = usePhoneToFeature
      ? await this.createCustom002Phones(
          createMaskedPhones,
          updateMaskedPhones,
          toolSource!,
        )
      : { createCustom002Phones: [], updateCustom002Phones: [] };

    this.checkUsePhonePartyAndType(
      createMaskedPhones,
      updateMaskedPhones,
      usePhonePartyAndType ?? false,
    );

    const maskedPhonesToUpdate = await this.collectMaskedPhoneDataForUpdate(
      updateMaskedPhones,
    );
    updateImportProgress(20);

    const chunk = this.config.get<number>('chunk.default')!;

    try {
      await getManager().transaction(async (manager) => {
        if (maskedPhonesToUpdate.length > 0) {
          const tempExtPhoneTable = new TemporaryExtPhone(manager, this.config);
          await tempExtPhoneTable.initialize();
          await tempExtPhoneTable.insert(maskedPhonesToUpdate);
        }

        updateImportProgress(40);

        if (updateCustom002Phones.length > 0) {
          const tempCustom002PhoneTable = new TemporaryCustom002Phone(
            manager,
            this.config,
          );
          await tempCustom002PhoneTable.initialize();
          await tempCustom002PhoneTable.insert(updateCustom002Phones);
        }

        updateImportProgress(60);

        const chunks = splitOnChunks(createMaskedPhones, chunk);
        const insertExtPhonePromises: Array<Promise<any>> = [];
        for (const chunkData of chunks) {
          insertExtPhonePromises.push(manager.insert(ExtPhone, chunkData));
        }
        await Promise.all(insertExtPhonePromises);
        updateImportProgress(80);

        await Promise.all([
          this.phoneToCaseRepository.transactionSave(
            manager,
            createMaskedPhoneToCases,
            { chunk },
          ),
          this.extCustom002PhoneRepository.transactionSave(
            manager,
            createCustom002Phones,
            { chunk },
          ),
        ]);
        updateImportProgress(100);
      });
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Finished },
      );
    } catch {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Failed },
      );
    }

    return true;
  }

  private async collectMaskedPhoneDataForUpdate(
    updatedMaskedPhones: ExtPhone[],
  ): Promise<ExtPhone[]> {
    const maskedPhonesToUpdate: ExtPhone[] = [];

    if (updatedMaskedPhones.length > 0) {
      const maskedPhonesBeforeUpdate = await this.extPhoneRepository.find({
        where: { id: In(updatedMaskedPhones.map((mp) => mp.id)) },
      });

      for (const maskedPhoneBeforeUpdate of maskedPhonesBeforeUpdate) {
        maskedPhonesToUpdate.push(
          Object.assign(
            {},
            maskedPhoneBeforeUpdate,
            updatedMaskedPhones.find(
              (mp) => mp.id === maskedPhoneBeforeUpdate.id,
            ),
          ),
        );
      }
    }

    return maskedPhonesToUpdate;
  }

  private checkUsePhonePartyAndType(
    createMaskedPhones: ExtPhone[],
    updateMaskedPhones: ExtPhone[],
    usePhonePartyAndType: boolean,
  ): void {
    if (!usePhonePartyAndType) {
      for (const p of createMaskedPhones) {
        delete p.partId;
      }
      for (const p of updateMaskedPhones) {
        delete p.partId;
      }
    }
  }

  private async createCustom002Phones(
    insertPhones: ExtPhone[],
    updatePhones: ExtPhone[],
    toolSource: ToolSource,
  ): Promise<{
    createCustom002Phones: ExtCustom002Phone[];
    updateCustom002Phones: ExtCustom002Phone[];
  }> {
    const createCustom002Phones: ExtCustom002Phone[] = [];
    const updateCustom002Phones: ExtCustom002Phone[] = [];

    for (const maskedPhone of insertPhones) {
      const custom002Phone = this.createCustom002Phone(
        maskedPhone,
        toolSource.id,
      );
      createCustom002Phones.push(custom002Phone);
    }

    if (updatePhones.length > 0) {
      const [
        availableToChangeCustom002MaskedPhones,
        canBeChangedToolSourceIds,
      ] = await Promise.all([
        this.maskedPhoneListenerDataAccess.getAvailableToChangeCustom002MaskedPhone(
          updatePhones.map((phone) => phone.id),
        ),
        this.maskedPhoneListenerDataAccess.getCanBeChangedToolSourceIds(),
      ]);

      for (const maskedPhone of updatePhones) {
        const canChange = availableToChangeCustom002MaskedPhones.find(
          (item) => item.MaskedPhoneID === maskedPhone.id.toString(),
        );

        if (
          canBeChangedToolSourceIds.includes(maskedPhone.id) &&
          canChange &&
          canChange.ShouldBeChanged
        ) {
          const custom002Phone = this.createCustom002Phone(
            maskedPhone,
            toolSource.id,
          );
          updateCustom002Phones.push(custom002Phone);
        }
      }
    }

    return { createCustom002Phones, updateCustom002Phones };
  }

  private createCustom002Phone(
    maskedPhone: ExtPhone,
    toolSourceId: number,
  ): ExtCustom002Phone {
    return this.extCustom002PhoneRepository.create({
      maskedPhoneId: maskedPhone.id.toString(),
      partId: maskedPhone.partId,
      toolSourceId,
    });
  }

  private async setIdsToImportData(
    createMaskedPhones: ExtPhone[],
    createMaskedPhoneToCases: PhoneToCase[],
  ) {
    const maskedPhoneIds =
      await this.maskedPhoneListenerDataAccess.getPoolMaskedPhoneIds(
        createMaskedPhones.length,
      );

    for (const [index, maskedPhone] of createMaskedPhones.entries()) {
      maskedPhone.id = maskedPhoneIds[index];
    }

    for (const [
      index,
      maskedPhoneToCase,
    ] of createMaskedPhoneToCases.entries()) {
      maskedPhoneToCase.maskedPhoneId = maskedPhoneIds[index];
    }
  }

  private getAdditionalInfoByIsActive(isActive: number, userId: number): any {
    return isActive
      ? { isDeleted: 0, reactivatedUserId: userId, reactivatedDate: new Date() }
      : {
          isDeleted: 1,
          deactivatedUserId: userId,
          deactivatedDate: new Date(),
        };
  }

  private async getCaseIdToDebtorIdMap(
    caseIds: number[],
  ): Promise<Map<string, string>> {
    const casesWithDebtor =
      await this.maskedPhoneListenerDataAccess.getCaseIdWithDebtor([
        ...caseIds,
      ]);
    const caseIdToDebtorIdMap = new Map<string, string>();

    for (const caseWithDebtor of casesWithDebtor) {
      caseIdToDebtorIdMap.set(caseWithDebtor.id, caseWithDebtor.debtorId);
    }

    return caseIdToDebtorIdMap;
  }

  private getUniqueValuesInArray<T, K extends keyof T>(
    array: T[],
    key: K,
  ): T[K][] {
    const values = new Set(
      array.filter((item) => item[key]).map((item) => item[key]),
    );
    return [...values];
  }

  private combinePhoneByCase(
    maskedPhonesWithCase: MaskedPhoneWithCase[],
    caseIds: number[],
  ): { [key: string]: { [key: string]: number } } {
    const combiningPhonesByCase: { [key: string]: { [key: string]: number } } =
      {};
    for (const caseId of caseIds) {
      combiningPhonesByCase[caseId] = {};
      const maskedPhonesWithSameCaseId = maskedPhonesWithCase.filter(
        (mp) => mp.caseId === caseId.toString(),
      );
      for (const maskedPhone of maskedPhonesWithSameCaseId) {
        combiningPhonesByCase[caseId][maskedPhone.maskedNumber] =
          maskedPhone.maskedPhoneId;
      }
    }

    return combiningPhonesByCase;
  }
}
