/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryCustom002PhoneInterface } from './interfaces/temporary-custom002-phone.interface';

const TemporaryCustom002PhoneEntity = new EntitySchema({
  name: 'TemporaryCustom002Phone',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryCustom002Phone',
  declaration: `
  "MaskedPhoneID" BIGINT PRIMARY KEY NOT NULL ,
  "PartID" SMALLINT,
  "ToolSourceID" SMALLINT
  `,
})
export class TemporaryCustom002Phone extends TemporaryTable<TemporaryCustom002PhoneInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryCustom002PhoneEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryCustom002PhoneInterface[]): any[] {
    return data.map((v: any) => {
      return {
        MaskedPhoneID: v['maskedPhoneId'],
        PartID: v['partId'],
        ToolSourceID: v['toolSourceId'],
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "ExtMaskedData"."Custom002Phone" as emdcp
                        set
                            "MaskedPhoneID" = t."MaskedPhoneID",
                            "PartID" = t."PartID",
                            "ToolSourceID" = t."ToolSourceID"
                        from "${this.tableName}" "t" WHERE t."MaskedPhoneID"= emdcp."MaskedPhoneID";
    `);
  }
}
