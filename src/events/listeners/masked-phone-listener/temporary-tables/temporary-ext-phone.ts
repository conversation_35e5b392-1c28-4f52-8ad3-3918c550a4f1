/* eslint-disable unicorn/prevent-abbreviations */
import { Entity<PERSON>anager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryExtPhoneInterface } from './interfaces/temporary-ext-phone.interface';

const TemporaryExtPhoneEntity = new EntitySchema({
  name: 'TemporaryExtPhone',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryExtPhone',
  declaration: `
  "ID" BIGINT PRIMARY KEY NOT NULL ,
  "TypeID" SMALLINT,
  "PartID" SMALLINT,
  "Note" VARCHAR(50),
  "UpdatedUserID" INTEGER,
  "IsDeleted" SMALLINT NOT NULL,
  "ReactivatedUserID" INTEGER,
  "ReactivatedDate" TIMESTAMP,
  "DeactivatedUserID" INTEGER,
  "DeactivatedDate" TIMESTAMP
  `,
})
export class TemporaryExtPhone extends TemporaryTable<TemporaryExtPhoneInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryExtPhoneEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryExtPhoneInterface[]): any[] {
    return data.map((v: any) => {
      return {
        ID: v['id'],
        TypeID: v['typeId'],
        PartID: v['partId'],
        Note: v['note'],
        UpdatedUserID: v['updatedUserId'],
        IsDeleted: v['isDeleted'],
        ReactivatedUserID: v['reactivatedUserId'],
        ReactivatedDate: this.timestampColumn(v['reactivatedDate']),
        DeactivatedUserID: v['deactivatedUserId'],
        DeactivatedDate: this.timestampColumn(v['deactivatedDate']),
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "ExtMaskedData"."Phone" as emdp
                        set
                            "TypeID" = ipt."TypeID",
                            "PartID" = ipt."PartID",
                            "Note" = ipt."Note",
                            "UpdatedUserID" = ipt."UpdatedUserID",
                            "IsDeleted" = ipt."IsDeleted",
                            "ReactivatedUserID" = ipt."ReactivatedUserID",
                            "ReactivatedDate" = ipt."ReactivatedDate",
                            "DeactivatedUserID" = ipt."DeactivatedUserID",
                            "DeactivatedDate" = ipt."DeactivatedDate"
                        from "${this.tableName}" "ipt" WHERE ipt."ID"= emdp."ID";
    `);
  }
}
