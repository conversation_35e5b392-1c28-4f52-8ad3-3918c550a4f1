import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse, subDays } from 'date-fns';
import { de, ro } from 'date-fns/locale';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { getManager, In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CaseStateHistory } from '../../../entities/data/case-state-history.entity';
import { TagForCaseDebtorNetwork } from '../../../entities/data/tag-for-case-debtor-network.entity';
import { TagForCasePhone } from '../../../entities/data/tag-for-case-phone.entity';
import { TagForCase } from '../../../entities/data/tag-for-case.entity';
import { TagForInvoice } from '../../../entities/data/tag-for-invoice.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportCaseStateDto } from '../../../import-case-state/dto/import-case-state.dto';
import { ImportTagDTO } from '../../../import-tag/dto/import-tag.dto';
import { Level } from '../../../import/level.enum';
import { CaseStateHistoryRepository } from '../../../repositories/data/case-state-history.repository';
import { TagForCaseDebtorNetworkRepository } from '../../../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../../../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../../../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../../../repositories/data/tag-for-invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { CaseStateEvent } from '../../types/case-state-event';
import { TagEvent } from '../../types/tag-event';
import { CaseStatePreImportDataDto } from './dto/case-state-pre-import-data.dto';

type Dictionary = { ID: number; Name: string };

const systemDateFormat = 'yyyy-MM-dd';
const fileDateFormat = 'dd.MM.yyyy';
const defaultValidTo = '01.01.2100';

@Injectable()
export class CaseStateListenerService extends BaseImportListenerService<
  CaseStateEvent,
  CaseStatePreImportDataDto
> {
  queueName: Queues.CaseState = Queues.CaseState;
  catalog: CatalogNameEnum = CatalogNameEnum.CaseState;
  catalogId: CatalogEnum = CatalogEnum.CaseState;
  postImport = undefined;

  constructor(
    protected readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly caseStateHistoryRepository: CaseStateHistoryRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportCaseStateDto['importData'],
    userId: number,
  ): Promise<CaseStatePreImportDataDto[]> {
    const rows: CaseStatePreImportDataDto[] = [];

    const caseStateMapping: any = {};

    const caseStates: Dictionary[] = await getManager()
      .query(`select "ID", "Name"
      from "Dictionary"."CaseState"
      where "IsDeleted" = 0
      order by 1;`);

    for (const caseState of caseStates) {
      caseStateMapping[caseState.Name] = caseState.ID;
    }

    for (const [index, row] of importData.entries()) {
      const attributes: CaseStatePreImportDataDto = {
        ...row,
        CaseStateID: caseStateMapping[row.StateName],
        DateFrom: row.StateFrom,
        DateTo: row.StateTo,
        CaseStateName: row.StateName,
        InsertedUserID: userId,
        UpdatedUserID: userId,
      };
      rows.push(attributes);
    }

    return rows;
  }

  async messageHandler(
    importData: CaseStatePreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const caseStateHistories: CaseStateHistory[] = [];
    const caseStateHistoriesDelete: number[] = [];

    if (importData.length > 0) {
      const chunks: CaseStatePreImportDataDto[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = (Number(jobPercent) ?? 100) / chunks.length;

      for (const chunk of chunks) {
        const caseIDs = chunk.map((item) => item.CaseID);
        const existsCaseStateHistories =
          await this.caseStateHistoryRepository.find({
            where: {
              caseId: In(caseIDs),
              isDeleted: 0,
            },
          });
        const existsCaseStateHistoryMap: {
          [caseId: number]: CaseStateHistory;
        } = {};
        for (const record of existsCaseStateHistories) {
          existsCaseStateHistoryMap[record.caseId] = record;
        }
        for (const row of chunk) {
          const dateTo = row.DateTo ? row.DateTo : defaultValidTo;
          if (!row.StateID && existsCaseStateHistoryMap[row.CaseID]) {
            await this.caseStateHistoryRepository.update(
              {
                id: Number(existsCaseStateHistoryMap[row.CaseID].id),
              },
              {
                dateTo: format(
                  subDays(parse(row.DateFrom, fileDateFormat, new Date()), 1),
                  systemDateFormat,
                ),
                updatedUserID: row.UpdatedUserID,
              },
            );
          }
          if (!row.IsDeleted && row.StateID) {
            this.caseStateHistoryRepository.update(
              {
                id: Number(row.StateID),
              },
              {
                caseStateId: row.CaseStateID,
                dateFrom: format(
                  parse(row.DateFrom, fileDateFormat, new Date()),
                  systemDateFormat,
                ),
                dateTo: format(
                  parse(dateTo, fileDateFormat, new Date()),
                  systemDateFormat,
                ),
                updatedUserID: row.UpdatedUserID,
              },
            );
          } else if (!row.IsDeleted) {
            caseStateHistories.push(
              this.caseStateHistoryRepository.create({
                caseId: row.CaseID,
                caseStateId: row.CaseStateID,
                dateFrom: format(
                  parse(row.DateFrom, fileDateFormat, new Date()),
                  systemDateFormat,
                ),
                dateTo: format(
                  parse(dateTo, fileDateFormat, new Date()),
                  systemDateFormat,
                ),
                insertedUserID: row.InsertedUserID,
                isDeleted: row.IsDeleted ? row.IsDeleted : 0,
                updatedUserID: row.UpdatedUserID,
              }),
            );
          } else if (row.IsDeleted && row.StateID) {
            caseStateHistoriesDelete.push(row.StateID);
          }
        }
        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        caseStateHistories,
        caseStateHistoriesDelete,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }
}
