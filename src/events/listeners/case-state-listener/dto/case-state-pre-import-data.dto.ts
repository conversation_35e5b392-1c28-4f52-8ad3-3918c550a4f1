import { Expose } from 'class-transformer';

export class CaseStatePreImportDataDto {
  @Expose()
  public StateID: number | null;

  @Expose()
  public CaseID: number;

  @Expose()
  public CaseStateName: string;

  @Expose()
  public CaseStateID: number;

  @Expose()
  public DateFrom: string;

  @Expose()
  public DateTo: string | null;

  @Expose()
  public InsertedUserID: number;

  @Expose()
  public UpdatedUserID: number;

  @Expose()
  public IsDeleted: number | null;
}
