import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { CaseStateHistoryRepository } from '../../../repositories/data/case-state-history.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { CaseStateListenerService } from './case-state-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      UploadHistoryRepository,
      ServiceParameterRepository,
      CaseStateHistoryRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [CaseStateListenerService, EventsGateway],
})
export class CaseStateListenerModule {}
