import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { Custom005CaseAdditionalInfo } from '../../../entities/data/custom005-case-additional-info.entity';
import { Debtor } from '../../../entities/data/debtor.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ContractDataDTO } from '../../../import-contract-data/dto/contract-data.dto';
import { ImportContractDataDTO } from '../../../import-contract-data/dto/import-contract-data.dto';
import { CaseRepository } from '../../../repositories/data/case.repository';
import { Custom005CaseAdditionalInfoRepository } from '../../../repositories/data/custom005-case-additional-info.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { DebtorTypeRepository } from '../../../repositories/dictionary/debtor-type.repository';
import { EventsGateway } from '../../events.gateway';
import { ContractDataEvent } from '../../types/contract-data-event';
import { ContractDataPreImportDataDTO } from './dto/contract-data-pre-import-data.dto';

@Injectable()
export class ContractDataListenerService extends BaseImportListenerService<
  ContractDataEvent,
  ContractDataPreImportDataDTO
> {
  queueName: Queues.ContractData = Queues.ContractData;
  catalog: CatalogNameEnum = CatalogNameEnum.ContractData;
  catalogId: CatalogEnum = CatalogEnum.ContractData;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly debtorTypeRepository: DebtorTypeRepository,
    protected readonly custom005CaseAdditionalInfoRepository: Custom005CaseAdditionalInfoRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly debtorRepository: DebtorRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportContractDataDTO['importData'],
  ): Promise<ContractDataPreImportDataDTO[]> {
    const date_columns = [
      'DateOfContracting',
      'DateOfCancellation',
      'LastClientPaymentDate',
      'OldestPhoneNoActivationDate',
      'NewestPhoneNoActivationDate',
      'NextInstallmentDate',
      'LastGDPRNoticeDate',
      'MaturityDate',
      'PaperFileBatchDate',
      'LegalSelectionDate',
      'BackToSoftCollectionDate',
    ] as (keyof ContractDataDTO)[];

    const except_column = ['DebtorType'] as (keyof ContractDataDTO)[];

    const boolean_columns = [
      'AddClosedCasesToGrouping',
      'CaseHasGDPRNotice',
    ] as (keyof ContractDataDTO)[];

    const integer_columns = [
      'InitialDPD',
      'ActualDPD',
      'NumberOfDueInvoices',
      'CountOfPhones',
      'NumberOfAllInvoices',
      'TotalNumberOfContractsLoans',
    ] as (keyof ContractDataDTO)[];
    const float_columns = [
      'PenaltyRate',
      'LastClientPaymentAmount',
      'AllClientPaymentAmount',
      'InitialDebtStructure',
      'ValueOfFirstDueBill',
      'FinancedValue',
      'NumberFirstOverdueInstallment',
      'OverdraftAvailableBalance',
      'InterestRate',
    ] as (keyof ContractDataDTO)[];

    const contractDataPreImportDataDTO: ContractDataPreImportDataDTO[] = [];

    const allColumns = Object.keys(importData[0]) as (keyof ContractDataDTO)[];

    const debtorTypes = await this.debtorTypeRepository.getMap();
    const booleanPositiveAvailableValues = new Set(['1', 'yes', 'true']);
    const booleanNegativeAvailableValues = new Set(['0', 'no', 'false']);

    for (const row of importData) {
      for (const column of allColumns) {
        if (
          boolean_columns.includes(column) &&
          row[column] !== '' &&
          row[column] !== 'null'
        ) {
          if (row[column] === null) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            row[column] = 'null';
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            if (
              booleanPositiveAvailableValues.has(
                String(row[column]).toLowerCase(),
              )
            ) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              row[column] = true;
            } else if (
              booleanNegativeAvailableValues.has(
                String(row[column]).toLowerCase(),
              )
            ) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              row[column] = false;
            } else {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              row[column] = '';
            }
          }
        }

        if (
          integer_columns.includes(column) &&
          row[column] !== '' &&
          row[column] !== 'null'
        ) {
          if (row[column] === null) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            row[column] = 'null';
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            row[column] = Number(row[column]);
          }
        }

        if (
          date_columns.includes(column) &&
          typeof row[column] === 'string' &&
          row[column] !== '' &&
          row[column] !== 'null'
        ) {
          try {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            row[column] = format(
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              parse(row[column], 'dd.MM.yyyy', new Date()),
              // eslint-disable-next-line radar/no-duplicate-string
              'yyyy-MM-dd',
            );
          } catch {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            row[column] = format(
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              parse(row[column], 'yyyy-MM-dd', new Date()),
              'yyyy-MM-dd',
            );
          }
        }

        if (
          float_columns.includes(column) &&
          row[column] !== '' &&
          row[column] !== 'null'
        ) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          row[column] = String(row[column]).replace(',', '.');
        }

        if (column === 'DebtorType') {
          row['DebtorTypeID'] =
            row[column] !== '' && row[column] !== 'null'
              ? debtorTypes[String(row[column])]
              : null;
        }

        if (
          (column === 'PenaltyRate' || column === 'InterestRate') &&
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          row[column] !== '' &&
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          row[column] !== 'null'
        ) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          row[column] = row[column] / 100;
        }

        if (except_column.includes(column)) {
          delete row[column];
        }
      }
      contractDataPreImportDataDTO.push(row);
    }

    return contractDataPreImportDataDTO;
  }

  async messageHandler(
    importData: ContractDataPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const chunks: ContractDataPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const insertItems: Custom005CaseAdditionalInfo[] = [];
      const updateItems: Custom005CaseAdditionalInfo[] = [];
      const updateDebtors: Debtor[] = [];

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const CaseIDs = chunk.map((index) => index.CaseID);
        const existsContractData =
          await this.custom005CaseAdditionalInfoRepository.find({
            where: {
              CaseID: In(CaseIDs),
            },
          });

        const existsCases = await this.caseRepository.find({
          select: ['id', 'debtorId'],
          where: {
            id: In(CaseIDs),
          },
        });

        const DebtorIDs = existsCases.map((c) => c.debtorId);

        const existsDebtors = await this.debtorRepository.find({
          select: ['id', 'typeId'],
          where: {
            id: In(DebtorIDs),
          },
        });

        const exists_cases_map: { [key: string]: string } = {};
        const exists_debtor_map: { [key: string]: number } = {};
        const exists_custom005_map: {
          [key: string]: Custom005CaseAdditionalInfo;
        } = {};

        for (const existsCase of existsCases) {
          exists_cases_map[existsCase.id] = existsCase.debtorId;
        }

        for (const existsDebtor of existsDebtors) {
          exists_debtor_map[existsDebtor.id] = existsDebtor.typeId;
        }

        for (const custom005CaseAdditionalInfo of existsContractData) {
          exists_custom005_map[custom005CaseAdditionalInfo.CaseID] =
            custom005CaseAdditionalInfo;
        }

        for (const row of chunk) {
          if (exists_custom005_map[row.CaseID]) {
            const updatedItem = this.updateItem(
              exists_custom005_map[row.CaseID],
              row,
            );
            updateItems.push(updatedItem);
          } else {
            const insertedItem = this.insertItem(row);
            insertItems.push(insertedItem);
          }

          const debtorID = exists_cases_map[row.CaseID];
          const debtorAttributes = this.updateDebtor(
            row.DebtorTypeID,
            exists_debtor_map[debtorID],
            debtorID,
          );

          if (debtorAttributes) {
            updateDebtors.push(debtorAttributes);
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertItems: insertItems,
        updateItems: updateItems,
        updateDebtors: updateDebtors,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  updateItem(
    values: Custom005CaseAdditionalInfo,
    newValues: ContractDataPreImportDataDTO,
  ): Custom005CaseAdditionalInfo {
    for (const field of Object.keys(values)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const newValue = newValues[field];

      if (newValue !== null) {
        if (newValue === 'null') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = null;
        } else if (newValue !== '') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = newValue;
        }
      }
    }
    return values;
  }

  insertItem(
    values: ContractDataPreImportDataDTO,
  ): Custom005CaseAdditionalInfo {
    for (const field of Object.keys(values)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const newValue = values[field];

      if (newValue !== null && (newValue === 'null' || newValue === '')) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        values[field] = null;
      }
    }

    return this.custom005CaseAdditionalInfoRepository.create(
      values,
    ) as Custom005CaseAdditionalInfo;
  }

  updateDebtor(
    newDebtorTypeID: null | number | undefined,
    originDebtorTypeID: number | undefined,
    debtorId: string | undefined,
  ): Debtor | null {
    if (
      debtorId &&
      newDebtorTypeID &&
      originDebtorTypeID &&
      originDebtorTypeID != newDebtorTypeID
    ) {
      return {
        id: debtorId,
        typeId: newDebtorTypeID,
      } as Debtor;
    }
    return null;
  }
}
