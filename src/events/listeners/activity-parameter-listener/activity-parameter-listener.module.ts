import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { CaseStatusRepository } from '../../../repositories/dictionary/case-status.repository';
import { HistoryTypeRepository } from '../../../repositories/dictionary/history-type.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { ActivityParameterListenerService } from './activity-parameter-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      ParameterRepository,
      HistoryTypeRepository,
      CaseStatusRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [ActivityParameterListenerService, EventsGateway],
})
export class ActivityParameterListenerModule {}
