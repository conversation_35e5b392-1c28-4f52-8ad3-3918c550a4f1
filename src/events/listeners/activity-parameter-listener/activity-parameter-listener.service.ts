import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportWithParameterListenerService } from '../../../common/imports/listeners/base-import-with-parameter-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportActivityParameterDTO } from '../../../import-activity-parameter/dto/import-activity-parameter.dto';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { History } from '../../../entities/activity/history.entity';
import { CaseStatusRepository } from '../../../repositories/dictionary/case-status.repository';
import { HistoryTypeRepository } from '../../../repositories/dictionary/history-type.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { ActivityParameterEvent } from '../../types/activity-parameter-event';
import { ActivityParameterPreImportDataDTO } from './dto/activity-parameter-pre-import-data.dto';

@Injectable()
export class ActivityParameterListenerService extends BaseImportWithParameterListenerService<
  ActivityParameterEvent,
  ActivityParameterPreImportDataDTO
> {
  queueName: Queues.ActivityParameter = Queues.ActivityParameter;
  catalog: CatalogNameEnum = CatalogNameEnum.ActivityParameter;
  catalogId: CatalogEnum = CatalogEnum.ActivityParameter;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly historyRepository: HistoryRepository,
    protected readonly historyTypeRepository: HistoryTypeRepository,
    protected readonly caseStatusRepository: CaseStatusRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
      parameterRepository,
    );
    this.listen().catch((error) => console.log(error));
  }

  async preImport(
    importData: ImportActivityParameterDTO['importData'],
    userId: number,
    uploadHistoryId: string,
  ): Promise<ActivityParameterPreImportDataDTO[]> {
    return this.formatParameters(importData, userId, uploadHistoryId);
  }

  async messageHandler(
    importData: ActivityParameterPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
    additionalParams: { typeId: number },
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const historyType = await this.historyTypeRepository.findOne({
      id: additionalParams.typeId,
    });

    if (!historyType) {
      console.error('History type is not exists:', additionalParams.typeId);
      return true;
    }

    if (historyType.setCaseStatusId) {
      const resultID = await this.caseStatusRepository.findOne({
        id: historyType.setCaseStatusId,
      });

      if (!resultID) {
        console.error(
          'History result is not exists:',
          historyType.setCaseStatusId,
        );
        return true;
      }
    }

    if (importData.length > 0) {
      const histories: any[] = [];
      const activityParameters: any[] = [];
      const historyToCaseID: any = {};
      const activateAppellateCourtMapping: any[] = [];

      const chunks: ActivityParameterPreImportDataDTO[][] =
        this.sliceIntoChunks(importData, 1000);

      const slugToId = await this.getParameterBySlug();
      const parametersHasActivateCourtMapping =
        await this.parameterRepository.getParametersHasActivateCourtMapping();

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      const caseIDs = [
        ...new Set(
          importData.map(
            (index: ActivityParameterPreImportDataDTO) => index.LegalCaseID,
          ),
        ),
      ];

      const historyPool = await this.historyRepository.getPoolHistoryIds(
        caseIDs.length,
      );

      const historyToCaseMapping: { [key: number]: number } = {};
      for (const [index, caseID] of caseIDs.entries()) {
        historyToCaseMapping[caseID] = historyPool[index];
        if (historyToCaseMapping[caseID]) {
          histories.push({
            id: String(historyToCaseMapping[caseID]),
            caseId: caseID,
            typeId: Number(additionalParams.typeId),
            creationUserID: userId,
            isDeleted: 0,
            creationDate: new Date(),
          } as History);
        }
      }

      for (const chunk of chunks) {
        for (const [index, row] of chunk.entries()) {
          const parameters = Object.keys(row).filter(
            (c) => c !== 'LegalCaseID',
          );

          for (const parameter of parameters) {
            const activityParameter = {
              historyId: String(historyToCaseMapping[row.LegalCaseID]),
              parameterId: slugToId[parameter],
              value: row[parameter],
              insertedUserId: userId,
              updatedUserId: userId,
            };
            activityParameters.push(activityParameter);
            historyToCaseID[activityParameter.historyId] = row.LegalCaseID;

            if (
              this.hasAppellateCourtMapping(
                row[parameter],
                slugToId[parameter],
                parametersHasActivateCourtMapping,
              )
            ) {
              activateAppellateCourtMapping.push(row.LegalCaseID);
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
        await this.createLegalContactInHistoryForParameters(chunk, userId);
      }

      const casesToActivateLegalRules = importData.map((r) => r.LegalCaseID);

      await this.saveResult(Number(uploadHistoryId), page, {
        histories,
        activityParameters,
        activateAppellateCourtMapping,
        casesToActivateLegalRules,
        historyToCaseID,
        metaData: {
          userId,
          typeId: Number(additionalParams.typeId),
          changeSettings: historyType,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }
}
