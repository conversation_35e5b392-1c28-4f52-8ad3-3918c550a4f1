import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportWithParameterListenerService } from '../../../common/imports/listeners/base-import-with-parameter-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportCourtParameterDTO } from '../../../import-court-parameter/dto/import-court-parameter.dto';
import { CourtProcessParameterRepository } from '../../../repositories/data/court-process-parameter.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { CourtProcessRepository } from '../../../repositories/legal/court-process.repository';
import { EventsGateway } from '../../events.gateway';
import { CourtParameterEvent } from '../../types/court-parameter-event';
import { CourtParameterPreImportDataDTO } from './dto/court-parameter-pre-import-data.dto';

@Injectable()
export class CourtParameterListenerService extends BaseImportWithParameterListenerService<
  CourtParameterEvent,
  CourtParameterPreImportDataDTO
> {
  queueName: Queues.CourtParameter = Queues.CourtParameter;
  catalog: CatalogNameEnum = CatalogNameEnum.CourtParameter;
  catalogId: CatalogEnum = CatalogEnum.CourtParameter;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly courtProcessRepository: CourtProcessRepository,
    protected readonly courtProcessParameterRepository: CourtProcessParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
      parameterRepository,
    );
    this.listen();
  }

  async preImport(
    importData: ImportCourtParameterDTO['importData'],
    userId: number,
    uploadHistoryId: string,
  ): Promise<CourtParameterPreImportDataDTO[]> {
    return this.formatParameters(importData, userId, uploadHistoryId);
  }

  async messageHandler(
    importData: CourtParameterPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const parametersCreate: any[] = [];
      const parametersDelete: any[] = [];
      const activateAppellateCourtMapping: any[] = [];

      const chunks: CourtParameterPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const slugToId = await this.getParameterBySlug();
      const parametersHasActivateCourtMapping =
        await this.parameterRepository.getParametersHasActivateCourtMapping();

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const caseIDs = chunk.map((row) => row.LegalCaseID);
        const existsCourtProcess = await this.courtProcessRepository.find({
          select: ['id', 'caseId'],
          where: {
            caseId: In(caseIDs),
            isActual: 1,
            isDeleted: 0,
          },
        });

        const courtProcessIds: string[] = [];
        const caseToCourtProcessMap: { [key: string]: string } = {};
        for (const item of existsCourtProcess) {
          caseToCourtProcessMap[item.caseId] = item.id;
          courtProcessIds.push(item.id);
        }

        const existsParameters =
          await this.courtProcessParameterRepository.find({
            where: {
              courtProcessId: In(courtProcessIds),
              isDeleted: 0,
            },
          });

        const courtProcessToExistsParameterMapping: {
          [key: string]: { [key2: string]: string };
        } = {};

        const parameterToValueMapping: {
          [key: string]: any;
        } = {};

        for (const parameter of existsParameters) {
          if (!courtProcessToExistsParameterMapping[parameter.courtProcessId]) {
            courtProcessToExistsParameterMapping[parameter.courtProcessId] = {};
          }
          courtProcessToExistsParameterMapping[parameter.courtProcessId][
            parameter.parameterId
          ] = parameter.id;

          parameterToValueMapping[parameter.id] = parameter.value;
        }

        for (const row of chunk) {
          const parameters = Object.keys(row).filter(
            (c) => c !== 'LegalCaseID',
          );

          const courtProcessId = caseToCourtProcessMap[row.LegalCaseID];

          for (const parameter of parameters) {
            const parameterId = slugToId[parameter];
            const value = row[parameter];

            if (
              courtProcessToExistsParameterMapping[courtProcessId] &&
              courtProcessToExistsParameterMapping[courtProcessId][parameterId]
            ) {
              const existsParameterId =
                courtProcessToExistsParameterMapping[courtProcessId][
                  parameterId
                ];
              const existsValue = parameterToValueMapping[existsParameterId];

              if (value === existsValue || value === '') {
                continue;
              }

              parametersDelete.push(existsParameterId);

              if (String(value).toLowerCase() !== 'null') {
                parametersCreate.push({
                  courtProcessId,
                  parameterId,
                  value,
                  insertedUserId: userId,
                  updatedUserId: userId,
                });

                if (
                  this.hasAppellateCourtMapping(
                    value,
                    parameterId,
                    parametersHasActivateCourtMapping,
                  )
                ) {
                  activateAppellateCourtMapping.push(row.LegalCaseID);
                }
              }
            } else {
              if (value !== '' && String(value).toLowerCase() !== 'null') {
                parametersCreate.push({
                  courtProcessId,
                  parameterId,
                  value,
                  insertedUserId: userId,
                  updatedUserId: userId,
                });

                if (
                  this.hasAppellateCourtMapping(
                    value,
                    parameterId,
                    parametersHasActivateCourtMapping,
                  )
                ) {
                  activateAppellateCourtMapping.push(row.LegalCaseID);
                }
              }
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
        await this.createLegalContactInHistoryForParameters(chunk, userId);
      }

      const casesToActivateLegalRules = importData.map((r) => r.LegalCaseID);
      await this.saveResult(Number(uploadHistoryId), page, {
        parametersCreate,
        parametersDelete,
        activateAppellateCourtMapping,
        userId: userId,
        casesToActivateLegalRules,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }
}
