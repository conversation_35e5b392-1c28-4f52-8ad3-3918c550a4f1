import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { Custom004EEnforcementComplaint } from '../../../entities/legal/custom004-enforcement-complaint.entity';
import { ImportComplaintDTO } from '../../../import-basis-complaint/dto/import-complaint.dto';
import { AvailabilityOptionRepository } from '../../../repositories/dictionary/availability-option.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisComplaintEvent } from '../../types/basis-complaint-event';
import { ComplaintPreImportDataDTO } from './dto/complaint-pre-import-data.dto';

const ImportFormat = 'dd.MM.yyyy';
const SystemFormat = 'yyyy-MM-dd';

@Injectable()
export class BasisComplaintListenerService extends BaseImportListenerService<
  BasisComplaintEvent,
  ComplaintPreImportDataDTO
> {
  queueName: Queues.BasisComplaint = Queues.BasisComplaint;
  catalog: CatalogNameEnum = CatalogNameEnum.BasisComplaint;
  catalogId: CatalogEnum = CatalogEnum.BasisComplaint;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly availabilityOptionRepository: AvailabilityOptionRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportComplaintDTO['importData'],
  ): Promise<ComplaintPreImportDataDTO[]> {
    const employerPreImportDataDTO: ComplaintPreImportDataDTO[] = [];

    const availabilityOptions = await this.availabilityOptionRepository.find({
      isDeleted: 0,
    });

    const availabilityOptionMapping: { [key: string]: number } = {};
    for (const availabilityOption of availabilityOptions) {
      availabilityOptionMapping[availabilityOption.name] =
        availabilityOption.id;
    }

    for (const [index, data] of importData.entries()) {
      if (data.ComplaintDate !== '' && data.ComplaintDate !== 'null') {
        const parsedDate = parse(
          String(data.ComplaintDate),
          ImportFormat,
          new Date(),
        );
        data.ComplaintDate = format(parsedDate, SystemFormat);
      }

      if (data.Suspension !== '' && data.Suspension !== 'null') {
        const parsedDate = parse(
          String(data.Suspension),
          ImportFormat,
          new Date(),
        );
        data.Suspension = format(parsedDate, SystemFormat);
      }

      if (data.FinalJudgment !== '' && data.FinalJudgment !== 'null') {
        const parsedDate = parse(
          String(data.FinalJudgment),
          ImportFormat,
          new Date(),
        );
        data.FinalJudgment = format(parsedDate, SystemFormat);
      }

      if (
        data.SentForDocumentation !== '' &&
        data.SentForDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.SentForDocumentation),
          ImportFormat,
          new Date(),
        );
        data.SentForDocumentation = format(parsedDate, SystemFormat);
      }

      if (
        data.FirstInstanceDecision !== '' &&
        data.FirstInstanceDecision !== 'null'
      ) {
        const parsedDate = parse(
          String(data.FirstInstanceDecision),
          ImportFormat,
          new Date(),
        );
        data.FirstInstanceDecision = format(parsedDate, SystemFormat);
      }

      if (
        data.SecondInstanceDecision !== '' &&
        data.SecondInstanceDecision !== 'null'
      ) {
        const parsedDate = parse(
          String(data.SecondInstanceDecision),
          ImportFormat,
          new Date(),
        );
        data.SecondInstanceDecision = format(parsedDate, SystemFormat);
      }

      if (data.ReceivedDocumentation !== '') {
        data.ReceivedDocumentation =
          availabilityOptionMapping[String(data.ReceivedDocumentation)];
      }

      if (data.Expires !== '') {
        data.Expires = availabilityOptionMapping[String(data.Expires)];
      }

      if (data.Complaint !== '') {
        data.Complaint = availabilityOptionMapping[String(data.Complaint)];
      }

      if (data.DocumentationSentToLawyer !== '') {
        data.DocumentationSentToLawyer =
          availabilityOptionMapping[String(data.DocumentationSentToLawyer)];
      }

      if (data.Foreigner !== '') {
        data.Foreigner = availabilityOptionMapping[String(data.Foreigner)];
      }

      if (data.Result !== '') {
        data.Result = availabilityOptionMapping[String(data.Result)];
      }

      employerPreImportDataDTO.push(data);
    }

    return employerPreImportDataDTO;
  }

  async messageHandler(
    importData: ComplaintPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertComplaint: Custom004EEnforcementComplaint[] = [];

    if (importData.length > 0) {
      const chunks: ComplaintPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        for (const row of chunk) {
          const model = {
            CaseID: row.CaseID,
            BasisID: row.BasisForPaymentID,
            ComplaintDate: this.insertItem(row.ComplaintDate),
            ReceivedDocumentationOptionID: this.insertItem(
              row.ReceivedDocumentation,
            ),
            SentForDocumentationDate: this.insertItem(row.SentForDocumentation),
            DocumentationSentToLawyerOptionID: this.insertItem(
              row.DocumentationSentToLawyer,
            ),
            ExpiresOptionID: this.insertItem(row.Expires),
            ForeignerOptionID: this.insertItem(row.Foreigner),
            SuspensionDate: this.insertItem(row.Suspension),
            FirstInstanceDecisionDate: this.insertItem(
              row.FirstInstanceDecision,
            ),
            ComplaintOptionID: this.insertItem(row.Complaint),
            SecondInstanceDecisionDate: this.insertItem(
              row.SecondInstanceDecision,
            ),
            FinalJudgmentDate: this.insertItem(row.FinalJudgment),
            ResultOptionID: this.insertItem(row.Result),
            InsertedUserID: userId,
            UpdatedUserID: userId,
          } as Custom004EEnforcementComplaint;

          insertComplaint.push(model);
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertComplaint: insertComplaint,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  insertItem(newValue: any): any {
    if (newValue !== null) {
      if (newValue === 'null') {
        return null;
      } else if (newValue !== '') {
        return newValue;
      }
    }
    return null;
  }
}
