/* eslint-disable unicorn/prevent-abbreviations */
import { Entity<PERSON>anager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryCustom005ContactPersonInterface } from './interfaces/temporary-custom005-contact-person.interface';

const TemporaryCustom005ContactPersonEntity = new EntitySchema({
  name: 'TemporaryCustom005ContactPerson',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryCustom005ContactPerson',
  declaration: `
  "ContactPersonID" BIGINT PRIMARY KEY NOT NULL,
  "CompanyFullName" varchar(100),
  "CompanyFiscalCode" varchar(100),
  "CompanyRegistrationNumber" varchar(200),
  "SexID" smallint
  `,
})
export class TemporaryCustom005Contact<PERSON>erson extends TemporaryTable<TemporaryCustom005ContactPersonInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryCustom005ContactPersonEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryCustom005ContactPersonInterface[]): any[] {
    return data.map((v: any) => {
      return {
        ContactPersonID: v['contactPersonId'],
        CompanyFullName: this.nullableStringColumn(v['companyFullName']),
        CompanyFiscalCode: this.nullableStringColumn(v['companyFiscalCode']),
        CompanyRegistrationNumber: this.nullableStringColumn(
          v['companyRegistrationNumber'],
        ),
        SexID: v['sexId'],
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."Custom005ContactPerson" as dcp
                        set
                            "SexID" = ipt."SexID",
                            "CompanyFullName" = ipt."CompanyFullName",
                            "CompanyFiscalCode" = ipt."CompanyFiscalCode",
                            "CompanyRegistrationNumber" = ipt."CompanyRegistrationNumber"
                        from "${this.tableName}" ipt WHERE ipt."ContactPersonID"= dcp."ContactPersonID";
    `);
  }
}
