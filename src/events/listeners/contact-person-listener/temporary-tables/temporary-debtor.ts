/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryDebtorInterface } from './interfaces/temporary-debtor.interface';

const TemporaryDebtorEntity = new EntitySchema({
  name: 'TemporaryDebtor',
  columns: {},
});

@Temporary({
  tableName: 'TemporaryDebtor',
  declaration: `
  "ID" BIGINT PRIMARY KEY NOT NULL ,
  "LastName" VARCHAR(255),
  "FirstName" VARCHAR(255),
  "MiddleName" VARCHAR(255),
  "IsDied" SMALLINT,
  "TaxNumber" VARCHAR(200),
  "BirthDate" DATE,
  "BirthPlace" VARCHAR(200),
  "PassportNumber" VARCHAR(200),
  "PassportIssuingDate" DATE,
  "TypeID" SMALLINT,
  "SexID" SMALLINT,
  "UpdatedUserID" INTEGER
  `,
})
export class TemporaryDebtor extends TemporaryTable<TemporaryDebtorInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryDebtorEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryDebtorInterface[]): any[] {
    return data.map((v: any) => {
      return {
        ID: v['id'],
        LastName: this.nullableStringColumn(v['lastName']),
        FirstName: this.nullableStringColumn(v['firstName']),
        MiddleName: this.nullableStringColumn(v['middleName']),
        IsDied: v['isDied'],
        TaxNumber: this.nullableStringColumn(v['taxNumber']),
        BirthDate: this.nullableStringColumn(v['birthDate']),
        BirthPlace: this.nullableStringColumn(v['birthPlace']),
        PassportNumber: this.nullableStringColumn(v['passportNumber']),
        PassportIssuingDate: this.nullableStringColumn(
          v['passportIssuingDate'],
        ),
        TypeID: v['typeId'],
        SexID: v['sexId'],
        UpdatedUserID: v['updatedUserId'],
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."Debtor" as dd
                        set
                          "TypeID" = ipt."TypeID",
                          "LastName" = ipt."LastName",
                          "FirstName" = ipt."FirstName",
                          "MiddleName" = ipt."MiddleName",
                          "IsDied" = ipt."IsDied",
                          "SexID" = ipt."SexID",
                          "TaxNumber" = ipt."TaxNumber",
                          "BirthDate" = ipt."BirthDate",
                          "BirthPlace" = ipt."BirthPlace",
                          "PassportNumber" = ipt."PassportNumber",
                          "UpdatedUserID" = ipt."UpdatedUserID",
                          "PassportIssuingDate" = ipt."PassportIssuingDate"
                        from "${this.tableName}" "ipt" WHERE ipt."ID"= dd."ID";
    `);
  }
}
