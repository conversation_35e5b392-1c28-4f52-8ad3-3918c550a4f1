/* eslint-disable unicorn/prevent-abbreviations */

export interface TemporaryDebtorInterface {
  id: string;
  typeId: number;
  lastName: string;
  firstName: string;
  middleName: string;
  isDied: number;
  sexId: number;
  taxNumber: string;
  birthDate: string | null;
  birthPlace: string;
  persAccPassword: number;
  passportSeries: string;
  passportNumber: string;
  passportInfo: string;
  groupId: number;
  insertedUserId: number;
  updatedUserId: number;
  caseId2: number;
  personDocumentTypeId: number;
  passportIssuingDate: string | null;
  codeIssuingAuthority: string;
  workPlace: string;
  position: string;
  isDeleted: number;
  updated: Date;
  inserted: Date;
}
