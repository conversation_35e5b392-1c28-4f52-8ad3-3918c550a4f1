/* eslint-disable unicorn/prevent-abbreviations */

export interface TemporaryContactPersonInterface {
  id: string;

  typeId: number;

  debtorTypeId: number;

  lastName: string;

  firstName: string | null;

  middleName: string | null;

  passportSeries: string | null;

  passportNumber: string | null;

  passportInfo: string | null;

  taxNumber: string | null;

  birthDate: string | null;

  value: Record<string, any> | null;

  note: string | null;

  isDebtor: boolean;
}
