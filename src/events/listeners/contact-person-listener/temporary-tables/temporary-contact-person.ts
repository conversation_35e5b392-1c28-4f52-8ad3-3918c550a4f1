/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryContactPersonInterface } from './interfaces/temporary-contact-person.interface';

const TemporaryContactPersonEntity = new EntitySchema({
  name: 'TemporaryContactPerson',
  columns: {},
});

@Temporary({
  tableName: 'TemporaryContactPerson',
  declaration: `
  "ID" BIGINT PRIMARY KEY NOT NULL ,
  "TypeID" SMALLINT,
  "DebtorTypeID" SMALLINT,
  "DataSourceID" SMALLINT,
  "LastName" VARCHAR(255),
  "FirstName" VARCHAR(255),
  "MiddleName" VARCHAR(255),
  "PassportSeries" VARCHAR(2),
  "PassportNumber" VARCHAR(200),
  "PassportInfo" VARCHAR(255),
  "TaxNumber" VARCHAR(280),
  "BirthDate" DATE,
  "Value" jsonb,
  "Note" VARCHAR(200),
  "IsDebtor" boolean
  `,
})
export class TemporaryContactPerson extends TemporaryTable<TemporaryContactPersonInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryContactPersonEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryContactPersonInterface[]): any[] {
    return data.map((v: any) => {
      return {
        ID: v['id'],
        TypeID: v['typeId'],
        DebtorTypeID: v['debtorTypeId'],
        DataSourceID: v['dataSourceId'],
        LastName: this.nullableStringColumn(v['lastName']),
        FirstName: this.nullableStringColumn(v['firstName']),
        MiddleName: this.nullableStringColumn(v['middleName']),
        PassportSeries: this.nullableStringColumn(v['passportSeries']),
        PassportNumber: this.nullableStringColumn(v['passportNumber']),
        PassportInfo: this.nullableStringColumn(v['passportInfo']),
        TaxNumber: this.nullableStringColumn(v['taxNumber']),
        BirthDate: this.nullableStringColumn(v['birthDate']),
        Value: this.jsonColumn(v['value']),
        Note: this.nullableStringColumn(v['note']),
        IsDebtor: this.booleanColumn(v['isDebtor']),
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."ContactPerson" as dcp
                        set
                            "TypeID" = ipt."TypeID",
                            "DebtorTypeID" = ipt."DebtorTypeID",
                            "DataSourceID" = ipt."DataSourceID",
                            "LastName" = ipt."LastName",
                            "FirstName" = ipt."FirstName",
                            "MiddleName" = ipt."MiddleName",
                            "PassportSeries" = ipt."PassportSeries",
                            "PassportNumber" = ipt."PassportNumber",
                            "PassportInfo" = ipt."PassportInfo",
                            "TaxNumber" = ipt."TaxNumber",
                            "Note" = ipt."Note",
                            "BirthDate" = ipt."BirthDate",
                            "Value" = ipt."Value",
                            "IsDebtor" = ipt."IsDebtor"
                        from "${this.tableName}" "ipt" WHERE ipt."ID"= dcp."ID";
    `);
  }
}
