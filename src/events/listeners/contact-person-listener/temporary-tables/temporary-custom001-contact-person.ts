/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryCustom001ContactPersonInterface } from './interfaces/temporary-custom001-contact-person.interface';

const TemporaryCustom001ContactPersonEntity = new EntitySchema({
  name: 'TemporaryCustom001ContactPerson',
  columns: {},
});

@Temporary({
  tableName: 'TemporaryCustom001ContactPerson',
  declaration: `
  "ContactPersonID" BIGINT PRIMARY KEY NOT NULL,
  "DeathDate" DATE,
  "IsDied" boolean
  `,
})
export class TemporaryCustom001ContactPerson extends TemporaryTable<TemporaryCustom001ContactPersonInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryCustom001ContactPersonEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryCustom001ContactPersonInterface[]): any[] {
    return data.map((v: any) => {
      return {
        ContactPersonID: v['contactPersonId'],
        DeathDate: this.nullableStringColumn(v['deathDate']),
        IsDied: this.booleanColumn(v['isDied']),
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."Custom001ContactPerson" as dcp
                        set
                            "IsDied" = ipt."IsDied",
                            "DeathDate" = ipt."DeathDate"
                        from "${this.tableName}" "ipt" WHERE ipt."ContactPersonID"= dcp."ContactPersonID";
    `);
  }
}
