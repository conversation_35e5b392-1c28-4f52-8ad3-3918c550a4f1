/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { TemporaryCustom001DebtorInterface } from './interfaces/temporary-custom001-debtor.interface';

const TemporaryCustom001DebtorEntity = new EntitySchema({
  name: 'TemporaryCustom001Debtor',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryCustom001Debtor',
  declaration: `
  "DebtorID" BIGINT PRIMARY KEY NOT NULL,
  "DeathDate" DATE,
  "RegistrationNumber" VARCHAR(100)
  `,
})
export class TemporaryCustom001Debtor extends TemporaryTable<TemporaryCustom001DebtorInterface> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryCustom001DebtorEntity,
  ) {
    super();
  }

  protected decompose(data: TemporaryCustom001DebtorInterface[]): any[] {
    return data.map((v: any) => {
      return {
        DebtorID: v['debtorId'],
        DeathDate: this.nullableStringColumn(v['deathDate']),
        RegistrationNumber: this.nullableStringColumn(v['registrationNumber']),
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."Custom001Debtor" as dcp
                        set
                            "DeathDate" = ipt."DeathDate",
                            "RegistrationNumber" = ipt."RegistrationNumber"
                        from "${this.tableName}" "ipt" WHERE ipt."DebtorID"= dcp."DebtorID";
    `);
  }
}
