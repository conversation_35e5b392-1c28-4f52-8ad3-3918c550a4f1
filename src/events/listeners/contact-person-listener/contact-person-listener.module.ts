/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ToolSourceRepository } from 'src/repositories/dictionary/tool-source.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { AddressRepository } from '../../../repositories/data/address.repository';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../../../repositories/data/custom001-contact-person.repository';
import { Custom001DebtorRepository } from '../../../repositories/data/custom001-debtor.repository';
import { Custom004DebtorRepository } from '../../../repositories/data/custom004-debtor.repository';
import { Custom005ContactPersonRepository } from '../../../repositories/data/custom005-contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { PhoneRepository } from '../../../repositories/data/phone.repository';
import { DebtorTypeRepository } from '../../../repositories/debtor-type.repository';
import { DataSourceRepository } from '../../../repositories/dictionary/data-source.repository';
import { SexTypeRepository } from '../../../repositories/dictionary/sex-type.repository';
import { EventsGateway } from '../../events.gateway';
import { ContactPersonListenerDataAccess } from './contact-person-listener-data-access';
import { ContactPersonListenerService } from './contact-person-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('importService.url'),
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CaseRepository,
      PartyTypeRepository,
      DebtorTypeRepository,
      ToolSourceRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      ContactPersonRepository,
      DebtorRepository,
      Custom001ContactPersonRepository,
      Custom001DebtorRepository,
      Custom005ContactPersonRepository,
      SexTypeRepository,
      Custom004DebtorRepository,
      PhoneRepository,
      AddressRepository,
      DataSourceRepository,
    ]),
    // EventsModule,
  ],
  providers: [
    ContactPersonListenerService,
    ContactPersonListenerDataAccess,
    EventsGateway,
  ],
})
export class ContactPersonListenerModule {}
