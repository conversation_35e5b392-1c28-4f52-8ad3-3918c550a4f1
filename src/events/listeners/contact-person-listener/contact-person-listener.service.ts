import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { getManager, In } from 'typeorm';
import { Contact<PERSON>erson } from '../../../entities/data/contact-person.entity';
import { Custom001ContactPerson } from '../../../entities/data/custom001-contact-person.entity';
import { Custom001Debtor } from '../../../entities/data/custom001-debtor.entity';
import { Custom005Contact<PERSON>erson } from '../../../entities/data/custom005-contact-person.entity';
import { Debtor } from '../../../entities/data/debtor.entity';
import { ImportContactPersonDTO } from '../../../import-contact-person/dto/import-contact-person.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { AddressRepository } from '../../../repositories/data/address.repository';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../../../repositories/data/custom001-contact-person.repository';
import { Custom004DebtorRepository } from '../../../repositories/data/custom004-debtor.repository';
import { Custom005ContactPersonRepository } from '../../../repositories/data/custom005-contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { PhoneRepository } from '../../../repositories/data/phone.repository';
import { DebtorTypeRepository } from '../../../repositories/debtor-type.repository';
import { DataSourceRepository } from '../../../repositories/dictionary/data-source.repository';
import { SexTypeRepository } from '../../../repositories/dictionary/sex-type.repository';
import { EventsGateway } from '../../events.gateway';
import { ContactPersonEvent } from '../../types/contact-person-event';
import { ContactPersonListenerDataAccess } from './contact-person-listener-data-access';
import { ImportListener } from '../import-listener';
import { ContactPersonPreImportDataDTO } from './dto/contact-person-pre-import-data.dto';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { format, parse } from 'date-fns';
import { TemporaryContactPerson } from './temporary-tables/temporary-contact-person';
import { TemporaryCustom001ContactPerson } from './temporary-tables/temporary-custom001-contact-person';
import { TemporaryCustom001Debtor } from './temporary-tables/temporary-custom001-debtor';
import { TemporaryCustom005ContactPerson } from './temporary-tables/temporary-custom005-contact-person';
import { TemporaryDebtor } from './temporary-tables/temporary-debtor';

@Injectable()
export class ContactPersonListenerService extends ImportListener<
  ContactPersonEvent,
  ContactPersonPreImportDataDTO
> {
  queueName: Queues.ContactPerson = Queues.ContactPerson;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly contactPersonListenerDataAccess: ContactPersonListenerDataAccess,
    private readonly partyTypeRepository: PartyTypeRepository,
    private readonly httpService: HttpService,
    private readonly sexTypeRepository: SexTypeRepository,
    private readonly debtorTypeRepository: DebtorTypeRepository,
    private readonly serviceParameterRepository: ServiceParameterRepository,
    private readonly contactPersonRepository: ContactPersonRepository,
    private readonly phoneRepository: PhoneRepository,
    private readonly addressRepository: AddressRepository,
    private readonly custom001ContactPersonRepository: Custom001ContactPersonRepository,
    private readonly custom005ContactPersonRepository: Custom005ContactPersonRepository,
    private readonly debtorRepository: DebtorRepository,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly custom004DebtorRepository: Custom004DebtorRepository,
    private readonly dataSourceRepository: DataSourceRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: ImportContactPersonDTO['importData'],
  ): Promise<ContactPersonPreImportDataDTO[]> {
    const partyTypes = await this.partyTypeRepository.getList('contact-person');
    const sexTypes = await this.sexTypeRepository.getList();
    const debtorTypes = await this.debtorTypeRepository.getList();
    const dataSource = await this.dataSourceRepository.getContactPersonList();

    const contactPersonPreImportDataDTO: ContactPersonPreImportDataDTO[] = [];

    for (const record of importData) {
      const preImportRecord = plainToClass(
        ContactPersonPreImportDataDTO,
        record,
        {
          excludeExtraneousValues: true,
        },
      );

      if (record.ContactPersonType) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        preImportRecord.TypeID = partyTypes.find(
          (pt) => pt.name === record.ContactPersonType,
        )!.id;
      }

      if (record.ContactPersonDataSource) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        preImportRecord.DataSourceID = dataSource.find(
          (c: any) => c.name === record.ContactPersonDataSource,
        )!.id;
      }

      if (record.Gender) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        preImportRecord.SexID = sexTypes.find(
          (st) => st.name === record.Gender,
        )!.id;
      }

      if (record.ContactPersonDebtorType) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        preImportRecord.DebtorTypeID = debtorTypes.find(
          (dt) => dt.name === record.ContactPersonDebtorType,
        )!.id;
      }

      if (record['PIN/CNP']) {
        preImportRecord.RegistrationNumber = record['PIN/CNP'];
      }

      if (record['SSN']) {
        preImportRecord.ContactPersonOIB = record['SSN'];
      }

      if (Number(record.IsDebtor) !== 0 && Number(record.IsDebtor) !== 1) {
        preImportRecord.IsDebtor = null;
      }

      if (record.ContactPersonBirthDate) {
        preImportRecord.ContactPersonBirthDate = format(
          parse(record.ContactPersonBirthDate, 'dd.MM.yyyy', new Date()),
          'yyyy-MM-dd',
        );
      }

      if (record.DeathDate) {
        preImportRecord.DeathDate = format(
          parse(record.DeathDate, 'dd.MM.yyyy', new Date()),
          'yyyy-MM-dd',
        );
      }

      contactPersonPreImportDataDTO.push(preImportRecord);
    }

    return contactPersonPreImportDataDTO;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => void {
    // const updateImportProgressPublisher = new UpdateImportProgressPublisher(
    //   this.connection,
    // );
    return (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
      // updateImportProgressPublisher.publish({
      //   event: 'UpdateImportProgress',
      //   payload,
      // });
    };
  }

  async messageHandler(
    importData: ContactPersonPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    const caseIds = this.getUniqueValuesInArray(importData, 'CaseID');
    const contactPersonIds = this.getUniqueValuesInArray(
      importData,
      'ContactPersonID',
    );

    const country =
      await this.serviceParameterRepository.getGlobalParameterValueByName(
        'country',
      );
    const defaultDataSourceID =
      (await this.serviceParameterRepository.getGlobalParameterValueByName(
        'defaultContactPersonDataSource',
      )) ?? 7;
    const caseToDebtor = await this.getCaseToDebtorMap(caseIds);
    const contactPersonToDelete: number[] = [];
    const contactPersonToDeleteDebtorId: string[] = [];

    const contactPersonMap = await this.getContactPersonMap(contactPersonIds);

    const contactPersonToCustom001ContactPerson =
      await this.getContactPersonToCustom001(contactPersonIds);

    const contactPersonToCustom005ContactPerson =
      await this.getContactPersonToCustom005(contactPersonIds);

    const createContactPerson: ContactPerson[] = [];
    const updateContactPersons: ContactPerson[] = [];

    const createCustom001ContactPerson: Custom001ContactPerson[] = [];
    const updateCustom001ContactPerson: Custom001ContactPerson[] = [];

    const createCustom005ContactPerson: Custom005ContactPerson[] = [];
    const updateCustom005ContactPerson: Custom005ContactPerson[] = [];

    const deactivateDebtorAttribute: string[] = [];

    const syncDebtorDataWithContactPerson: Array<{
      DebtorID: string;
      contactPerson: ContactPerson;
      isDied: number | null;
      deathDate: string | null;
      registrationNumber: string | null;
      sexId: number | null;
    }> = [];

    for (const data of importData) {
      const caseID = String(data.CaseID);
      const debtorId = caseToDebtor[caseID];

      if (data.ContactPersonID && data.IsDeleted) {
        const existsContactPerson = contactPersonMap[data.ContactPersonID];
        contactPersonToDelete.push(data.ContactPersonID);
        if (existsContactPerson && existsContactPerson.value?.DebtorID) {
          contactPersonToDeleteDebtorId.push(
            existsContactPerson.value.DebtorID,
          );
        }
        continue;
      }

      let isDied = null;
      if (Number(data.IsDied) === 0 || Number(data.IsDied) === 1) {
        isDied = Number(data.IsDied);
      }

      const sexId = data.SexID;

      const deathDate = data.DeathDate === '' ? null : data.DeathDate;
      const registrationNumber =
        data.RegistrationNumber === '' ||
        String(data.RegistrationNumber).toLowerCase() === 'null' ||
        data.RegistrationNumber === null
          ? null
          : data.RegistrationNumber;

      const contactPersonOIB =
        data.ContactPersonOIB === '' ||
        String(data.ContactPersonOIB).toLowerCase() === 'null' ||
        data.ContactPersonOIB === null
          ? null
          : data.ContactPersonOIB;

      if (data.ContactPersonID) {
        const existsContactPerson = contactPersonMap[data.ContactPersonID];

        const updateContactPerson = this.collectUpdateContactPersonFields(
          country as string,
          data,
          existsContactPerson,
        );

        updateContactPersons.push(updateContactPerson);

        const existsCustom001ContactPerson =
          this.collectUpdateCustom001ContactPersonFields(
            String(data.ContactPersonID),
            contactPersonToCustom001ContactPerson[data.ContactPersonID],
            isDied,
            deathDate,
            registrationNumber,
            sexId,
          );
        updateCustom001ContactPerson.push(existsCustom001ContactPerson);

        if (data.IsDebtor !== 1 && data.IsDebtor !== 0) {
          data.IsDebtor = Number(existsContactPerson.isDebtor);
        }

        if (data.IsDebtor) {
          deactivateDebtorAttribute.push(debtorId);
          syncDebtorDataWithContactPerson.push({
            DebtorID: debtorId,
            contactPerson: updateContactPerson,
            isDied,
            deathDate,
            registrationNumber,
            sexId,
          });
        }

        if (data.DebtorTypeID === 2) {
          if (contactPersonToCustom005ContactPerson[data.ContactPersonID]) {
            const existsCustom005ContactPerson =
              this.collectUpdateCustom005ContactPersonFields(
                String(data.ContactPersonID),
                contactPersonToCustom005ContactPerson[data.ContactPersonID],
                registrationNumber,
                data.ContactPersonLastName,
                contactPersonOIB,
                sexId,
              );
            updateCustom005ContactPerson.push(existsCustom005ContactPerson);
          } else {
            const newCustom005ContactPerson =
              this.collectCustom005ContactPersonFields(
                String(data.ContactPersonID),
                registrationNumber,
                data.ContactPersonLastName,
                contactPersonOIB,
                sexId,
              );
            createCustom005ContactPerson.push(newCustom005ContactPerson);
          }
        }
      } else {
        const isDebtor = data?.IsDebtor ? Number(data.IsDebtor) === 1 : false;
        const contactPersonId = await this.getNextContactPersonId();
        const importContactPerson = this.collectCreateContactPersonFields(
          country as string,
          defaultDataSourceID as number,
          contactPersonId,
          data,
          isDebtor,
          debtorId,
        );
        createContactPerson.push(importContactPerson);

        const importCustom001ContactPerson =
          this.collectCreateCustom001ContactPersonFields(
            contactPersonId,
            isDied,
            deathDate,
            registrationNumber,
            sexId,
          );
        createCustom001ContactPerson.push(importCustom001ContactPerson);

        if (data.DebtorTypeID === 2) {
          const newCustom005ContactPerson =
            this.collectCustom005ContactPersonFields(
              contactPersonId,
              registrationNumber,
              data.ContactPersonLastName,
              contactPersonOIB,
              sexId,
            );
          createCustom005ContactPerson.push(newCustom005ContactPerson);
        }

        if (isDebtor) {
          deactivateDebtorAttribute.push(debtorId);
          syncDebtorDataWithContactPerson.push({
            DebtorID: debtorId,
            contactPerson: importContactPerson,
            isDied,
            deathDate,
            registrationNumber,
            sexId,
          });
        }
      }
    }

    const contactPersonsToUpdate = await this.collectContactPersonDataForUpdate(
      updateContactPersons,
    );

    const { debtorToUpdate, custom001DebtorToUpdate } =
      await this.collectDebtorDataForUpdate(
        syncDebtorDataWithContactPerson,
        userId,
      );

    const chunk = this.config.get<number>('chunk.default')!;

    try {
      await getManager().transaction(async (manager) => {
        if (deactivateDebtorAttribute.length > 0) {
          await this.contactPersonRepository
            .createQueryBuilder('cp')
            .update()
            .set({ isDebtor: false })
            .where(
              `"IsDebtor" is true and CAST("Value"->>'DebtorID' as integer) in (${deactivateDebtorAttribute.join(
                ',',
              )})`,
            )
            .execute();
        }

        if (contactPersonsToUpdate.length > 0) {
          const temporaryContactPersonTable = new TemporaryContactPerson(
            manager,
            this.config,
          );
          await temporaryContactPersonTable.initialize();
          await temporaryContactPersonTable.insert(contactPersonsToUpdate);
        }

        if (updateCustom001ContactPerson.length > 0) {
          const temporaryCustom001ContactPersonTable =
            new TemporaryCustom001ContactPerson(manager, this.config);
          await temporaryCustom001ContactPersonTable.initialize();
          await temporaryCustom001ContactPersonTable.insert(
            updateCustom001ContactPerson,
          );
        }

        if (updateCustom005ContactPerson.length > 0) {
          const temporaryCustom005ContactPersonTable =
            new TemporaryCustom005ContactPerson(manager, this.config);
          await temporaryCustom005ContactPersonTable.initialize();
          await temporaryCustom005ContactPersonTable.insert(
            updateCustom005ContactPerson,
          );
        }

        await Promise.all([
          await this.contactPersonRepository.transactionSave(
            manager,
            createContactPerson,
            { chunk },
          ),
          await this.custom001ContactPersonRepository.transactionSave(
            manager,
            createCustom001ContactPerson,
            { chunk },
          ),
          await this.custom005ContactPersonRepository.transactionSave(
            manager,
            createCustom005ContactPerson,
            { chunk },
          ),
        ]);

        if (debtorToUpdate.length > 0) {
          const temporaryDebtorTable = new TemporaryDebtor(
            manager,
            this.config,
          );
          await temporaryDebtorTable.initialize();
          await temporaryDebtorTable.insert(debtorToUpdate);
        }

        if (custom001DebtorToUpdate.length > 0) {
          const temporaryDebtorTable = new TemporaryCustom001Debtor(
            manager,
            this.config,
          );
          await temporaryDebtorTable.initialize();
          await temporaryDebtorTable.insert(custom001DebtorToUpdate);
        }

        if (contactPersonToDelete.length > 0) {
          const rows = await this.contactPersonRepository
            .createQueryBuilder('cp')
            .select(
              '"Value"->\'PhoneID\' as "PhoneID",\n' +
                ' "Value"->\'AddressID\' as "AddressID"',
            )
            .where(`"ID" in (${contactPersonToDelete.join(',')})`)
            .getRawMany();

          const phoneIDs: number[] = [];
          const addressIDs: number[] = [];

          for (const row of rows) {
            if (row['PhoneID']) {
              phoneIDs.push(...row['PhoneID']);
            }

            if (row['AddressID']) {
              addressIDs.push(...row['AddressID']);
            }
          }

          if (phoneIDs.length > 0) {
            await this.phoneRepository
              .createQueryBuilder('dp')
              .update()
              .set({
                isDeleted: 2,
                updatedUserId: userId,
                updatedIsDeleted: new Date(),
              })
              .where(`"ID" in (${phoneIDs.join(',')})`)
              .execute();
          }

          if (addressIDs.length > 0) {
            await this.addressRepository
              .createQueryBuilder('da')
              .update()
              .set({
                statusId: 2,
                updatedUserId: userId,
              })
              .where(`"ID" in (${addressIDs.join(',')})`)
              .execute();
          }

          await this.contactPersonRepository
            .createQueryBuilder('cp')
            .update()
            .set({ isDeleted: 1 })
            .where(`"ID" in (${contactPersonToDelete.join(',')})`)
            .execute();
        }
      });

      updateImportProgress(100);

      const path = await this.saveReport(
        [
          {
            ImportedContactPerson: createContactPerson.length,
            UpdatedContactPerson: updateContactPersons.length,
          },
        ],
        userId,
        uploadHistory!.id,
      );

      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Finished, resultFilePath: path },
      );
    } catch (error) {
      console.log('exception', error);
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Failed },
      );
    }

    await this.updateCustom004Debtor(
      createContactPerson,
      contactPersonsToUpdate,
      contactPersonToDeleteDebtorId,
      deactivateDebtorAttribute,
      userId,
    );

    return true;
  }

  private async updateCustom004Debtor(
    create: ContactPerson[],
    update: ContactPerson[],
    contactPersonToDeleteDebtorId: string[],
    deletedDebtorIDs: string[],
    userId: number,
  ) {
    const featureEnabled =
      await this.serviceParameterRepository.getImportServiceParameterValueByName<boolean>(
        'useCustom004Debtor',
      );

    if (featureEnabled) {
      let debtorIds: string[] = [];
      const updatedDebtorIDs: string[] = [];

      if (contactPersonToDeleteDebtorId.length > 0) {
        debtorIds.push(...contactPersonToDeleteDebtorId);
      }

      for (const item of create) {
        if (item?.value?.DebtorID) {
          debtorIds.push(item.value.DebtorID);
        }
      }

      for (const item of update) {
        if (item?.value?.DebtorID) {
          debtorIds.push(item.value.DebtorID);
          updatedDebtorIDs.push(item.value.DebtorID);
        }
      }

      for (const debtorId of deletedDebtorIDs) {
        debtorIds.push(debtorId);
        updatedDebtorIDs.push(debtorId);
      }

      const existsGroups = await this.custom004DebtorRepository.find({
        where: {
          debtorId: In(updatedDebtorIDs),
          isDeleted: 0,
        },
      });

      if (existsGroups.length > 0) {
        const uniqueGroups = existsGroups.map((index) => index.groupUUID);
        const existsGroupDebtors = await this.custom004DebtorRepository.find({
          select: ['debtorId'],
          where: {
            groupUUID: In(uniqueGroups),
            isDeleted: 0,
          },
        });
        debtorIds = [
          ...debtorIds,
          ...existsGroupDebtors.map((item) => item.debtorId),
        ];
        await this.custom004DebtorRepository.update(
          {
            debtorId: In(updatedDebtorIDs),
            isDeleted: 0,
          },
          {
            isDeleted: 1,
            deletedUserId: userId,
            changeGroupReasonId: 1,
          },
        );

        await this.custom004DebtorRepository.update(
          {
            groupUUID: In(uniqueGroups),
            isDeleted: 0,
          },
          {
            isDeleted: 1,
            changeGroupReasonId: 2,
          },
        );
      }

      const importServiceURL = this.config.get<string>('importService.url');
      try {
        const response = await this.httpService
          .post(importServiceURL + '/update-debtor-group', {
            Debtors: debtorIds,
            userID: userId,
          })
          .toPromise();
      } catch (error: any) {
        console.log(error);
      }
    }
  }

  private getUniqueValuesInArray<T, K extends keyof T>(
    array: T[],
    key: K,
  ): T[K][] {
    const values = new Set(
      array.filter((item) => item[key]).map((item) => item[key]),
    );
    return [...values];
  }

  private async getCaseToDebtorMap(caseIds: number[]): Promise<{
    [key: string]: string;
  }> {
    const map: { [key: string]: string } = {};

    const existCases =
      await this.contactPersonListenerDataAccess.getCaseIdWithDebtor(caseIds);
    for (const caseModel of existCases) {
      map[String(caseModel.id)] = caseModel.debtorId;
    }

    return map;
  }
  private async getContactPersonToCustom001(
    contactPersonIds: number[],
  ): Promise<{
    [key: string]: Custom001ContactPerson;
  }> {
    const map: { [key: string]: Custom001ContactPerson } = {};

    const existItems =
      await this.contactPersonListenerDataAccess.getCustom001ContactPerson(
        contactPersonIds,
      );
    for (const model of existItems) {
      map[model.contactPersonId] = model;
    }

    return map;
  }

  private async getContactPersonMap(contactPersonIds: number[]): Promise<{
    [key: string]: ContactPerson;
  }> {
    const map: { [key: string]: ContactPerson } = {};

    const existItems =
      await this.contactPersonListenerDataAccess.getContactPerson(
        contactPersonIds,
      );
    for (const model of existItems) {
      map[model.id] = model;
    }

    return map;
  }

  private async getContactPersonToCustom005(
    contactPersonIds: number[],
  ): Promise<{
    [key: string]: Custom005ContactPerson;
  }> {
    const map: { [key: string]: Custom005ContactPerson } = {};

    const existItems =
      await this.contactPersonListenerDataAccess.getCustom005ContactPerson(
        contactPersonIds,
      );
    for (const model of existItems) {
      map[model.contactPersonId] = model;
    }

    return map;
  }

  private async collectContactPersonDataForUpdate(
    contactPersons: ContactPerson[],
  ) {
    const contactPersonsToUpdate: ContactPerson[] = [];

    if (contactPersons.length > 0) {
      const contactPersonsBeforeUpdate =
        await this.contactPersonRepository.find({
          where: { id: In(contactPersons.map((c) => c.id)) },
        });

      for (const contactPersonBeforeUpdate of contactPersonsBeforeUpdate) {
        contactPersonsToUpdate.push(
          Object.assign(
            {},
            contactPersonBeforeUpdate,
            contactPersons.find((c) => c.id === contactPersonBeforeUpdate.id),
          ),
        );
      }
    }
    return contactPersonsToUpdate;
  }

  private async collectDebtorDataForUpdate(
    debtors: Array<{
      DebtorID: string;
      contactPerson: ContactPerson;
      isDied: number | null;
      deathDate: string | null;
      registrationNumber: string | null;
      sexId: number | null;
    }>,
    userId: number,
  ): Promise<{
    custom001DebtorToUpdate: Custom001Debtor[];
    debtorToUpdate: Debtor[];
  }> {
    const debtorFieldsForSync = [
      'lastName',
      'firstName',
      'middleName',
      'isDied',
      'taxNumber',
      'birthDate',
      'birthPlace',
      'passportNumber',
      'passportIssuingDate',
      'sexId',
    ];

    const debtorToUpdate: Debtor[] = [];
    const custom001DebtorToUpdate: Custom001Debtor[] = [];

    const featureEnabled =
      await this.serviceParameterRepository.getCaseServiceParameterValueByName<boolean>(
        'syncContactPersonWithDebtor',
      );

    if (featureEnabled && debtors.length > 0) {
      const existDebtorIds = [];
      for (const debtor of debtors) {
        existDebtorIds.push(Number(debtor.DebtorID));
      }

      const existsDebtors: Debtor[] =
        await this.contactPersonListenerDataAccess.getDebtors(existDebtorIds);

      const existsDebtorsMap: { [key: string]: Debtor } = {};
      for (const debtor of existsDebtors) {
        existsDebtorsMap[debtor.id] = debtor;
      }

      const existsCustom001Debtors: Custom001Debtor[] =
        await this.contactPersonListenerDataAccess.getCustom001Debtors(
          existDebtorIds,
        );

      const existsCustom001DebtorsMap: { [key: string]: Custom001Debtor } = {};
      for (const debtor of existsCustom001Debtors) {
        existsCustom001DebtorsMap[debtor.debtorId] = debtor;
      }

      for (const debtor of debtors) {
        const existDebtor = existsDebtorsMap[debtor.DebtorID];
        const existCustom001Debtor = existsCustom001DebtorsMap[debtor.DebtorID];
        const cp = debtor.contactPerson;
        if (existDebtor && cp) {
          let hasChanges = false;
          let custom001Changed = false;
          for (const field of debtorFieldsForSync) {
            if (
              cp.hasOwnProperty(field) &&
              existDebtor.hasOwnProperty(field) &&
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              existDebtor[field] !== cp[field]
            ) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              existDebtor[field] = cp[field];
              hasChanges = true;
            }
          }

          if (cp.debtorTypeId && cp.debtorTypeId !== existDebtor.typeId) {
            existDebtor.typeId = cp.debtorTypeId;
            hasChanges = true;
          }

          if (debtor.sexId && debtor.sexId !== existDebtor.sexId) {
            existDebtor.sexId = debtor.sexId;
            hasChanges = true;
          }

          if (debtor.isDied !== null && existDebtor.isDied !== debtor.isDied) {
            existDebtor.isDied = debtor.isDied;
            existCustom001Debtor.deathDate = !existDebtor.isDied
              ? null
              : debtor.deathDate;
            hasChanges = true;
            custom001Changed = true;
          }

          if (
            debtor.registrationNumber !== null &&
            existCustom001Debtor.registrationNumber !==
              debtor.registrationNumber
          ) {
            existCustom001Debtor.registrationNumber = debtor.registrationNumber;
            hasChanges = true;
            custom001Changed = true;
          }

          if (hasChanges) {
            existDebtor.updatedUserId = userId;
            debtorToUpdate.push(existDebtor);
          }

          if (custom001Changed) {
            custom001DebtorToUpdate.push(existCustom001Debtor);
          }
        }
      }
    }

    return { debtorToUpdate, custom001DebtorToUpdate };
  }

  private collectUpdateContactPersonFields(
    country: string,
    data: ContactPersonPreImportDataDTO,
    contactPerson: ContactPerson,
  ): ContactPerson {
    let isDebtor = contactPerson.isDebtor;
    if (data.IsDebtor) {
      isDebtor = Boolean(data.IsDebtor);
    }

    let typeId = contactPerson.typeId;
    if (data.TypeID) {
      typeId = data.TypeID;
    }

    let debtorTypeId = contactPerson.debtorTypeId;
    if (data.DebtorTypeID) {
      debtorTypeId = data.DebtorTypeID;
    }

    let lastName = contactPerson.lastName;
    if (data.ContactPersonLastName) {
      lastName = data.ContactPersonLastName;
    }

    let firstName = contactPerson.firstName;
    if (data.ContactPersonFirstName) {
      firstName = data.ContactPersonFirstName;
    }
    if (String(data.ContactPersonFirstName).toLowerCase() === 'null') {
      firstName = null;
    }

    let middleName = contactPerson.middleName;
    if (data.ContactPersonMiddleName) {
      middleName = data.ContactPersonMiddleName;
    }
    if (String(data.ContactPersonMiddleName).toLowerCase() === 'null') {
      middleName = null;
    }

    let passportSeries = contactPerson.passportSeries;
    if (data.ContactPersonPassportSeries) {
      passportSeries = data.ContactPersonPassportSeries;
    }
    if (String(data.ContactPersonPassportSeries).toLowerCase() === 'null') {
      passportSeries = null;
    }

    let passportNumber = contactPerson.passportNumber;
    if (data.ContactPersonPassportNumber) {
      passportNumber = data.ContactPersonPassportNumber;
    }
    if (String(data.ContactPersonPassportNumber).toLowerCase() === 'null') {
      passportNumber = null;
    }

    let passportInfo = contactPerson.passportInfo;
    if (data.ContactPersonPassportInfo) {
      passportInfo = data.ContactPersonPassportInfo;
    }
    if (String(data.ContactPersonPassportInfo).toLowerCase() === 'null') {
      passportInfo = null;
    }

    let taxNumber = contactPerson.taxNumber;
    if (data.ContactPersonOIB) {
      taxNumber = data.ContactPersonOIB;
    }
    if (String(data.ContactPersonOIB).toLowerCase() === 'null') {
      taxNumber = null;
    }

    let birthDate = contactPerson.birthDate;
    if (data.ContactPersonBirthDate) {
      birthDate = data.ContactPersonBirthDate;
    }
    if (String(data.ContactPersonBirthDate).toLowerCase() === 'null') {
      birthDate = null;
    }

    let note = contactPerson.note;
    if (data.ContactPersonNote) {
      note = data.ContactPersonNote;
    }
    if (String(data.ContactPersonNote).toLowerCase() === 'null') {
      note = null;
    }

    const attributes: any = {
      id: data.ContactPersonID,
      typeId,
      debtorTypeId,
      lastName,
      firstName,
      middleName,
      passportSeries,
      passportNumber,
      passportInfo,
      taxNumber,
      birthDate,
      note,
      isDebtor,
    };

    if (country === 'Romania' && data.DataSourceID) {
      attributes.dataSourceId = data.DataSourceID;
    }

    return this.contactPersonRepository.create(
      attributes as unknown as ContactPerson,
    );
  }

  private collectCreateContactPersonFields(
    country: string,
    defaultDataSourceID: number,
    contactPersonId: string,
    data: ContactPersonPreImportDataDTO,
    isDebtor: boolean,
    debtorId: string,
  ): ContactPerson {
    const attributes: any = {
      id: contactPersonId,
      typeId: data.TypeID,
      firstName:
        String(data?.ContactPersonFirstName).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonFirstName,
      lastName: data?.ContactPersonLastName,
      middleName:
        String(data?.ContactPersonMiddleName).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonMiddleName,
      passportSeries:
        String(data?.ContactPersonPassportSeries).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonPassportSeries,
      passportNumber:
        String(data?.ContactPersonPassportNumber).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonPassportNumber,
      passportInfo:
        String(data?.ContactPersonPassportInfo).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonPassportInfo,
      taxNumber:
        String(data?.ContactPersonOIB).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonOIB,
      birthDate: data?.ContactPersonBirthDate
        ? data?.ContactPersonBirthDate
        : null,
      value: { DebtorID: Number(debtorId) },
      note:
        String(data?.ContactPersonNote).toLowerCase() === 'null'
          ? null
          : data?.ContactPersonNote,
      debtorTypeId: data?.DebtorTypeID,
      isDebtor: isDebtor,
      isDeleted: 0,
    };

    if (country === 'Romania') {
      attributes.dataSourceId = data.DataSourceID ?? defaultDataSourceID;
    }

    return this.contactPersonRepository.create(attributes as ContactPerson);
  }

  private collectCreateCustom001ContactPersonFields(
    id: string,
    isDied: number | null,
    deathDate: string | null,
    registrationNumber: string | null,
    sexId: number | null,
  ): Custom001ContactPerson {
    const attributes: Partial<Custom001ContactPerson> = {
      contactPersonId: id,
      isDied: isDied ? Boolean(isDied) : false,
      deathDate: isDied ? deathDate : null,
      registrationNumber: registrationNumber,
    };

    if (sexId) {
      attributes.sexId = sexId;
    }
    return this.custom001ContactPersonRepository.create(attributes);
  }

  private collectUpdateCustom001ContactPersonFields(
    id: string,
    currentRecord: Custom001ContactPerson,
    isDied: number | null,
    deathDate: string | null,
    registrationNumber: string | null,
    sexId: number | null,
  ): Custom001ContactPerson {
    let newDeathDate = null;
    let newSexId = null;
    let newRegistrationNumber = null;

    if (isDied) {
      newDeathDate = deathDate ? deathDate : currentRecord.deathDate;
    }
    if (registrationNumber) {
      newRegistrationNumber = registrationNumber
        ? registrationNumber
        : currentRecord.registrationNumber;
    }
    if (sexId) {
      newSexId = sexId ? sexId : currentRecord.sexId;
    }
    return this.custom001ContactPersonRepository.create({
      contactPersonId: id,
      deathDate: newDeathDate,
      sexId: newSexId,
      registrationNumber: newRegistrationNumber,
      ...(isDied !== null && { isDied: Boolean(isDied) }),
    } as unknown as Custom001ContactPerson);
  }

  private collectUpdateCustom005ContactPersonFields(
    id: string,
    currentRecord: Custom005ContactPerson,
    companyFiscalCode: string | null,
    companyFullName: string | null,
    companyRegistrationNumber: string | null,
    sexId: number | null,
  ): Custom005ContactPerson {
    let newFiscalCode = null;
    let newSexId = null;
    let newRegistrationNumber = null;
    let newCompanyFullName = null;

    if (companyFiscalCode) {
      newFiscalCode = currentRecord.companyFiscalCode;
      if (companyFiscalCode) {
        newFiscalCode = companyFiscalCode;
      }
      if (String(companyFiscalCode).toLowerCase() === 'null') {
        newFiscalCode = null;
      }
    }

    if (companyFullName) {
      newCompanyFullName = companyFullName
        ? companyFullName
        : currentRecord.companyFullName;
    }

    if (companyRegistrationNumber) {
      newRegistrationNumber = currentRecord.companyRegistrationNumber;
      if (companyRegistrationNumber) {
        newRegistrationNumber = companyRegistrationNumber;
      }

      if (String(companyRegistrationNumber).toLowerCase() === 'null') {
        newRegistrationNumber = null;
      }
    }

    if (sexId) {
      newSexId = sexId ? sexId : currentRecord.sexId;
    }
    return this.custom005ContactPersonRepository.create({
      contactPersonId: id,
      companyFullName: newCompanyFullName,
      companyFiscalCode: newFiscalCode,
      sexId: newSexId,
      companyRegistrationNumber: newRegistrationNumber,
    } as unknown as Custom005ContactPerson);
  }

  private collectCustom005ContactPersonFields(
    id: string,
    companyFiscalCode: string | null,
    companyFullName: string | null,
    companyRegistrationNumber: string | null,
    sexId: number | null,
  ): Custom005ContactPerson {
    return this.custom005ContactPersonRepository.create({
      contactPersonId: id,
      companyFullName: companyFullName,
      companyFiscalCode: companyFiscalCode,
      sexId: sexId,
      companyRegistrationNumber: companyRegistrationNumber,
    } as unknown as Custom005ContactPerson);
  }

  private async getNextContactPersonId(): Promise<string> {
    return String(
      (
        await this.contactPersonListenerDataAccess.getPoolContactPersonIds(1)
      )[0],
    );
  }
}
