import { Expose } from 'class-transformer';

export class ContactPersonPreImportDataDTO {
  @Expose()
  ContactPersonID: number;

  @Expose()
  CaseID: number;

  @Expose()
  TypeID: number;

  @Expose()
  IsDebtor: number | null;

  @Expose()
  DebtorTypeID: number;

  @Expose()
  DataSourceID: number;

  @Expose()
  SexID: number;

  @Expose()
  ContactPersonFirstName: string;

  @Expose()
  ContactPersonLastName: string;

  @Expose()
  ContactPersonMiddleName: string;

  @Expose()
  ContactPersonPassportSeries: string;

  @Expose()
  ContactPersonPassportNumber: string;

  @Expose()
  ContactPersonPassportInfo: string;

  @Expose()
  ContactPersonOIB: string;

  @Expose()
  ContactPersonBirthDate: string;

  @Expose()
  ContactPersonNote: string;

  @Expose()
  IsDied: number;

  @Expose()
  DeathDate: string;

  @Expose()
  RegistrationNumber: string;

  @Expose()
  IsDeleted: number;
}
