/* eslint-disable unicorn/prevent-abbreviations */
import { Injectable } from '@nestjs/common';

import { CaseRepository } from 'src/repositories/data/case.repository';
import { ToolSourceRepository } from 'src/repositories/dictionary/tool-source.repository';
import { get<PERSON><PERSON><PERSON>, In } from 'typeorm';
import { Case } from '../../../entities/data/case.entity';
import { Contact<PERSON>erson } from '../../../entities/data/contact-person.entity';
import { Custom001ContactPerson } from '../../../entities/data/custom001-contact-person.entity';
import { Custom001Debtor } from '../../../entities/data/custom001-debtor.entity';
import { Custom005ContactPerson } from '../../../entities/data/custom005-contact-person.entity';
import { Debtor } from '../../../entities/data/debtor.entity';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../../../repositories/data/custom001-contact-person.repository';
import { Custom001DebtorRepository } from '../../../repositories/data/custom001-debtor.repository';
import { Custom005ContactPersonRepository } from '../../../repositories/data/custom005-contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';

@Injectable()
export class ContactPersonListenerDataAccess {
  constructor(
    private caseRepository: CaseRepository,
    private debtorRepository: DebtorRepository,
    private contactPersonRepository: ContactPersonRepository,
    private custom001DebtorRepository: Custom001DebtorRepository,
    private custom001ContactPersonRepository: Custom001ContactPersonRepository,
    private custom005ContactPersonRepository: Custom005ContactPersonRepository,
    private toolSourceRepository: ToolSourceRepository,
  ) {}

  public async getCaseIdWithDebtor(
    caseIds: number[],
  ): Promise<Pick<Case, 'id' | 'debtorId'>[]> {
    return this.caseRepository.find({
      select: ['id', 'debtorId'],
      where: { id: In(caseIds) },
    });
  }

  public async getContactPerson(ids: number[]): Promise<ContactPerson[]> {
    return this.contactPersonRepository.find({
      where: { id: In(ids) },
    });
  }
  public async getCustom001ContactPerson(
    ids: number[],
  ): Promise<Custom001ContactPerson[]> {
    return this.custom001ContactPersonRepository.find({
      where: { contactPersonId: In(ids) },
    });
  }

  public async getCustom005ContactPerson(
    ids: number[],
  ): Promise<Custom005ContactPerson[]> {
    return this.custom005ContactPersonRepository.find({
      where: { contactPersonId: In(ids) },
    });
  }

  public async getDebtors(debtorIds: number[]): Promise<Debtor[]> {
    return this.debtorRepository.find({
      where: { id: In(debtorIds) },
    });
  }

  public async getCustom001Debtors(
    debtorIds: number[],
  ): Promise<Custom001Debtor[]> {
    return this.custom001DebtorRepository.find({
      where: { debtorId: In(debtorIds) },
    });
  }

  public async getPoolContactPersonIds(count: number): Promise<number[]> {
    const [sequence] = await getManager().query(
      `
      SELECT last_value + 1 AS from, setval('"Data"."ContactPerson_ID_seq"', last_value + $1) as to
      FROM (SELECT last_value FROM "Data"."ContactPerson_ID_seq") as last_sequence`,
      [count],
    );

    return this.range(Number(sequence.from), Number(sequence.to));
  }

  private range(start: number, end: number) {
    const result = [];
    for (let index = start; index <= end; index++) {
      result.push(index);
    }
    return result;
  }
}
