import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import {
  format,
  parse,
  parseISO,
  setHours,
  setMinutes,
  setSeconds,
} from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { Comment } from '../../../entities/activity/comment.entity';
import { History } from '../../../entities/activity/history.entity';
import { SMSService } from '../../../entities/activity/sms-service.entity';
import { Case } from '../../../entities/data/case.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { UploadDTO } from '../../../import-sms-activity/dto/upload.dto';
import { CommentRepository } from '../../../repositories/activity/comment.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { SmsServiceRepository } from '../../../repositories/activity/sms-service.repository';
import { PhoneRepository } from '../../../repositories/data/phone.repository';
import { EventsGateway } from '../../events.gateway';
import { SmsActivityEvent } from '../../types/sms-activity-event';
import { SmsActivityPreImportDataDto } from './dto/sms-activity-pre-import-data.dto';

const ImportFormat = 'dd.MM.yyyy';
const SystemFormat = 'yyyy-MM-dd';
@Injectable()
export class SmsActivityListenerService extends BaseImportListenerService<
  SmsActivityEvent,
  SmsActivityPreImportDataDto
> {
  queueName: Queues.SmsActivity = Queues.SmsActivity;
  catalog: CatalogNameEnum = CatalogNameEnum.SmsActivity;
  catalogId: CatalogEnum = CatalogEnum.SmsActivity;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly historyRepository: HistoryRepository,
    protected readonly commentRepository: CommentRepository,
    protected readonly smsServiceRepository: SmsServiceRepository,
    protected readonly phoneRepository: PhoneRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: UploadDTO['importData'],
  ): Promise<SmsActivityPreImportDataDto[]> {
    const preImportDataDTO: SmsActivityPreImportDataDto[] = [];

    for (const [index, data] of importData.entries()) {
      const parsedDate = parse(
        String(data.ActivityDate),
        ImportFormat,
        new Date(),
      );
      data.ActivityDate = format(parsedDate, SystemFormat);
      preImportDataDTO.push(data);
    }

    return preImportDataDTO;
  }

  async messageHandler(
    importData: SmsActivityPreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
    additionalParams: any,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const activityTimeFrom = additionalParams['ActivityTimeFrom'];
    const activityTimeTo = additionalParams['ActivityTimeTo'];
    const activityTypeID = await this.getDefaultTypeID();
    const systemUserId = 1;
    const smsStatusId = await this.getDefaultStatusID();

    const importActivities: History[] = [];
    const importSmsServices: SMSService[] = [];
    const importComments: Comment[] = [];

    if (importData.length > 0) {
      const chunks: SmsActivityPreImportDataDto[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const historyPool = await this.historyRepository.getPoolHistoryIds(
          chunk.length,
        );
        const phoneToIdMapping = await this.getPhoneMapping(chunk);

        for (const [index, row] of chunk.entries()) {
          const activityDateTime = this.generateTimestamp(
            row.ActivityDate,
            activityTimeFrom,
            activityTimeTo,
          );
          const historyId = String(historyPool[index]);

          const history = this.historyRepository.create({
            id: historyId,
            caseId: row.CaseID,
            typeId: activityTypeID,
            phoneId: phoneToIdMapping[row.CaseID][row.PhoneNumber],
            creationUserID: systemUserId,
            updatedUserID: systemUserId,
            creationDate: activityDateTime,
          });

          const comment = this.commentRepository.create({
            historyId: historyId,
            value: row.ActivityNote,
          });

          const smsService = this.smsServiceRepository.create({
            historyId: historyId,
            statusId: smsStatusId,
            statusDate: history.creationDate,
          });

          importActivities.push(history);
          importComments.push(comment);
          importSmsServices.push(smsService);
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        importActivities,
        importComments,
        importSmsServices,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  protected generateTimestamp(
    date: string,
    timeFrom: string,
    timeTo: string,
  ): string {
    const [fromH, fromM, fromS] = timeFrom
      .split(':')
      .map((element) => Number(element));
    const [toH, toM, toS] = timeTo.split(':').map((element) => Number(element));

    const fromTotalSeconds = fromH * 3600 + fromM * 60 + fromS;
    const toTotalSeconds = toH * 3600 + toM * 60 + toS;

    const randomTotalSeconds =
      fromTotalSeconds +
      Math.floor(Math.random() * (toTotalSeconds - fromTotalSeconds + 1));
    const randomHour = Math.floor(randomTotalSeconds / 3600);
    const randomMinute = Math.floor((randomTotalSeconds % 3600) / 60);
    const randomSecond = randomTotalSeconds % 60;

    let dateObj = parseISO(date);
    dateObj = setHours(dateObj, randomHour);
    dateObj = setMinutes(dateObj, randomMinute);
    dateObj = setSeconds(dateObj, randomSecond);

    return format(dateObj, 'yyyy-MM-dd HH:mm:ss');
  }

  protected async getPhoneMapping(
    rows: SmsActivityPreImportDataDto[],
  ): Promise<{ [key: number]: { [key: string]: number } }> {
    const mapping: { [key: number]: { [key: string]: number } } = {};
    const phoneNumbers = rows.map((r) => r.PhoneNumber);
    const caseIds = rows.map((r) => r.CaseID);

    const results = await this.phoneRepository
      .createQueryBuilder('dp')
      .select(['dc."ID"', 'dp."PhoneNumber"', 'dp."ID" as "PhoneID"'])
      .leftJoinAndSelect(Case, 'dc', 'dc."DebtorID" = dp."DebtorID"')
      .where('dp."PhoneNumber" IN (:...phoneNumbers)', { phoneNumbers })
      .andWhere('dc."ID" IN (:...caseIds)', { caseIds })
      .andWhere('dc."IsDeleted" = :isDeleted', { isDeleted: 0 })
      .getRawMany();

    for (const result of results) {
      mapping[result.ID] = mapping[result.ID] ?? {};
      mapping[result.ID][result.PhoneNumber] = result.PhoneID;
    }

    return mapping;
  }

  protected async getDefaultTypeID(): Promise<number> {
    const parameter = await this.serviceParameterRepository.findOne({
      where: {
        name: 'smsActivityTypeId',
        isDeleted: 0,
      },
    });

    if (parameter) {
      return Number(parameter.value);
    }

    return 10;
  }

  protected async getDefaultStatusID(): Promise<number> {
    const parameter = await this.serviceParameterRepository.findOne({
      where: {
        name: 'smsActivityStatusId',
        isDeleted: 0,
      },
    });

    if (parameter) {
      return Number(parameter.value);
    }

    return 31;
  }
}
