import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { CommentRepository } from '../../../repositories/activity/comment.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { SmsServiceRepository } from '../../../repositories/activity/sms-service.repository';
import { PhoneRepository } from '../../../repositories/data/phone.repository';
import { EventsGateway } from '../../events.gateway';
import { SmsActivityListenerService } from './sms-activity-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      CommentRepository,
      SmsServiceRepository,
      PhoneRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [SmsActivityListenerService, EventsGateway],
})
export class SmsActivityListenerModule {}
