import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel, ConsumeMessage } from 'amqplib';
import { BaseEvent } from 'src/events/types/base-event.interface';

export abstract class Listener<T extends BaseEvent> {
  abstract queueName: T['queueName'];
  protected channel: ChannelWrapper;
  protected connection: AmqpConnectionManager;

  abstract messageHandler(data: T['data']): Promise<boolean>;

  constructor(connection: AmqpConnectionManager) {
    this.connection = connection;
  }

  async listen() {
    this.channel = this.connection.createChannel({
      setup: (channel: ConfirmChannel) =>
        Promise.all([
          channel.assertQueue(this.queueName, {
            durable: true,
          }),
          channel.consume(this.queueName, this.onMessage.bind(this), {
            noAck: false,
          }),
        ]),
    });
  }

  async onMessage(message: ConsumeMessage | null): Promise<void> {
    try {
      if (!message) {
        return;
      }

      const data = this.parseMessage(message);
      const isCompleted = await this.messageHandler(data);

      if (isCompleted) {
        this.channel.ack(message);
      }
    } catch (error) {
      console.error(error);
    }
  }

  private parseMessage(message: ConsumeMessage): T {
    const data = message.content;

    return typeof data === 'string'
      ? JSON.parse(data)
      : JSON.parse(data.toString('utf8'));
  }
}
