/* eslint-disable unicorn/prevent-abbreviations */
import { EntityManager, EntitySchema } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { Temporary } from 'src/common/temporary-table/decorators/temporary.decorator';
import { Debtor } from '../../../../entities/data/debtor.entity';

const TemporaryDebtorEntity = new EntitySchema({
  name: 'TemporaryDebtor',
  columns: {},
});
@Temporary({
  tableName: 'TemporaryDebtor',
  declaration: `
  "ID" BIGINT PRIMARY KEY NOT NULL,
  "TypeID" SMALLINT
  `,
})
export class TemporaryDebtor extends TemporaryTable<Debtor> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected tableEntity = TemporaryDebtorEntity,
  ) {
    super();
  }

  protected decompose(data: Debtor[]): any[] {
    return data.map((v: any) => {
      return {
        ID: v['id'],
        TypeID: v['typeId'],
      };
    });
  }

  async updateOriginTable(): Promise<void> {
    return this.manager.query(`
    UPDATE "Data"."Debtor" as original
                        set
                            "TypeID" = temp."TypeID"
                        from "${this.tableName}" "temp" WHERE temp."ID" = original."ID";
    `);
  }
}
