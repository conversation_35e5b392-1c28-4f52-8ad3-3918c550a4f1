import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { EntityManager, EntitySchema, getMetadataArgsStorage } from 'typeorm';
import { Custom005CaseAdditionalInfo } from '../../../../entities/data/custom005-case-additional-info.entity';

function getAllColumns(model: any): string[] {
  const columns = getMetadataArgsStorage().columns.filter(
    (column) => column.target === model,
  );
  return columns.map((column) => column.propertyName);
}

const TemporaryCustom005CaseAdditionalInfoEntity = new EntitySchema({
  name: 'TemporaryCustom005CaseAdditionalInfo',
  columns: {},
});

const tableName = 'TemporaryCustom005CaseAdditionalInfo';

export class TemporaryCustom005CaseAdditionalInfo extends TemporaryTable<Custom005CaseAdditionalInfo> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected columnList: string[],
    protected tableEntity = TemporaryCustom005CaseAdditionalInfoEntity,
  ) {
    super();
  }

  async manualInitialize(): Promise<void> {
    const columns = getMetadataArgsStorage().columns.filter(
      (column) => column.target === Custom005CaseAdditionalInfo,
    );
    const columnsList = columns
      .map((column) => {
        if (this.columnList.includes(column.propertyName)) {
          return `"${column.propertyName}" ${column.options.type}`;
        }
        return false;
      })
      .filter((index) => index)
      .join(', ');
    const createTableSQL =
      `CREATE TEMPORARY TABLE "${tableName}"(` + columnsList + ');';
    await this.manager.query(`DROP TABLE if EXISTS "${tableName}";`);
    await this.manager.query(createTableSQL);
  }
  protected decompose(data: Custom005CaseAdditionalInfo[]): any[] {
    const fieldsToSync = getAllColumns(Custom005CaseAdditionalInfo);
    return data.map((v: any) => {
      const itemValues: any = {};
      for (const field of fieldsToSync) {
        if (v[field] !== undefined) {
          itemValues[field] = v[field];
        }
      }
      return itemValues;
    });
  }

  async updateOriginTable(): Promise<void> {
    const excludeSync = new Set(['CaseID', 'Inserted', 'Updated']);
    const fieldsToSync = getAllColumns(Custom005CaseAdditionalInfo).filter(
      (f) => !excludeSync.has(f),
    );
    const setValue = fieldsToSync
      .map((field) => {
        if (this.columnList.includes(field)) {
          return field;
        }
        return false;
      })
      .filter((index) => index)
      .map((field) => `"${field}" = temp."${field}"`)
      .join(', ');
    return this.manager.query(`
    UPDATE "Data"."Custom005CaseAdditionalInfo" as original
                        set
                            ${setValue}
                        from "${tableName}" "temp" 
                        WHERE temp."CaseID" = original."CaseID";
    `);
  }
}
