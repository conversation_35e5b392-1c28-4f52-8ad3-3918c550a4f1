import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportWithParameterListenerService } from '../../../common/imports/listeners/base-import-with-parameter-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportLegalDocumentDTO } from '../../../import-legal-document/dto/import-legal-document.dto';
import { DocumentRepository } from '../../../repositories/data/document.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalDocumentEvent } from '../../types/legal-document-event';
import { LegalDocumentPreImportDataDTO } from './dto/legal-document-pre-import-data-d-t.o';

@Injectable()
export class LegalDocumentListenerService extends BaseImportWithParameterListenerService<
  LegalDocumentEvent,
  LegalDocumentPreImportDataDTO
> {
  queueName: Queues.LegalDocument = Queues.LegalDocument;
  catalog: CatalogNameEnum = CatalogNameEnum.LegalDocument;
  catalogId: CatalogEnum = CatalogEnum.LegalDocument;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly documentRepository: DocumentRepository,
    protected readonly parameterRepository: ParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
      parameterRepository,
    );
    this.listen().catch((error) => console.log(error));
  }

  async preImport(
    importData: ImportLegalDocumentDTO['importData'],
    userId: number,
    uploadHistoryId: string,
  ): Promise<LegalDocumentPreImportDataDTO[]> {
    return this.formatParameters(importData, userId, uploadHistoryId);
  }

  async messageHandler(
    importData: LegalDocumentPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
    additionalParams: { typeId: number },
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const documents: any[] = [];
      const documentParameters: any[] = [];
      const documentParameterToCaseID: any = {};
      const activateAppellateCourtMapping: any[] = [];

      const chunks: LegalDocumentPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const slugToId = await this.getParameterBySlug();
      const parametersHasActivateCourtMapping =
        await this.parameterRepository.getParametersHasActivateCourtMapping();

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      const documentPool = await this.documentRepository.getDocumentIDPool(
        importData.length,
      );

      for (const chunk of chunks) {
        for (const [index, row] of chunk.entries()) {
          const document = {
            id: String(documentPool[index]),
            levelValue: row.LegalCaseID,
          };
          documents.push(document);

          const parameters = Object.keys(row).filter(
            (c) => c !== 'LegalCaseID',
          );

          for (const parameter of parameters) {
            const documentParameter = {
              documentId: document.id,
              parameterId: slugToId[parameter],
              value: row[parameter],
              insertedUserId: userId,
              updatedUserId: userId,
            };
            documentParameters.push(documentParameter);
            documentParameterToCaseID[document.id] = row.LegalCaseID;

            if (
              this.hasAppellateCourtMapping(
                row[parameter],
                slugToId[parameter],
                parametersHasActivateCourtMapping,
              )
            ) {
              activateAppellateCourtMapping.push(row.LegalCaseID);
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      const casesToActivateLegalRules = importData.map((r) => r.LegalCaseID);
      await this.saveResult(Number(uploadHistoryId), page, {
        documents,
        documentParameters,
        documentParameterToCaseID,
        activateAppellateCourtMapping,
        casesToActivateLegalRules,
        metaData: {
          userId,
          typeId: Number(additionalParams.typeId),
          levelId: 4,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }
}
