import { Expose } from 'class-transformer';

export class LetterPrintHouseDataPreImportDataDTO {
  @Expose()
  AddressCheckDigit: string;

  @Expose()
  CaseID: number;

  @Expose()
  DebtorFullName: string | null;

  @Expose()
  PrintDate: string | null;

  @Expose()
  ActualOrderName: string | null;

  @Expose()
  LetterSenderID: number | null | string;

  @Expose()
  AWBNumber: string | null;

  @Expose()
  PostDate: string | null;
}
