/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-assertion */

import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { plainToClass } from 'class-transformer';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { getManager, In } from 'typeorm';
import { Custom005LetterPrintHouseInfo } from '../../../entities/activity/custom005-letter-print-house-info.entity';
import { Custom005LetterSenderEnum } from '../../../entities/enum/custom005-letter-sender.enum';
import { ImportLetterPrintHouseDataDTO } from '../../../import-letter-print-house-data/dto/import-letter-print-house-data.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { Custom005LetterPrintHouseInfoRepository } from '../../../repositories/activity/custom005-letter-print-house-info.repository';
import { EventsGateway } from '../../events.gateway';
import { LetterPrintHouseDataEvent } from '../../types/letter-print-house-data-event';
import { LetterPrintHouseDataPreImportDataDTO } from './dto/letter-print-house-data-pre-import-data.dto';
import { LetterPrintHouseDataListenerDataAccess } from './letter-print-house-data-listener-data-access';
import { ImportListener } from '../import-listener';
// import { UpdateImportProgressPublisher } from 'src/events/publishers/update-import-progress-publisher';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { format, parse } from 'date-fns';

const fileDateFormat = 'dd.MM.yyyy';
const systemDateFormat = 'yyyy-MM-dd';

@Injectable()
export class LetterPrintHouseDataListenerService extends ImportListener<
  LetterPrintHouseDataEvent,
  LetterPrintHouseDataPreImportDataDTO
> {
  queueName: Queues.LetterPrintHouseData = Queues.LetterPrintHouseData;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly letterPrintHouseDataListenerDataAccess: LetterPrintHouseDataListenerDataAccess,
    private readonly serviceParameterRepository: ServiceParameterRepository,
    private readonly custom005LetterPrintHouseInfoRepository: Custom005LetterPrintHouseInfoRepository,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: ImportLetterPrintHouseDataDTO['importData'],
  ): Promise<LetterPrintHouseDataPreImportDataDTO[]> {
    const letterPrintHouseDataPreImportDataDTOS: LetterPrintHouseDataPreImportDataDTO[] =
      [];

    for (const record of importData) {
      const preImportRecord = plainToClass(
        LetterPrintHouseDataPreImportDataDTO,
        record,
        {
          excludeExtraneousValues: true,
        },
      );

      preImportRecord.AddressCheckDigit = record.LETTER_QUEUE_ID;
      preImportRecord.CaseID = Number(record.DENUMB);
      preImportRecord.DebtorFullName = record.DENAME;
      preImportRecord.PrintDate = record.data_receptie;
      preImportRecord.ActualOrderName = record.comanda;
      preImportRecord.LetterSenderID = record.curier;

      if (
        record.curier &&
        ['true', 'false'].includes(record.curier.toLowerCase())
      ) {
        preImportRecord.LetterSenderID =
          record.curier.toLowerCase() === 'true'
            ? Custom005LetterSenderEnum.Courier
            : Custom005LetterSenderEnum.CNPR;
      }

      if (record.curier === 'null') {
        record.curier = 'null';
      }

      preImportRecord.AWBNumber = record.awb_lung;

      try {
        preImportRecord.PostDate = format(
          parse(record['Data Posta'], fileDateFormat, new Date()),
          systemDateFormat,
        );
      } catch {
        preImportRecord.PostDate = '';
      }

      if (preImportRecord.PrintDate && preImportRecord.PrintDate !== 'null') {
        try {
          preImportRecord.PrintDate = format(
            parse(preImportRecord.PrintDate, fileDateFormat, new Date()),
            systemDateFormat,
          );
        } catch {
          preImportRecord.PrintDate = '';
        }
      }

      letterPrintHouseDataPreImportDataDTOS.push(preImportRecord);
    }
    return letterPrintHouseDataPreImportDataDTOS;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => Promise<void> {
    // const updateImportProgressPublisher = new UpdateImportProgressPublisher(
    //   this.connection,
    // );
    return async (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
      // await updateImportProgressPublisher.publish({
      //   event: 'UpdateImportProgress',
      //   payload,
      // });
    };
  }

  async messageHandler(
    importData: LetterPrintHouseDataPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    try {
      const updateImportProgress = this.createUpdateImportProgressPublisher(
        uploadHistoryId,
        uploadHistory!.importListId,
        userId,
      );

      const createLetterPrintHouseInfo: Custom005LetterPrintHouseInfo[] = [];
      const updateLetterPrintHouseInfo: number[] = [];

      const caseIds = this.getUniqueValuesInArray(importData, 'CaseID');

      const existLetterRecords = await this.getCaseLetters(caseIds);
      const existCustom005LetterRecords = await this.getCustom005Letters(
        Object.values(existLetterRecords),
      );

      let index = 0;
      let previousPercent = 0;

      for (const data of importData) {
        const historyId = String(existLetterRecords[data.AddressCheckDigit]);
        const existsRecord = existCustom005LetterRecords[historyId];

        let debtorFullName = existsRecord ? existsRecord.debtorFullName : null;
        if (data.DebtorFullName) {
          debtorFullName =
            data.DebtorFullName === 'null' ? null : data.DebtorFullName;
        }

        let printDate = existsRecord ? existsRecord.printDate : null;
        if (data.PrintDate) {
          printDate = data.PrintDate === 'null' ? null : data.PrintDate;
        }

        let actualOrderName = existsRecord
          ? existsRecord.actualOrderName
          : null;
        if (data.ActualOrderName) {
          actualOrderName =
            data.ActualOrderName === 'null' ? null : data.ActualOrderName;
        }

        let letterSenderId = existsRecord ? existsRecord.letterSenderId : null;
        if (data.LetterSenderID) {
          letterSenderId =
            data.LetterSenderID === 'null' ? null : data.LetterSenderID;
        }

        let awbNumber = existsRecord ? existsRecord.awbNumber : null;
        if (data.AWBNumber) {
          awbNumber = data.AWBNumber === 'null' ? null : data.AWBNumber;
        }

        let postDate = existsRecord ? existsRecord.postDate : null;
        if (data.PostDate) {
          postDate = data.PostDate === 'null' ? null : data.PostDate;
        }

        const newModel = this.custom005LetterPrintHouseInfoRepository.create({
          historyId: historyId,
          caseId: data.CaseID,
          addressCheckDigit: data.AddressCheckDigit,
          debtorFullName: debtorFullName,
          printDate: printDate,
          actualOrderName: actualOrderName,
          letterSenderId: letterSenderId,
          awbNumber: awbNumber,
          postDate: postDate,
          insertedUserId: userId,
        });

        createLetterPrintHouseInfo.push(newModel);

        if (existsRecord) {
          updateLetterPrintHouseInfo.push(Number(existsRecord.id));
        }

        const progress = Math.round((index / importData.length) * 100);
        if (previousPercent !== progress) {
          await updateImportProgress(progress);
          previousPercent = progress;
        }

        index++;
      }

      const chunk = this.config.get<number>('chunk.default')!;

      await getManager().transaction(async (manager) => {
        if (updateLetterPrintHouseInfo.length > 0) {
          await this.custom005LetterPrintHouseInfoRepository
            .createQueryBuilder('cp')
            .update()
            .set({ isDeleted: 1 })
            .where(`"ID" in (${updateLetterPrintHouseInfo.join(',')})`)
            .execute();
        }

        await Promise.all([
          await this.custom005LetterPrintHouseInfoRepository.transactionSave(
            manager,
            createLetterPrintHouseInfo,
            { chunk },
          ),
        ]);

        await updateImportProgress(100);

        await this.uploadHistoryRepository.update(
          { id: uploadHistory!.id },
          { statusId: UploadStatus.Finished },
        );
      });
    } catch (error) {
      console.error(error);
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Failed },
      );
    }

    return true;
  }

  private getUniqueValuesInArray<T, K extends keyof T>(
    array: T[],
    key: K,
  ): T[K][] {
    const values = new Set(
      array.filter((item) => item[key]).map((item) => item[key]),
    );
    return [...values];
  }

  private async getCaseLetters(caseIds: number[]): Promise<{
    [key: string]: number;
  }> {
    const map: { [key: string]: number } = {};

    const existItems =
      await this.letterPrintHouseDataListenerDataAccess.getCaseLetter(caseIds);

    for (const model of existItems) {
      const key = String(model.caseId + '-' + String(model.id).slice(-4));
      map[key] = Number(model.id);
    }

    return map;
  }

  private async getCustom005Letters(historyIds: number[]): Promise<{
    [key: string]: Custom005LetterPrintHouseInfo;
  }> {
    const map: { [key: string]: Custom005LetterPrintHouseInfo } = {};

    const existItems =
      await this.letterPrintHouseDataListenerDataAccess.getCustom005LetterPrintHouseInfo(
        historyIds,
      );

    for (const model of existItems) {
      map[model.historyId] = model;
    }

    return map;
  }
}
