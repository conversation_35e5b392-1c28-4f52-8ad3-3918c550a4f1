/* eslint-disable unicorn/prevent-abbreviations */
import { Injectable } from '@nestjs/common';

import { In } from 'typeorm';
import { splitOnChunks } from '../../../common/helpers/split-on-chunks';
import { Custom005LetterPrintHouseInfo } from '../../../entities/activity/custom005-letter-print-house-info.entity';
import { History } from '../../../entities/activity/history.entity';
import { Custom005LetterSender } from '../../../entities/dictionary/custom005-letter-sender.entity';
import { Custom005LetterPrintHouseInfoRepository } from '../../../repositories/activity/custom005-letter-print-house-info.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom005LetterSenderRepository } from '../../../repositories/dictionary/custom005-letter-sender.repository';

@Injectable()
export class LetterPrintHouseDataListenerDataAccess {
  constructor(
    private custom005LetterSenderRepository: Custom005LetterSenderRepository,
    private custom005LetterPrintHouseInfoRepository: Custom005LetterPrintHouseInfoRepository,
    private historyRepository: HistoryRepository,
  ) {}

  public async getCustom005LetterSenders(): Promise<Custom005LetterSender[]> {
    return this.custom005LetterSenderRepository.find({
      where: { isDeleted: 0 },
    });
  }

  public async getCaseLetter(caseIds: number[]): Promise<History[]> {
    const chunks = splitOnChunks(caseIds, 10_000);
    let parts: History[] = [];

    for (const cases of chunks) {
      const part = await this.historyRepository.find({
        where: { caseId: In(cases), typeId: 13, isDeleted: 0 },
        order: { creationDate: 'DESC' },
      });
      parts = [...parts, ...part];
    }

    return parts;
  }

  public async getCustom005LetterPrintHouseInfo(
    historyIds: number[],
  ): Promise<Custom005LetterPrintHouseInfo[]> {
    const chunks = splitOnChunks(historyIds, 10_000);
    let parts: Custom005LetterPrintHouseInfo[] = [];

    for (const ids of chunks) {
      const part = await this.custom005LetterPrintHouseInfoRepository.find({
        where: { historyId: In(ids), isDeleted: 0 },
      });
      parts = [...parts, ...part];
    }

    return parts;
  }
}
