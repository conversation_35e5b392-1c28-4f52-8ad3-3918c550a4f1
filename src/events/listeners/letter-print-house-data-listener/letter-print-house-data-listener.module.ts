/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { Custom005LetterPrintHouseInfoRepository } from '../../../repositories/activity/custom005-letter-print-house-info.repository';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom005LetterSenderRepository } from '../../../repositories/dictionary/custom005-letter-sender.repository';
import { EventsGateway } from '../../events.gateway';
import { LetterPrintHouseDataListenerDataAccess } from './letter-print-house-data-listener-data-access';
import { LetterPrintHouseDataListenerService } from './letter-print-house-data-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005LetterPrintHouseInfoRepository,
      Custom005LetterSenderRepository,
      HistoryRepository,
    ]),
  ],
  providers: [
    LetterPrintHouseDataListenerService,
    LetterPrintHouseDataListenerDataAccess,
    EventsGateway,
  ],
})
export class LetterPrintHouseDataListenerModule {}
