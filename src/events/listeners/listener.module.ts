import { Module } from '@nestjs/common';
import { ActivityParameterListenerModule } from './activity-parameter-listener/activity-parameter-listener.module';
import { AttachmentListenerModule } from './attachment-listener/attachment-listener.module';
import { BasisComplaintListenerModule } from './basis-complaint-listener/basis-complaint-listener.module';
import { BasisCpiioListenerModule } from './basis-cpiio-listener/basis-cpiio-listener.module';
import { BasisEmployerListenerModule } from './basis-employer-listener/basis-employer-listener.module';
import { BasisFinaListenerModule } from './basis-fina-listener/basis-fina-listener.module';
import { CaseAttachmentListenerModule } from './case-attachment-listener/case-attachment-listener.module';
import { CaseCloningListenerModule } from './case-cloning-listener/case-cloning-listener.module';
import { CaseDiscountListenerModule } from './case-discount-listener/case-discount-listener.module';
import { CaseParameterListenerModule } from './case-parameter-listener/case-parameter-listener.module';
import { CaseStateListenerModule } from './case-state-listener/case-state-listener.module';
import { ContactPersonListenerModule } from './contact-person-listener/contact-person-listener.module';
import { ContractDataListenerModule } from './contract-data-listener/contract-data-listener.module';
import { CourtParameterListenerModule } from './court-parameter-listener/court-parameter-listener.module';
import { DiscountListenerModule } from './discount-listener/discount-listener.module';
import { ExtraInfoListenerModule } from './extra-info-listener/extra-info-listener.module';
import { InteractionDeleteListenerModule } from './interaction-delete-listener/interaction-delete-listener.module';
import { LegalCaseDeleteListenerModule } from './legal-case-delete-listener/legal-case-delete-listener.module';
import { RecalculateFinancialDataListenerModule } from './recalculate-financial-data-listener/recalculate-financial-data-listener.module';
import { SmsActivityListenerModule } from './sms-activity-listener/sms-activity-listener.module';
import { TagListenerModule } from './tag-listener/tag-listener.module';
import { TransferToCollectionAgencyListenerModule } from './transfer-to-collection-agency-listener/transfer-to-collection-agency-listener.module';
import { InteractionListenerModule } from './interaction-listener/interaction-listener.module';
import { LegalAppellateCourtListenerModule } from './legal-appellate-court-listener/legal-appellate-court-listener.module';
import { LegalCaseListenerModule } from './legal-case-listener/legal-case-listener.module';
import { LegalCourtListenerModule } from './legal-court-listener/legal-court-listener.module';
import { LegalDocumentListenerModule } from './legal-document-listener/legal-document-listener.module';
import { LegalInvoiceListenerModule } from './legal-invoice-listener/legal-invoice-listener.module';
import { LetterPrintHouseDataListenerModule } from './letter-print-house-data-listener/letter-print-house-data-listener.module';

import { MaskedPhoneListenerModule } from './masked-phone-listener/masked-phone-listener.module';
import { UpdateLegalDocumentListenerModule } from './update-legal-document-listener/update-legal-document-listener.module';
import { PhoneListenerModule } from './phone-listener/phone-listener.module';

@Module({
  imports: [
    MaskedPhoneListenerModule,
    ContactPersonListenerModule,
    LetterPrintHouseDataListenerModule,
    CaseAttachmentListenerModule,
    InteractionListenerModule,
    CaseCloningListenerModule,
    ExtraInfoListenerModule,
    ContractDataListenerModule,
    LegalCaseListenerModule,
    LegalDocumentListenerModule,
    LegalInvoiceListenerModule,
    CourtParameterListenerModule,
    UpdateLegalDocumentListenerModule,
    LegalCourtListenerModule,
    LegalAppellateCourtListenerModule,
    BasisFinaListenerModule,
    BasisCpiioListenerModule,
    BasisEmployerListenerModule,
    BasisComplaintListenerModule,
    TransferToCollectionAgencyListenerModule,
    ActivityParameterListenerModule,
    DiscountListenerModule,
    TagListenerModule,
    CaseParameterListenerModule,
    RecalculateFinancialDataListenerModule,
    AttachmentListenerModule,
    PhoneListenerModule,
    LegalCaseDeleteListenerModule,
    InteractionDeleteListenerModule,
    CaseStateListenerModule,
    SmsActivityListenerModule,
    CaseDiscountListenerModule,
  ],
})
export class ListenerModule {}
