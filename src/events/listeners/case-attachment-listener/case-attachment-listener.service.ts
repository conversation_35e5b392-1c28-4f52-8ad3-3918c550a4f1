/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-assertion */

import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';

// eslint-disable-next-line @typescript-eslint/no-var-requires
import FormData from 'form-data';
import * as fs from 'fs';
// eslint-disable-next-line unicorn/import-style
import * as path from 'path';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
// import { UpdateImportProgressPublisher } from 'src/events/publishers/update-import-progress-publisher';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { appendToFile } from '../../../common/helpers/append-to-file';
import { checkIfExists } from '../../../common/helpers/check-if-exists';
import { mkDir } from '../../../common/helpers/mk-dir';
import { rmDir } from '../../../common/helpers/rm-dir';
import { CaseAttachmentDTO } from '../../../import-case-attachment/dto/case-attachment.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { EventsGateway } from '../../events.gateway';
import { CaseAttachmentEvent } from '../../types/case-attachment-event';
import { ImportListener } from '../import-listener';

@Injectable()
export class CaseAttachmentListenerService extends ImportListener<
  CaseAttachmentEvent,
  CaseAttachmentDTO
> {
  queueName: Queues.CaseAttachment = Queues.CaseAttachment;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly httpService: HttpService,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: CaseAttachmentDTO[],
  ): Promise<CaseAttachmentDTO[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number, statusId: number) => void {
    return (Progress: number, statusId: number) => {
      const channel = `import:${CatalogID}`;
      const payload: any = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        Progress,
      };

      if (statusId) {
        payload['StatusID'] = statusId;
      }

      this.messageGateway.server.in(channel).emit('progress', payload);
    };
  }

  async messageHandler(
    importData: CaseAttachmentDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    if (!uploadHistory || (uploadHistory && !uploadHistory.importListId)) {
      console.log('undefined history id', uploadHistoryId);
      return false;
    }

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    let success: { Filename: string; CaseID: number }[] = [];
    const failed: { Filename: string; CaseID: number }[] = [];
    const errors: { [key: string]: string } = {};

    const caseServiceURL = this.config.get<string>('caseService.url');
    for (const data of importData) {
      let currentFile = 1;
      let previousPercent = 0;
      let previousPack = [];

      const chunkSize = 50;
      const chunks = this.sliceIntoChunks(data.attachments, chunkSize);
      for (const attachments of chunks) {
        let insertedAttachments = 0;
        const requestAttachments = [];
        const formData = new FormData();
        formData.append('userID', String(userId));
        for (const attachment of attachments) {
          try {
            const filepath = path.join(
              process.cwd(),
              data.directory + '/' + attachment.Filename,
            );

            if (!(await checkIfExists(filepath))) {
              failed.push(attachment);
              errors[attachment.Filename + '_' + attachment.CaseID] =
                'File not found';
              continue;
            }

            const cases = attachment.CaseID;
            for (const caseId of cases) {
              const params = {
                CaseID: caseId,
                FileName: attachment.CustomName ?? attachment.Filename,
                IsHidden: attachment.IsHidden,
                DateOfDocument: attachment.DateOfDocument,
                DueDate: attachment.DueDate,
                Note: attachment.Note,
                SourceID: attachment.SourceID,
                TypeID: attachment.TypeID,
                ReplyTypeID: attachment.ReplyTypeID,
              };

              formData.append('files[]', fs.createReadStream(filepath));
              formData.append('params[]', JSON.stringify(params));
              insertedAttachments++;
            }
            requestAttachments.push(attachment);
            previousPack.push(attachment);
          } catch (error: any) {
            console.log(error.message);
            failed.push(attachment);
            if (error.message) {
              errors[attachment.Filename + '_' + attachment.CaseID] =
                error.message;
            }
            if (error?.response?.data?.message) {
              errors[attachment.Filename + '_' + attachment.CaseID] =
                error?.response?.data?.message;
            }
          }
          const progress = Math.round(
            (currentFile / data.attachments.length) * 100,
          );
          if (previousPercent !== progress) {
            updateImportProgress(progress, UploadStatus.InProgress);
            previousPercent = progress;
          }
          currentFile++;
        }

        if (insertedAttachments > 0) {
          try {
            const response = await this.httpService
              .post(caseServiceURL + '/mass-case-attachment', formData, {
                headers: { ...formData.getHeaders() },
                maxContentLength: 2048 * 1024 * 1024,
                maxBodyLength: 2048 * 1024 * 1024,
              })
              .toPromise();
          } catch (error: any) {
            if (error.response?.status === 422) {
              for (const attachment of requestAttachments) {
                failed.push(attachment);
                errors[attachment.Filename + '_' + attachment.CaseID] =
                  JSON.stringify(error.response.data);
              }
            } else {
              console.log(error);
            }
          }
        }

        success = [
          ...success,
          ...(Array.isArray(previousPack) ? previousPack : [previousPack]),
        ];

        previousPack = [];
      }
    }

    await this.saveImportDetails(
      uploadHistory!.id,
      success,
      failed,
      errors,
      userId,
    );
    updateImportProgress(
      100,
      failed.length === 0 ? UploadStatus.Finished : UploadStatus.Failed,
    );

    await (failed.length === 0
      ? this.uploadHistoryRepository.update(
          { id: uploadHistory!.id },
          { statusId: UploadStatus.Finished },
        )
      : this.uploadHistoryRepository.update(
          { id: uploadHistory!.id },
          { statusId: UploadStatus.Failed },
        ));

    for (const data of importData) {
      if (await checkIfExists(path.join(process.cwd(), data.directory))) {
        await rmDir(path.join(process.cwd(), data.directory), {
          recursive: true,
        });
      }
    }

    return true;
  }

  async delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async saveImportDetails(
    historyId: string,
    success: { Filename: string; CaseID: number }[],
    failed: { Filename: string; CaseID: number }[],
    errors: { [key: string]: string },
    userId: number,
  ) {
    const relativePath = `output/${userId}`;
    const filePath = path.join(relativePath, `${historyId}.csv`);
    const resultPath = path.join(process.cwd(), relativePath);

    if (!(await checkIfExists(resultPath))) {
      await mkDir(resultPath, {
        recursive: true,
      });
    }

    const data: any[] = [];
    const failedNames = new Set(failed.map((item) => item.Filename));
    const filteredSuccess = success.filter((attachment) => {
      return !failedNames.has(attachment.Filename);
    });

    for (const row of failed) {
      data.push({
        Filename: row.Filename,
        CaseID: row.CaseID,
        Status: 'error',
        Message: errors[row.Filename + '_' + row.CaseID],
      });
    }

    for (const row of filteredSuccess) {
      data.push({
        Filename: row.Filename,
        CaseID: row.CaseID,
        Status: 'Ok',
        Message: null,
      });
    }
    const csvBody = this.generateCsv(data);
    await appendToFile(filePath, csvBody, 'utf8');

    await this.uploadHistoryRepository.update(
      { id: historyId },
      { resultFilePath: filePath },
    );
  }

  sliceIntoChunks(items: any[], chunkSize: number) {
    const res = [];
    for (let i = 0; i < items.length; i += chunkSize) {
      const chunk = items.slice(i, i + chunkSize);
      res.push(chunk);
    }
    return res;
  }
}
