/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ApiClient } from '../../../common/api.client';
import { EventsGateway } from '../../events.gateway';
import { CaseAttachmentListenerService } from './case-attachment-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      ServiceParameterRepository,
      UploadHistoryRepository,
    ]),
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [CaseAttachmentListenerService, EventsGateway, ApiClient],
})
export class CaseAttachmentListenerModule {}
