import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { RegionRepository } from '../../../repositories/dictionary/region.repository';
import { AppellateCourtRepository } from '../../../repositories/legal-ua/appellate-court.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalAppellateCourtListenerService } from './legal-appellate-court-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      AppellateCourtRepository,
      RegionRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [LegalAppellateCourtListenerService, EventsGateway],
})
export class LegalAppellateCourtListenerModule {}
