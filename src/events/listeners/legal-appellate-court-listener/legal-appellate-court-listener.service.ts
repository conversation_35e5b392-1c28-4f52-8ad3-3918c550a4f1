import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { AppellateCourt } from '../../../entities/legal-ua/appellate-court.entity';
import { ImportLegalAppellateCourtDTO } from '../../../import-legal-appellate-court/dto/import-legal-appellate-court.dto';
import { RegionRepository } from '../../../repositories/dictionary/region.repository';
import { AppellateCourtRepository } from '../../../repositories/legal-ua/appellate-court.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalAppellateCourtEvent } from '../../types/legal-appellate-court-event';
import { LegalCourtPreImportDataDTO } from './dto/legal-court-pre-import-data.dto';

@Injectable()
export class LegalAppellateCourtListenerService extends BaseImportListenerService<
  LegalAppellateCourtEvent,
  LegalCourtPreImportDataDTO
> {
  queueName: Queues.LegalAppellateCourt = Queues.LegalAppellateCourt;
  catalog: CatalogNameEnum = CatalogNameEnum.LegalAppellateCourt;
  catalogId: CatalogEnum = CatalogEnum.LegalAppellateCourt;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly courtRepository: AppellateCourtRepository,
    protected readonly regionRepository: RegionRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportLegalAppellateCourtDTO['importData'],
  ): Promise<LegalCourtPreImportDataDTO[]> {
    const legalCourtPreImportDataDTO: LegalCourtPreImportDataDTO[] = [];
    const regions = await this.regionRepository.getList();

    const regionMapping: { [key: string]: number } = {};

    for (const region of regions) {
      regionMapping[region.name] = region.id;
    }

    for (const [index, data] of importData.entries()) {
      if (data.ChangeAppellateCourtID === '') {
        importData[index].ChangeAppellateCourtID = null;
      }

      if (data.Region) {
        data.RegionID = regionMapping[data.Region];
      }

      legalCourtPreImportDataDTO.push(data);
    }

    return legalCourtPreImportDataDTO;
  }

  async messageHandler(
    importData: LegalCourtPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertCourt: AppellateCourt[] = [];
    const updateCourt: AppellateCourt[] = [];

    if (importData.length > 0) {
      const chunks: LegalCourtPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const IDs = chunk.map((index) => index.ID);

        const existsRecords = await this.courtRepository.find({
          where: {
            ID: In(IDs),
            IsDeleted: 0,
          },
        });

        const existsRecordsMapping: { [key: number]: AppellateCourt } = {};
        for (const existsRecord of existsRecords) {
          existsRecordsMapping[existsRecord.ID] = existsRecord;
        }

        for (const row of chunk) {
          if (!row.ID) {
            const model = {
              Name: row.Name,
              InsertedUserID: userId,
              UpdatedUserID: userId,
              CityCode: row.CityCode,
              RegionID: row.RegionID,
              City: row.City,
              District: row.District,
              Street: row.Street,
              House: row.House,
              Building: row.Building,
              Apartment: row.Apartment,
              ChangeAppellateCourtID:
                row.ChangeAppellateCourtID !== 'null'
                  ? row.ChangeAppellateCourtID
                  : null,
              Receiver: row.Receiver,
              InvoiceNumber: row.InvoiceNumber,
              ReceiverCode: row.ReceiverCode,
              PaymentDestination: row.PaymentDestination,
              PhoneNumber: row.PhoneNumber,
              Email: row.Email,
            } as AppellateCourt;
            insertCourt.push(model);
          } else {
            updateCourt.push(
              this.updateItem(existsRecordsMapping[row.ID], row, userId),
            );
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertCourt,
        updateCourt,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  updateItem(
    values: AppellateCourt,
    newValues: LegalCourtPreImportDataDTO,
    userId: number,
  ): AppellateCourt {
    for (const field of Object.keys(values)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const newValue = newValues[field];

      if (newValue !== null) {
        if (newValue === 'null') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = null;
        } else if (newValue !== '') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = newValue;
        }
      }
    }
    values['UpdatedUserID'] = userId;
    return values;
  }
}
