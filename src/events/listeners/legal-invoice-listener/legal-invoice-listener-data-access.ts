import { Injectable } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';

import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { LegalInvoicePreImportDataDto } from './dto/legal-invoice-pre-import-data.dto';

@Injectable()
export class LegalInvoiceListenerDataAccess {
  constructor(
    private legalInvoiceRepository: LegalInvoiceRepository,
    private serviceParameterRepository: ServiceParameterRepository,
  ) {}

  public async runCreateLegalInvoice(
    cases: LegalInvoicePreImportDataDto[],
    userId: number,
    connection: AmqpConnectionManager,
  ): Promise<any> {
    return await this.legalInvoiceRepository.createLegalInvoices(
      cases,
      userId,
      connection,
      this.serviceParameterRepository,
    );
  }
}
