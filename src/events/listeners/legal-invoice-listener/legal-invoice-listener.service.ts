import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { ImportLegalInvoiceDto } from '../../../import-legal-invoice/dto/import-legal-invoice.dto';
import { UploadStatus } from '../../../import/upload-status.enum';
import { EventsGateway } from '../../events.gateway';
import { LegalInvoiceEvent } from '../../types/legal-invoice-event';
import { LegalInvoiceListenerDataAccess } from './legal-invoice-listener-data-access';
import { ImportListener } from '../import-listener';
import { LegalInvoicePreImportDataDto } from './dto/legal-invoice-pre-import-data.dto';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';

@Injectable()
export class LegalInvoiceListenerService extends ImportListener<
  LegalInvoiceEvent,
  LegalInvoicePreImportDataDto
> {
  queueName: Queues.LegalInvoice = Queues.LegalInvoice;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly legalInvoiceListenerDataAccess: LegalInvoiceListenerDataAccess,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
  ) {
    super(connection);
    this.listen();
  }

  async preImport(
    importData: ImportLegalInvoiceDto['importData'],
  ): Promise<LegalInvoicePreImportDataDto[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
  ): (progress: number) => void {
    return (Progress: number) => {
      const channel = `import:${CatalogID}:${UserID}`;
      const payload = {
        CatalogID,
        UploadHistoryID: Number(UploadHistoryID),
        StatusID: UploadStatus.Finished,
        Progress,
      };
      this.messageGateway.server.in(channel).emit('progress', payload);
    };
  }

  async messageHandler(
    importData: LegalInvoicePreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.getFromMaster(
      uploadHistoryId,
    );

    const updateImportProgress = this.createUpdateImportProgressPublisher(
      uploadHistoryId,
      uploadHistory!.importListId,
      userId,
    );

    const result =
      await this.legalInvoiceListenerDataAccess.runCreateLegalInvoice(
        importData,
        userId,
        this.connection,
      );

    updateImportProgress(100);

    if (result.success) {
      const path = await this.saveReport(
        result.report,
        userId,
        uploadHistory!.id,
      );
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Finished, resultFilePath: path },
      );
    } else {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory!.id },
        { statusId: UploadStatus.Failed },
      );
    }

    return true;
  }
}
