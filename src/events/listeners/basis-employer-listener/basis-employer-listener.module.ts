import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../../../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisEmployerListenerService } from './basis-employer-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      Custom004EEnforcementRequestDeliveryStatusRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [BasisEmployerListenerService, EventsGateway],
})
export class BasisEmployerListenerModule {}
