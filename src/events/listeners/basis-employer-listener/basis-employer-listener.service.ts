import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { Custom004EEnforcementRequestToEmployer } from '../../../entities/legal/custom004-enforcement-request-to-employer.entity';
import { ImportEmployerDTO } from '../../../import-basis-employer/dto/import-employer.dto';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../../../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisEmployerEvent } from '../../types/basis-employer-event';
import { EmployerPreImportDataDTO } from './dto/employer-pre-import-data.dto';

const ImportFormat = 'dd.MM.yyyy';
const SystemFormat = 'yyyy-MM-dd';

@Injectable()
export class BasisEmployerListenerService extends BaseImportListenerService<
  BasisEmployerEvent,
  EmployerPreImportDataDTO
> {
  queueName: Queues.BasisEmployer = Queues.BasisEmployer;
  catalog: CatalogNameEnum = CatalogNameEnum.BasisEmployer;
  catalogId: CatalogEnum = CatalogEnum.BasisEmployer;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly custom004EEnforcementRequestDeliveryStatusRepository: Custom004EEnforcementRequestDeliveryStatusRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportEmployerDTO['importData'],
  ): Promise<EmployerPreImportDataDTO[]> {
    const employerPreImportDataDTO: EmployerPreImportDataDTO[] = [];
    const deliveryStatuses =
      await this.custom004EEnforcementRequestDeliveryStatusRepository.find({
        isDeleted: 0,
      });

    const deliveryStatusMapping: { [key: string]: number } = {};
    const yesNoMapping: { [key: string]: boolean } = { yes: true, no: false };
    for (const deliveryStatus of deliveryStatuses) {
      deliveryStatusMapping[deliveryStatus.name] = deliveryStatus.id;
    }

    for (const [index, data] of importData.entries()) {
      if (data.DeliveryStatus) {
        data.DeliveryStatusID = deliveryStatusMapping[data.DeliveryStatus];
      }

      if (data.DeliveryDate !== '' && data.DeliveryDate !== 'null') {
        const parsedDate = parse(
          String(data.DeliveryDate),
          ImportFormat,
          new Date(),
        );
        data.DeliveryDate = format(parsedDate, SystemFormat);
      }

      if (
        data.RequestToOriginalDocumentation !== '' &&
        data.RequestToOriginalDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.RequestToOriginalDocumentation),
          ImportFormat,
          new Date(),
        );
        data.RequestToOriginalDocumentation = format(parsedDate, SystemFormat);
      }

      if (
        data.WeDontHaveOriginalDocumentation !== '' &&
        data.WeDontHaveOriginalDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.WeDontHaveOriginalDocumentation),
          ImportFormat,
          new Date(),
        );
        data.WeDontHaveOriginalDocumentation = format(parsedDate, SystemFormat);
      }

      if (
        data.WaitingOriginalDocumentation !== '' &&
        data.WaitingOriginalDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.WaitingOriginalDocumentation),
          ImportFormat,
          new Date(),
        );
        data.WaitingOriginalDocumentation = format(parsedDate, SystemFormat);
      }

      if (
        data.SentOriginalDocumentation !== '' &&
        data.SentOriginalDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.SentOriginalDocumentation),
          ImportFormat,
          new Date(),
        );
        data.SentOriginalDocumentation = format(parsedDate, SystemFormat);
      }

      if (
        data.ReturnedOriginalDocumentation !== '' &&
        data.ReturnedOriginalDocumentation !== 'null'
      ) {
        const parsedDate = parse(
          String(data.ReturnedOriginalDocumentation),
          ImportFormat,
          new Date(),
        );
        data.ReturnedOriginalDocumentation = format(parsedDate, SystemFormat);
      }

      if (data.IsProcessed !== '') {
        data.IsProcessed = yesNoMapping[String(data.IsProcessed).toLowerCase()];
      }

      employerPreImportDataDTO.push(data);
    }

    return employerPreImportDataDTO;
  }

  async messageHandler(
    importData: EmployerPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertEmployer: Custom004EEnforcementRequestToEmployer[] = [];
    const deactivateEmployerByBasis: number[] = [];

    if (importData.length > 0) {
      const chunks: EmployerPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        for (const row of chunk) {
          const model = {
            CaseID: row.CaseID,
            BasisID: row.BasisForPaymentID,
            DeliveryDate: this.insertItem(row.DeliveryDate),
            DeliveryStatusID: this.insertItem(row.DeliveryStatusID),
            IsProcessed: this.insertItem(row.IsProcessed),
            RequestToOriginalDocumentation: this.insertItem(
              row.RequestToOriginalDocumentation,
            ),
            WeDontHaveOriginalDocumentation: this.insertItem(
              row.WeDontHaveOriginalDocumentation,
            ),
            WaitingOriginalDocumentation: this.insertItem(
              row.WaitingOriginalDocumentation,
            ),
            SentOriginalDocumentation: this.insertItem(
              row.SentOriginalDocumentation,
            ),
            ReturnedOriginalDocumentation: this.insertItem(
              row.ReturnedOriginalDocumentation,
            ),
            InsertedUserID: userId,
            UpdatedUserID: userId,
          } as Custom004EEnforcementRequestToEmployer;

          insertEmployer.push(model);
          deactivateEmployerByBasis.push(row.BasisForPaymentID);
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertEmployer: insertEmployer,
        deactivateEmployerByBasis: deactivateEmployerByBasis,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  insertItem(newValue: any): any {
    if (newValue !== null) {
      if (newValue === 'null') {
        return null;
      } else if (newValue !== '') {
        return newValue;
      }
    }
    return null;
  }
}
