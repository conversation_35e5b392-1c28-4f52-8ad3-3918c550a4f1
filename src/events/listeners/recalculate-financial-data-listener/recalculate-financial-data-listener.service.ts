import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Sentry from '@sentry/node';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Redis } from 'ioredis';
import { Queues } from 'src/events/queues.enum';
import { splitOnChunks } from '../../../common/helpers/split-on-chunks';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { UploadStatus } from '../../../import/upload-status.enum';
import { EventsGateway } from '../../events.gateway';
import { RecalculateFinancialDataEvent } from '../../types/recalculate-financial-data-event';
import { RecalculateFinancialDataDto } from './dto/recalculate-financial-data.dto';
import { ImportListener } from '../import-listener';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';

@Injectable()
export class RecalculateFinancialDataListenerService extends ImportListener<
  RecalculateFinancialDataEvent,
  RecalculateFinancialDataDto
> {
  queueName: Queues.RecalculateFinancialData = Queues.RecalculateFinancialData;
  postImport = undefined;
  catalog: number = CatalogEnum.FinancialDataRecalculation;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    private readonly uploadHistoryRepository: UploadHistoryRepository,
    private readonly messageGateway: EventsGateway,
    private readonly httpService: HttpService,
    @Inject(REDIS)
    private readonly redis: Redis,
  ) {
    super(connection);
    this.prefetchNumber = 1;
  }

  async preImport(importData: any): Promise<RecalculateFinancialDataDto[]> {
    return importData;
  }

  private createUpdateImportProgressPublisher(
    UploadHistoryID: string,
    CatalogID: number,
    UserID: number,
    ImportID: number | null,
  ): (progress: number, statusId: number) => void {
    return async (
      Progress: number,
      statusId: number = UploadStatus.InProgress,
    ) => {
      if (ImportID) {
        const progressKey = REDIS_KEYS.progress(
          'financial-recalculation',
          Number(UploadHistoryID),
        );
        await this.redis.incrbyfloat(progressKey, Progress);
        const updatedProgress = +((await this.redis.get(progressKey)) || 0);

        const channel = `import:${CatalogID}:${UserID}`;
        const payload = {
          ImportID: ImportID,
          CatalogID,
          UploadHistoryID: Number(UploadHistoryID),
          StatusID: statusId,
          Progress:
            Math.ceil(updatedProgress) > 100 ? 100 : Math.ceil(updatedProgress),
        };

        this.messageGateway.server.in(channel).emit('progress', payload);
      }
    };
  }

  async messageHandler(
    importData: RecalculateFinancialDataDto[],
    userId: number,
    uploadHistoryId: string,
    chunk: number,
    additionalInfo: any,
  ): Promise<boolean> {
    const uploadHistory = await this.uploadHistoryRepository.findOne({
      importListId: this.catalog,
      id: uploadHistoryId,
    });

    let updateImportProgress;
    if (uploadHistory) {
      updateImportProgress = this.createUpdateImportProgressPublisher(
        uploadHistory.id,
        uploadHistory.importListId,
        userId,
        additionalInfo?.importId,
      );
    }

    const chunks = splitOnChunks(importData, 25);
    const progressValue = Math.floor(100 / chunks.length);
    let result: any = {};

    for (const chunk of chunks) {
      for (const { CaseID } of chunk) {
        const channel = `recalculation:${CaseID}`;
        this.messageGateway.server.in(channel).emit('recalculation-started', {
          HasTransactionForRecalculation: true,
        });
      }

      result = await this.recalculateFinancialData(chunk, userId);
      if (updateImportProgress) {
        updateImportProgress(progressValue, UploadStatus.InProgress);
      }

      for (const { CaseID } of chunk) {
        this.messageGateway.server
          .in(`recalculation:${CaseID}`)
          .emit('recalculation-completed', {
            HasTransactionForRecalculation: false,
          });
      }

      if (!result.success && uploadHistory) {
        const path = await this.saveReport(
          result.report,
          userId,
          uploadHistory.id,
        );

        await this.uploadHistoryRepository.update(
          { id: uploadHistory.id },
          { statusId: UploadStatus.Failed, resultFilePath: path },
        );
        break;
      }
    }

    if (result.success && uploadHistory) {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.Finished },
      );

      if (updateImportProgress) {
        updateImportProgress(100, UploadStatus.Finished);
      }
    } else {
      if (updateImportProgress) {
        updateImportProgress(100, UploadStatus.Failed);
      }
    }

    return true;
  }

  protected getReportPath(userId: number): string {
    return this.config.get('store') + `attachments/output/${userId}`;
  }

  private async recalculateFinancialData(
    importData: RecalculateFinancialDataDto[],
    userId: number,
  ): Promise<any> {
    const importServiceURL = this.config.get<string>('importService.url');
    try {
      await this.httpService
        .post(importServiceURL + '/recalculate-financial-data', {
          RecalculationData: importData,
          userID: userId,
        })
        .toPromise();

      return {
        success: true,
        report: [],
      };
    } catch (error: any) {
      console.log(error);
      Sentry.captureException(error);
      return {
        success: false,
        report: [{ error: error.message }],
      };
    }
  }
}
