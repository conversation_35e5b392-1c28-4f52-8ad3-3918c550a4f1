/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { EventsGateway } from '../../events.gateway';
import { RecalculateFinancialDataListenerService } from './recalculate-financial-data-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('importService.url'),
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      UploadHistoryRepository,
      ServiceParameterRepository,
    ]),
    // EventsModule,
    ImportRedisModule,
  ],
  providers: [RecalculateFinancialDataListenerService, EventsGateway],
})
export class RecalculateFinancialDataListenerModule {}
