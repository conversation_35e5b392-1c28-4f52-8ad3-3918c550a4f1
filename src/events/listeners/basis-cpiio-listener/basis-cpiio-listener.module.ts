import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../../../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { Custom004EEnforcementRequestToCpiioRepository } from '../../../repositories/legal/custom004-enforcement-request-to-cpiio.repository';
import { EventsGateway } from '../../events.gateway';
import { BasisCpiioListenerService } from './basis-cpiio-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      HistoryRepository,
      Custom004EEnforcementRequestToCpiioRepository,
      Custom004EEnforcementRequestDeliveryStatusRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [BasisCpiioListenerService, EventsGateway],
})
export class BasisCpiioListenerModule {}
