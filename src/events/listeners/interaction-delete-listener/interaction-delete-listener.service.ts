import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { UploadDTO } from '../../../import-delete-interaction/dto/upload.dto';
import { HistoryRepository } from '../../../repositories/activity/history.repository';
import { EventsGateway } from '../../events.gateway';
import { DeleteInteractionEvent } from '../../types/delete-interaction-event';
import { DeleteInteractionPreImportDataDto } from './dto/delete-interaction-pre-import-data.dto';

@Injectable()
export class InteractionDeleteListenerService extends BaseImportListenerService<
  DeleteInteractionEvent,
  DeleteInteractionPreImportDataDto
> {
  queueName: Queues.DeleteInteraction = Queues.DeleteInteraction;
  catalog: CatalogNameEnum = CatalogNameEnum.DeleteInteraction;
  catalogId: CatalogEnum = CatalogEnum.DeleteInteraction;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly historyRepository: HistoryRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: UploadDTO['importData'],
  ): Promise<DeleteInteractionPreImportDataDto[]> {
    const cpiioPreImportDataDTO: DeleteInteractionPreImportDataDto[] = [];

    const activeBooleanValues = new Set(['1', 'yes', 'true']);
    const inactiveBooleanValues = new Set(['0', 'no', 'false']);

    for (const [index, data] of importData.entries()) {
      if (activeBooleanValues.has(String(data.IsActive).toLowerCase())) {
        data.IsActive = '1';
      }

      if (inactiveBooleanValues.has(String(data.IsActive).toLowerCase())) {
        data.IsActive = '0';
      }
      cpiioPreImportDataDTO.push(data);
    }

    return cpiioPreImportDataDTO;
  }

  async messageHandler(
    importData: DeleteInteractionPreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const activateInteractions: number[] = [];
    const deactivateInteractions: number[] = [];

    if (importData.length > 0) {
      const chunks: DeleteInteractionPreImportDataDto[][] =
        this.sliceIntoChunks(importData, 1000);

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const contactIDs = chunk.map((c) => c.ContactID);
        const existInteraction =
          await this.historyRepository.getExistContactsMapping(contactIDs);
        for (const row of chunk) {
          const contact = existInteraction[row.ContactID];
          if (contact) {
            if (contact.isDeleted === 1 && row.IsActive === '0') {
              activateInteractions.push(Number(contact.id));
            }

            if (contact.isDeleted === 0 && row.IsActive === '1') {
              deactivateInteractions.push(Number(contact.id));
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        activateInteractions,
        deactivateInteractions,
        metaData: {
          userId,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }
}
