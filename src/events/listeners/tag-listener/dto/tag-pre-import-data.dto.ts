import { Expose } from 'class-transformer';

export class TagPreImportDataDTO {
  @Expose()
  public Level: string;

  @Expose()
  public LevelID: number;

  @Expose()
  public ValueID: number;

  @Expose()
  public TagName: string;

  @Expose()
  public TagID: number;

  @Expose()
  public ValidTo: string | null;

  @Expose()
  public Inserted: string | null;

  @Expose()
  public ToDelete: number | null;
}
