import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { TagForCaseDebtorNetworkRepository } from '../../../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../../../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../../../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../../../repositories/data/tag-for-invoice.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { TagListenerService } from './tag-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      CaseRepository,
      UploadHistoryRepository,
      ServiceParameterRepository,
      TagForInvoiceRepository,
      TagForCaseRepository,
      TagForCasePhoneRepository,
      TagForCaseDebtorNetworkRepository,
    ]),
    ImportRedisModule,
  ],
  providers: [TagListenerService, EventsGateway],
})
export class TagListenerModule {}
