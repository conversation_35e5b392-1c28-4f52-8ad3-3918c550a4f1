import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { format, parse } from 'date-fns';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { getManager, In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { TagForCaseDebtorNetwork } from '../../../entities/data/tag-for-case-debtor-network.entity';
import { TagForCasePhone } from '../../../entities/data/tag-for-case-phone.entity';
import { TagForCase } from '../../../entities/data/tag-for-case.entity';
import { TagForInvoice } from '../../../entities/data/tag-for-invoice.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportTagDTO } from '../../../import-tag/dto/import-tag.dto';
import { Level } from '../../../import/level.enum';
import { TagForCaseDebtorNetworkRepository } from '../../../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../../../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../../../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../../../repositories/data/tag-for-invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { TagEvent } from '../../types/tag-event';
import { TagPreImportDataDTO } from './dto/tag-pre-import-data.dto';

type Dictionary = { ID: number; Name: string };

const systemDateFormat = 'yyyy-MM-dd';
const defaultValidTo = '2100-01-01';

@Injectable()
export class TagListenerService extends BaseImportListenerService<
  TagEvent,
  TagPreImportDataDTO
> {
  queueName: Queues.Tag = Queues.Tag;
  catalog: CatalogNameEnum = CatalogNameEnum.Tag;
  catalogId: CatalogEnum = CatalogEnum.Tag;
  postImport = undefined;

  constructor(
    protected readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly tagForInvoiceRepository: TagForInvoiceRepository,
    protected readonly tagForCaseRepository: TagForCaseRepository,
    protected readonly tagForCasePhoneRepository: TagForCasePhoneRepository,
    protected readonly tagForCaseDebtorNetworkRepository: TagForCaseDebtorNetworkRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportTagDTO['importData'],
  ): Promise<TagPreImportDataDTO[]> {
    const rows: TagPreImportDataDTO[] = [];

    const levelMapping: any = {};
    const tagsMapping: any = {};

    const levels: Dictionary[] = await getManager().query(`select "ID", "Name"
from "Dictionary"."Level"
where "IsDeleted" = 0
  and "IsUseForTag" = 1
order by 1;`);

    const tags: Dictionary[] = await getManager().query(`select "ID", "Name"
from "Dictionary"."Tag"
where "IsDeleted" = 0
order by 1;`);

    for (const level of levels) {
      levelMapping[level.Name] = level.ID;
    }

    for (const tag of tags) {
      tagsMapping[tag.Name] = tag.ID;
    }

    for (const [index, row] of importData.entries()) {
      const attributes: TagPreImportDataDTO = {
        ...row,
        LevelID: levelMapping[row.Level],
        TagID: tagsMapping[row.TagName],
      };

      try {
        attributes.ValidTo =
          attributes.ValidTo && attributes.ValidTo.length > 0
            ? format(
                parse(attributes.ValidTo, 'dd.MM.yyyy', new Date()),
                systemDateFormat,
              )
            : null;

        attributes.Inserted =
          attributes.Inserted && attributes.Inserted.length > 0
            ? format(
                parse(attributes.Inserted, 'dd.MM.yyyy', new Date()),
                systemDateFormat,
              )
            : null;
      } catch (error) {
        console.log('Error in parsing date', row);
        throw error;
      }

      rows.push(attributes);
    }

    return rows;
  }

  async messageHandler(
    importData: TagPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const importConfig: {
      DeletionSourceID: number;
    } = await this.parameters.getGlobalParameterValueByName<any>(
      'importTagConfig',
    );

    const caseTags: TagForCase[] = [];
    const caseTagsDelete: number[] = [];

    const casePhoneTags: TagForCasePhone[] = [];
    const casePhoneTagsDelete: number[] = [];

    const caseEmailTags: TagForCaseDebtorNetwork[] = [];
    const caseEmailTagsDelete: number[] = [];

    const caseInvoiceTags: TagForInvoice[] = [];
    const caseInvoiceTagsDelete: number[] = [];

    if (importData.length > 0) {
      const chunks: TagPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = (Number(jobPercent) ?? 100) / chunks.length;

      for (const chunk of chunks) {
        const caseRows = chunk.filter((item) => item.LevelID === Level.Case);
        const phoneRows = chunk.filter((item) => item.LevelID === Level.Phone);
        const emailRows = chunk.filter((item) => item.LevelID === Level.Email);
        const invoiceRows = chunk.filter(
          (item) => item.LevelID === Level.Invoice,
        );

        if (caseRows.length > 0) {
          const tagIDs = caseRows.map((item) => item.TagID);
          const caseIDs = caseRows.map((item) => item.ValueID);

          const existsCaseTags = await this.tagForCaseRepository.find({
            where: {
              caseId: In(caseIDs),
              tagId: In(tagIDs),
              isDeleted: 0,
            },
          });

          const existsCaseTagMap: any = {};
          for (const { caseId, tagId, id } of existsCaseTags) {
            existsCaseTagMap[caseId] = existsCaseTagMap[caseId] ?? {};
            existsCaseTagMap[caseId][tagId] = id;
          }

          for (const row of caseRows) {
            if (existsCaseTagMap[row.ValueID]?.[row.TagID]) {
              caseTagsDelete.push(existsCaseTagMap[row.ValueID][row.TagID]);
            }

            if (!row.ToDelete) {
              caseTags.push(
                this.tagForCaseRepository.create({
                  caseId: row.ValueID,
                  tagId: row.TagID,
                  validTo: row.ValidTo ?? defaultValidTo,
                  inserted: row.Inserted ?? undefined,
                }),
              );
            }
          }
        }

        if (phoneRows.length > 0) {
          const tagIDs = phoneRows.map((item) => item.TagID);
          const phoneIDs = phoneRows.map((item) => item.ValueID);

          const phoneIdToCaseIdMap = await this.getCaseIdByPhoneId(phoneIDs);
          const caseIDs = Object.values(phoneIdToCaseIdMap);

          const existsCaseTags = await this.tagForCasePhoneRepository.find({
            where: {
              phoneId: In(phoneIDs),
              tagId: In(tagIDs),
              caseId: In(caseIDs),
              isDeleted: 0,
            },
          });

          const existsTagMap: any = {};

          for (const { caseId, phoneId, tagId, id } of existsCaseTags) {
            existsTagMap[caseId] = existsTagMap[caseId] ?? {};
            existsTagMap[caseId][phoneId] = existsTagMap[caseId][phoneId] ?? {};
            existsTagMap[caseId][phoneId][tagId] = id;
          }

          for (const row of phoneRows) {
            const caseId = phoneIdToCaseIdMap[row.ValueID];
            if (existsTagMap[caseId]?.[row.ValueID]?.[row.TagID]) {
              casePhoneTagsDelete.push(
                existsTagMap[caseId][row.ValueID][row.TagID],
              );
            }

            if (!row.ToDelete) {
              casePhoneTags.push(
                this.tagForCasePhoneRepository.create({
                  caseId: caseId,
                  phoneId: row.ValueID,
                  tagId: row.TagID,
                  validTo: row.ValidTo ?? defaultValidTo,
                  inserted: row.Inserted ?? undefined,
                }),
              );
            }
          }
        }

        if (emailRows.length > 0) {
          const tagIDs = emailRows.map((item) => item.TagID);
          const emailIDs = emailRows.map((item) => item.ValueID);

          const emailIdToCaseIdMap = await this.getCaseIdByEmailId(emailIDs);
          const caseIDs = Object.values(emailIdToCaseIdMap);

          const existsCaseTags =
            await this.tagForCaseDebtorNetworkRepository.find({
              where: {
                debtorNetworkId: In(emailIDs),
                tagId: In(tagIDs),
                caseId: In(caseIDs),
                isDeleted: 0,
              },
            });

          const existsTagMap: any = {};
          for (const { caseId, debtorNetworkId, tagId, id } of existsCaseTags) {
            existsTagMap[caseId] = existsTagMap[caseId] ?? {};
            existsTagMap[caseId][debtorNetworkId] =
              existsTagMap[caseId][debtorNetworkId] ?? {};
            existsTagMap[caseId][debtorNetworkId][tagId] = id;
          }

          for (const row of emailRows) {
            const caseId = emailIdToCaseIdMap[row.ValueID];
            if (existsTagMap[caseId]?.[row.ValueID]?.[row.TagID]) {
              caseEmailTagsDelete.push(
                existsTagMap[caseId][row.ValueID][row.TagID],
              );
            }

            if (!row.ToDelete) {
              caseEmailTags.push(
                this.tagForCaseDebtorNetworkRepository.create({
                  caseId: caseId,
                  debtorNetworkId: row.ValueID,
                  tagId: row.TagID,
                  validTo: row.ValidTo ?? defaultValidTo,
                  inserted: row.Inserted ?? undefined,
                }),
              );
            }
          }
        }

        if (invoiceRows.length > 0) {
          const tagIDs = invoiceRows.map((item) => item.TagID);
          const invoiceIDs = invoiceRows.map((item) => item.ValueID);

          const existsCaseTags = await this.tagForInvoiceRepository.find({
            where: {
              invoiceId: In(invoiceIDs),
              tagId: In(tagIDs),
              isDeleted: 0,
            },
          });

          const existsTagMap: any = {};
          for (const { invoiceId, tagId, id } of existsCaseTags) {
            existsTagMap[invoiceId] = existsTagMap[invoiceId] ?? {};
            existsTagMap[invoiceId][tagId] = id;
          }

          for (const {
            ValueID,
            TagID,
            ToDelete,
            ValidTo,
            Inserted,
          } of invoiceRows) {
            if (existsTagMap[ValueID]?.[TagID]) {
              caseInvoiceTagsDelete.push(existsTagMap[ValueID][TagID]);
            }

            if (!ToDelete) {
              caseInvoiceTags.push(
                this.tagForInvoiceRepository.create({
                  invoiceId: ValueID,
                  tagId: TagID,
                  validTo: ValidTo ?? defaultValidTo,
                  inserted: Inserted ?? undefined,
                }),
              );
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        caseTags,
        caseTagsDelete,
        casePhoneTags,
        casePhoneTagsDelete,
        caseEmailTags,
        caseEmailTagsDelete,
        caseInvoiceTags,
        caseInvoiceTagsDelete,
        metaData: {
          userId,
          deletionSourceId: importConfig.DeletionSourceID,
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  protected async getCaseIdByPhoneId(
    phoneIDs: number[],
  ): Promise<{ [key: number]: number }> {
    const rows: { PhoneID: number; CaseID: number }[] = await getManager()
      .query(`select dp."ID" as "PhoneID", dc."ID" as "CaseID" from "Data"."Phone" dp
    left join "Data"."Case" dc on dp."DebtorID" = dc."DebtorID"
    where dp."ID" in (${phoneIDs.join(',')});`);

    const mapping: { [key: number]: number } = {};
    for (const row of rows) {
      mapping[row.PhoneID] = row.CaseID;
    }

    return mapping;
  }

  protected async getCaseIdByEmailId(
    emailIDs: number[],
  ): Promise<{ [key: number]: number }> {
    const rows: { EmailID: number; CaseID: number }[] = await getManager()
      .query(`select dp."ID" as "EmailID", dc."ID" as "CaseID" from "Data"."DebtorNetwork" dp
    left join "Data"."Case" dc on dp."DebtorID" = dc."DebtorID"
    where dp."ID" in (${emailIDs.join(',')}) and dp."TypeID" = 2;`);

    const mapping: { [key: number]: number } = {};
    for (const row of rows) {
      mapping[row.EmailID] = row.CaseID;
    }

    return mapping;
  }
}
