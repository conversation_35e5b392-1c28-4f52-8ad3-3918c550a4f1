import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportWithParameterListenerService } from '../../../common/imports/listeners/base-import-with-parameter-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { ImportCaseParameterDTO } from '../../../import-case-parameter/dto/import-case-parameter.dto';
import { CaseParameterRepository } from '../../../repositories/data/case-parameter.repository';
import { ParameterRepository } from '../../../repositories/dictionary/parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { CaseParameterEvent } from '../../types/case-parameter-event';
import { CaseParameterPreImportDataDTO } from './dto/case-parameter-pre-import-data.dto';

@Injectable()
export class CaseParameterListenerService extends BaseImportWithParameterListenerService<
  CaseParameterEvent,
  CaseParameterPreImportDataDTO
> {
  queueName: Queues.CaseParameter = Queues.CaseParameter;
  catalog: CatalogNameEnum = CatalogNameEnum.CaseParameter;
  catalogId: CatalogEnum = CatalogEnum.CaseParameter;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly caseParameterRepository: CaseParameterRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
      parameterRepository,
    );
    this.listen();
  }

  async preImport(
    importData: ImportCaseParameterDTO['importData'],
    userId: number,
    uploadHistoryId: string,
  ): Promise<CaseParameterPreImportDataDTO[]> {
    return this.formatParameters(importData, userId, uploadHistoryId);
  }

  async messageHandler(
    importData: CaseParameterPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const parametersCreate: any[] = [];
      const parametersDelete: any[] = [];
      const activateDebtorRegrouping: any[] = [];
      const updatedCases: number[] = [];

      const chunks: CaseParameterPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const slugToId = await this.getParameterBySlug();
      const parametersHasDebtorRegrouping =
        await this.parameterRepository.getParametersHasDebtorRegrouping();

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const caseIDs = chunk.map((row) => row.CaseID);

        const existsParameters = await this.caseParameterRepository.find({
          where: {
            caseId: In(caseIDs),
            isDeleted: 0,
          },
        });

        const caseToExistsParameterMapping: {
          [key: string]: { [key2: string]: string };
        } = {};

        const parameterToValueMapping: {
          [key: string]: any;
        } = {};

        for (const parameter of existsParameters) {
          if (!caseToExistsParameterMapping[parameter.caseId]) {
            caseToExistsParameterMapping[parameter.caseId] = {};
          }
          caseToExistsParameterMapping[parameter.caseId][
            parameter.parameterId
          ] = parameter.id;

          parameterToValueMapping[parameter.id] = parameter.value;
        }

        for (const row of chunk) {
          const parameters = Object.keys(row).filter((c) => c !== 'CaseID');

          const caseId = row.CaseID;

          for (const parameter of parameters) {
            const parameterId = slugToId[parameter];
            const value = row[parameter];

            if (
              caseToExistsParameterMapping[caseId] &&
              caseToExistsParameterMapping[caseId][parameterId]
            ) {
              const existsParameterId =
                caseToExistsParameterMapping[caseId][parameterId];
              const existsValue = parameterToValueMapping[existsParameterId];

              if (value === existsValue || value === '') {
                continue;
              }

              parametersDelete.push(existsParameterId);

              if (
                this.hasParameter(
                  value,
                  parameterId,
                  parametersHasDebtorRegrouping,
                )
              ) {
                activateDebtorRegrouping.push(row.CaseID);
              }

              if (value && String(value).toLowerCase() !== 'null') {
                parametersCreate.push({
                  caseId: caseId,
                  parameterId,
                  value,
                  insertedUserId: userId,
                  updatedUserId: userId,
                });
              }
            } else {
              if (value !== '' && String(value).toLowerCase() !== 'null') {
                parametersCreate.push({
                  caseId: caseId,
                  parameterId,
                  value,
                  insertedUserId: userId,
                  updatedUserId: userId,
                });

                if (
                  this.hasParameter(
                    value,
                    parameterId,
                    parametersHasDebtorRegrouping,
                  )
                ) {
                  activateDebtorRegrouping.push(row.CaseID);
                }
              }
            }
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
        await this.createContactInHistoryForParameters(chunk, userId);
      }

      const casesToActivateLegalRules = importData.map((r) => r.CaseID);

      await this.saveResult(Number(uploadHistoryId), page, {
        parametersCreate,
        parametersDelete,
        activateDebtorRegrouping,
        casesToActivateLegalRules,
        userId,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }
    return true;
  }
}
