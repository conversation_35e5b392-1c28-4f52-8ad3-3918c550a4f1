import { ConfigService } from '@nestjs/config';

import { TemporaryTable } from 'src/common/temporary-table/temporary-table';
import { EntityManager, EntitySchema, getMetadataArgsStorage } from 'typeorm';
import { Court } from '../../../../entities/legal-ua/court.entity';

function getAllColumns(model: any): string[] {
  const columns = getMetadataArgsStorage().columns.filter(
    (column) => column.target === model,
  );
  return columns.map((column) => column.propertyName);
}

const TemporaryCourtEntity = new EntitySchema({
  name: 'TemporaryCourt',
  columns: {},
});

const tableName = 'TemporaryCourt';

export class TemporaryLegalUaCourt extends TemporaryTable<Court> {
  constructor(
    protected manager: EntityManager,
    protected config: ConfigService,
    protected columnList: string[],
    protected tableEntity = TemporaryCourtEntity,
  ) {
    super();
  }

  async manualInitialize(): Promise<void> {
    const columns = getMetadataArgsStorage().columns.filter(
      (column) => column.target === Court,
    );
    const columnsList = columns
      .map((column) => {
        if (this.columnList.includes(column.propertyName)) {
          return `"${column.propertyName}" ${column.options.type}`;
        }
        return false;
      })
      .filter((index) => index)
      .join(', ');
    const createTableSQL =
      `CREATE TEMPORARY TABLE "${tableName}"(` + columnsList + ');';
    await this.manager.query(`DROP TABLE if EXISTS "${tableName}";`);
    await this.manager.query(createTableSQL);
  }
  protected decompose(data: Court[]): any[] {
    const fieldsToSync = getAllColumns(Court);
    return data.map((v: any) => {
      const itemValues: any = {};
      for (const field of fieldsToSync) {
        if (v[field] !== undefined) {
          itemValues[field] = v[field];
        }
      }
      return itemValues;
    });
  }

  async updateOriginTable(): Promise<void> {
    const excludeSync = new Set(['ID', 'Inserted', 'Updated']);
    const fieldsToSync = getAllColumns(Court).filter(
      (f) => !excludeSync.has(f),
    );
    const setValue = fieldsToSync
      .map((field) => {
        if (this.columnList.includes(field)) {
          return field;
        }
        return false;
      })
      .filter((index) => index)
      .map((field) => `"${field}" = temp."${field}"`)
      .join(', ');
    return this.manager.query(`
    UPDATE "LegalUA"."Court" as original
                        set
                            ${setValue}
                        from "${tableName}" "temp" 
                        WHERE temp."ID" = original."ID";
    `);
  }
}
