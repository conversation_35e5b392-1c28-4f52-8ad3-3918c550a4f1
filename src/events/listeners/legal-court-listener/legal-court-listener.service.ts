import { Inject, Injectable } from '@nestjs/common';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { In } from 'typeorm';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { Court } from '../../../entities/legal-ua/court.entity';
import { ImportLegalCourtDTO } from '../../../import-legal-court/dto/import-legal-court.dto';
import { RegionRepository } from '../../../repositories/dictionary/region.repository';
import { CourtTypeRepository } from '../../../repositories/legal-ua/court-type.repository';
import { CourtRepository } from '../../../repositories/legal-ua/court.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalCourtEvent } from '../../types/legal-court-event';
import { LegalCourtPreImportDataDTO } from './dto/legal-court-pre-import-data.dto';

@Injectable()
export class LegalCourtListenerService extends BaseImportListenerService<
  LegalCourtEvent,
  LegalCourtPreImportDataDTO
> {
  queueName: Queues.LegalCourt = Queues.LegalCourt;
  catalog: CatalogNameEnum = CatalogNameEnum.LegalCourt;
  catalogId: CatalogEnum = CatalogEnum.LegalCourt;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly courtRepository: CourtRepository,
    protected readonly courtTypeRepository: CourtTypeRepository,
    protected readonly regionRepository: RegionRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async preImport(
    importData: ImportLegalCourtDTO['importData'],
  ): Promise<LegalCourtPreImportDataDTO[]> {
    const legalCourtPreImportDataDTO: LegalCourtPreImportDataDTO[] = [];
    const courtTypes = await this.courtTypeRepository.getList();
    const regions = await this.regionRepository.getList();

    const typeMapping: { [key: string]: number } = {};
    const regionMapping: { [key: string]: number } = {};

    for (const courtType of courtTypes) {
      typeMapping[courtType.name] = courtType.id;
    }

    for (const region of regions) {
      regionMapping[region.name] = region.id;
    }

    for (const [index, data] of importData.entries()) {
      if (data.ChangeCourtID === '') {
        importData[index].ChangeCourtID = null;
      }

      if (data.Type) {
        data.TypeID = typeMapping[data.Type];
      }

      if (data.Region) {
        data.RegionID = regionMapping[data.Region];
      }

      legalCourtPreImportDataDTO.push(data);
    }

    return legalCourtPreImportDataDTO;
  }

  async messageHandler(
    importData: LegalCourtPreImportDataDTO[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    const insertCourt: Court[] = [];
    const updateCourt: Court[] = [];

    if (importData.length > 0) {
      const chunks: LegalCourtPreImportDataDTO[][] = this.sliceIntoChunks(
        importData,
        1000,
      );

      const chunkPercent = Math.ceil(
        (Number(jobPercent) ?? 100) / chunks.length,
      );

      for (const chunk of chunks) {
        const IDs = chunk.map((index) => index.ID);

        const existsRecords = await this.courtRepository.find({
          where: {
            ID: In(IDs),
            IsDeleted: 0,
          },
        });

        const existsRecordsMapping: { [key: number]: Court } = {};
        for (const existsRecord of existsRecords) {
          existsRecordsMapping[existsRecord.ID] = existsRecord;
        }

        for (const row of chunk) {
          if (!row.ID) {
            const model = {
              Name: row.Name,
              TypeID: row.TypeID,
              InsertedUserID: userId,
              UpdatedUserID: userId,
              CityCode: row.CityCode,
              RegionID: row.RegionID,
              City: row.City,
              District: row.District,
              Street: row.Street,
              House: row.House,
              Building: row.Building,
              Apartment: row.Apartment,
              ChangeCourtID:
                row.ChangeCourtID !== 'null' ? row.ChangeCourtID : null,
              Receiver: row.Receiver,
              InvoiceNumber: row.InvoiceNumber,
              ReceiverCode: row.ReceiverCode,
              PaymentDestination: row.PaymentDestination,
              PhoneNumber: row.PhoneNumber,
              Email: row.Email,
            } as Court;
            insertCourt.push(model);
          } else {
            updateCourt.push(
              this.updateItem(existsRecordsMapping[row.ID], row, userId),
            );
          }
        }

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
      }

      await this.saveResult(Number(uploadHistoryId), page, {
        insertCourt,
        updateCourt,
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }

  updateItem(
    values: Court,
    newValues: LegalCourtPreImportDataDTO,
    userId: number,
  ): Court {
    for (const field of Object.keys(values)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const newValue = newValues[field];

      if (newValue !== null) {
        if (newValue === 'null') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = null;
        } else if (newValue !== '') {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          values[field] = newValue;
        }
      }
    }
    values['UpdatedUserID'] = userId;
    return values;
  }
}
