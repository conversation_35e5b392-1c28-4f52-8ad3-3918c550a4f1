import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { Queues } from 'src/events/queues.enum';
import { BaseImportListenerService } from '../../../common/imports/listeners/base-import-listener.service';
import { REDIS } from '../../../common/redis/constants';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { Case } from '../../../entities/data/case.entity';
import { ContactPersonRelationEntity } from '../../../entities/data/contact-person-relation.entity';
import { Contact<PERSON>erson } from '../../../entities/data/contact-person.entity';
import { Debtor } from '../../../entities/data/debtor.entity';
import { CatalogNameEnum } from '../../../entities/enum/catalog-name.enum';
import { CatalogEnum } from '../../../entities/enum/catalog.enum';
import { CourtProcess } from '../../../entities/legal/court-process.entity';
import { LegalInvoice } from '../../../entities/legal/legal-invoice.entity';
import { ImportLegalCaseDto } from '../../../import-legal-case/dto/import-legal-case.dto';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalCaseEvent } from '../../types/legal-case-event';
import { LegalCaseListenerDataAccess } from './legal-case-listener-data-access';
import { LegalCasePreImportDataDto } from './dto/legal-case-pre-import-data.dto';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';

@Injectable()
export class LegalCaseListenerService extends BaseImportListenerService<
  LegalCaseEvent,
  LegalCasePreImportDataDto
> {
  queueName: Queues.LegalCase = Queues.LegalCase;
  catalog: CatalogNameEnum = CatalogNameEnum.LegalCase;
  catalogId: CatalogEnum = CatalogEnum.LegalCase;
  postImport = undefined;

  constructor(
    private readonly config: ConfigService,
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly httpService: HttpService,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly legalCaseListenerDataAccess: LegalCaseListenerDataAccess,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly messageGateway: EventsGateway,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen().catch((error) => console.log(error));
  }

  async preImport(
    importData: ImportLegalCaseDto['importData'],
  ): Promise<LegalCasePreImportDataDto[]> {
    return importData;
  }

  async messageHandler(
    importData: LegalCasePreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
    additionalParams: any,
  ): Promise<boolean> {
    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    if (importData.length > 0) {
      const newDebtors: Debtor[] = [];
      const newCases: Case[] = [];
      const newContactPersons: ContactPerson[] = [];
      const newContactPersonRelations: ContactPersonRelationEntity[] = [];
      const newLegalInvoice: LegalInvoice[] = [];
      const newCourtProcess: CourtProcess[] = [];

      const legalCases: number[] = [];
      const preLegalCases: number[] = [];
      const mappingResults: any[] = [];

      const {
        importDebtorData,
        importCaseData,
        importContactPersonData,
        importContactPersonRelationData,
        importLegalInvoiceData,
        importCourtProcessData,
        casesLegal,
        casesPreLegal,
        results,
      } = await this.legalCaseListenerDataAccess.runCreateLegalCase(
        importData,
        userId,
        additionalParams.PackageID,
      );

      newDebtors.push(...importDebtorData);
      newCases.push(...importCaseData);
      newContactPersons.push(...importContactPersonData);
      newContactPersonRelations.push(...importContactPersonRelationData);
      newLegalInvoice.push(...importLegalInvoiceData);
      newCourtProcess.push(...importCourtProcessData);

      legalCases.push(...casesLegal);
      preLegalCases.push(...casesPreLegal);

      mappingResults.push(...results);

      await this.emitProgress(Number(uploadHistoryId), Number(jobPercent));

      await this.saveResult(Number(uploadHistoryId), page, {
        newDebtors,
        newCases,
        newContactPersons,
        newContactPersonRelations,
        newLegalInvoice,
        newCourtProcess,
        legalCases,
        preLegalCases,
        mappingResults,
        metaData: {
          userId,
          actionID: additionalParams['actionID'],
        },
      });

      await this.markAsCompleted(Number(uploadHistoryId), page);
    }

    return true;
  }
}
