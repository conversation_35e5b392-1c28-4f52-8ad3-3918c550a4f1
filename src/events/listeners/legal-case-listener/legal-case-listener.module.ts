/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { ImportRedisModule } from '../../../common/redis/import-redis.module';
import { ContactPersonRelationRepository } from '../../../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { CourtProcessRepository } from '../../../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { EventsGateway } from '../../events.gateway';
import { LegalCaseListenerDataAccess } from './legal-case-listener-data-access';
import { LegalCaseListenerService } from './legal-case-listener.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    ImportRedisModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CaseRepository,
      UploadHistoryRepository,
      DebtorRepository,
      LegalInvoiceRepository,
      CourtProcessRepository,
      ServiceParameterRepository,
      ContactPersonRepository,
      ContactPersonRelationRepository,
    ]),
  ],
  providers: [
    LegalCaseListenerService,
    LegalCaseListenerDataAccess,
    EventsGateway,
  ],
})
export class LegalCaseListenerModule {}
