import { Injectable } from '@nestjs/common';

import { CaseRepository } from 'src/repositories/data/case.repository';
import { ContactPersonRelationRepository } from '../../../repositories/data/contact-person-relation.repository';
import { ContactPersonRepository } from '../../../repositories/data/contact-person.repository';
import { DebtorRepository } from '../../../repositories/data/debtor.repository';
import { CourtProcessRepository } from '../../../repositories/legal/court-process.repository';
import { LegalInvoiceRepository } from '../../../repositories/legal/legal-invoice.repository';
import { ServiceParameterRepository } from '../../../repositories/service-parameter.repository';
import { LegalCasePreImportDataDto } from './dto/legal-case-pre-import-data.dto';

@Injectable()
export class LegalCaseListenerDataAccess {
  constructor(
    private caseRepository: CaseRepository,
    private debtorRepository: DebtorRepository,
    private legalInvoiceRepository: LegalInvoiceRepository,
    private courtProcessRepository: CourtProcessRepository,
    private serviceParameterRepository: ServiceParameterRepository,
    private contactPersonRepository: ContactPersonRepository,
    private contactPersonRelationRepository: ContactPersonRelationRepository,
  ) {}

  public async runCreateLegalCase(
    cases: LegalCasePreImportDataDto[],
    userId: number,
    packageID: number,
  ): Promise<any> {
    return await this.legalInvoiceRepository.createLegalCases(
      cases,
      userId,
      packageID,
      this.caseRepository,
      this.debtorRepository,
      this.courtProcessRepository,
      this.contactPersonRepository,
      this.contactPersonRelationRepository,
    );
  }
}
