export interface IPhonesPreparedData {
  phoneId?: number;
  caseId: number;
  phoneNumber: string;
  phoneNote: string;
  isActive: boolean;
  isMobile: number;
  phoneTypeId: number;
  phoneSourceId: number;
  partyTypeId: number;
  contactPersonId?: number;
  existingPhoneId?: string | null;
  isDeleted?: number;
  canCall?: boolean | undefined;
  canSendSms?: boolean | undefined;
  predictiveOn?: boolean | undefined;
}

export interface ICustom004PhoneData {
  phoneId: number;
  canCall: boolean | undefined;
  canSendSms: boolean | undefined;
  predictiveOn: boolean | undefined;
}
