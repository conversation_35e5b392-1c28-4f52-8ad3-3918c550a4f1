/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';
import { EventsGateway } from '../../events.gateway';
import { PhoneListenerService } from './phone-listener.service';
import { PhoneTypeRepository } from 'src/repositories/phone-type.repository';
import { DataSourceRepository } from 'src/repositories/dictionary/data-source.repository';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { PhoneRepository } from 'src/repositories/data/phone.repository';
import { PhoneCodeRepository } from 'src/repositories/dictionary/phone-code.repository';
import { ImportRedisModule } from 'src/common/redis/import-redis.module';
import { PhoneToCaseRepository } from 'src/repositories/data/phone-to-case.repository';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ContactPerson } from 'src/entities/data/contact-person.entity';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { Custom004PhoneRepository } from 'src/repositories/data/custom004-phone.repository';
import { Custom001PhoneStatusRepository } from 'src/repositories/data/custom001-phone-status.repository';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    TypeOrmModule.forFeature([
      PhoneTypeRepository,
      DataSourceRepository,
      PartyTypeRepository,
      PhoneRepository,
      PhoneCodeRepository,
      PhoneToCaseRepository,
      CaseRepository,
      ContactPerson,
      UploadHistoryRepository,
      ServiceParameterRepository,
      Custom004PhoneRepository,
      Custom001PhoneStatusRepository,
    ]),
    ImportRedisModule,
    // EventsModule,
  ],
  providers: [EventsGateway, PhoneListenerService],
})
export class PhoneListenerModule {}
