import { Expose } from 'class-transformer';

export class PhonePreImportDataDto {
  @Expose()
  PhoneID: number;

  @Expose()
  CaseID: number;

  @Expose()
  PhoneNumber: string;

  @Expose()
  IsActive: string;

  @Expose()
  PhoneNote: string;

  @Expose()
  PhoneType: string;

  @Expose()
  PhoneSource: string;

  @Expose()
  PartyType: string;

  @Expose()
  ContactPersonID: number;

  @Expose()
  CanCall: string;

  @Expose()
  CanSendSms: string;

  @Expose()
  PredictiveOn: string;

  @Expose()
  Status: string;
}
