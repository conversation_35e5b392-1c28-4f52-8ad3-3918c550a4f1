import { Inject, Injectable } from '@nestjs/common';
import { PhoneEvent } from 'src/events/types/phone-event';
import { Queues } from 'src/events/queues.enum';
import { PhonePreImportDataDto } from './dto/phone-pre-import-data.dto';
import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { PhoneTypeRepository } from 'src/repositories/phone-type.repository';
import { PartyTypeRepository } from 'src/repositories/party-type.repository';
import { getManager, In } from 'typeorm';
import { Phone } from 'src/entities/data/phone.entity';
import { DataSourceRepository } from 'src/repositories/dictionary/data-source.repository';
import { ICustom004PhoneData, IPhonesPreparedData } from './types';
import { PhoneRepository } from 'src/repositories/data/phone.repository';
import { PhoneCodeRepository } from 'src/repositories/dictionary/phone-code.repository';
import { Redis } from 'ioredis';
import { REDIS } from 'src/common/redis/constants';
import { PhoneToCaseRepository } from 'src/repositories/data/phone-to-case.repository';
import { PhoneToCase } from 'src/entities/data/phone-to-case.entity';
import { CaseRepository } from 'src/repositories/data/case.repository';
import { ContactPersonRepository } from 'src/repositories/data/contact-person.repository';
import { REDIS_IMPORT_KEYS as REDIS_KEYS } from '../../../config/redis-import-keys';
import { CatalogNameEnum } from 'src/entities/enum/catalog-name.enum';
import { BaseImportListenerService } from 'src/common/imports/listeners/base-import-listener.service';
import { EventsGateway } from 'src/events/events.gateway';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from 'src/repositories/service-parameter.repository';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { Custom004PhoneRepository } from 'src/repositories/data/custom004-phone.repository';
import { Custom001PhoneStatusRepository } from 'src/repositories/data/custom001-phone-status.repository';

interface PreparedData {
  phonesToInsert: IPhonesPreparedData[];
  phonesToUpdate: IPhonesPreparedData[];
  caseIds: number[];
  contactPersonId: number[];
}

type ContactPersonResult = {
  ContactPersonID: number;
  CaseID: number;
  Value: any;
};

@Injectable()
export class PhoneListenerService extends BaseImportListenerService<
  PhoneEvent,
  PhonePreImportDataDto
> {
  queueName: Queues.Phone = Queues.Phone;
  catalog: CatalogNameEnum = CatalogNameEnum.Phone;
  catalogId: CatalogEnum = CatalogEnum.Phone;
  postImport = undefined;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly parameters: ServiceParameterRepository,
    protected readonly messageGateway: EventsGateway,
    protected readonly phoneTypeRepository: PhoneTypeRepository,
    protected readonly dataSourceRepository: DataSourceRepository,
    protected readonly partyTypeRepository: PartyTypeRepository,
    protected readonly phoneRepository: PhoneRepository,
    protected readonly phoneCodeRepository: PhoneCodeRepository,
    protected readonly phoneToCaseRepository: PhoneToCaseRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly contactPersonRepository: ContactPersonRepository,
    protected readonly custom004PhoneRepository: Custom004PhoneRepository,
    protected readonly custom001PhoneStatusRepository: Custom001PhoneStatusRepository,
  ) {
    super(
      connection,
      messageGateway,
      redis,
      uploadHistoryRepository,
      parameters,
    );
    this.listen();
  }

  async messageHandler(
    importData: PhonePreImportDataDto[],
    userId: number,
    uploadHistoryId: string,
    page: number,
  ) {
    console.log('import data', importData);

    const jobPercent = await this.redis.get(
      REDIS_KEYS.chunkPercent(this.catalog, Number(uploadHistoryId)),
    );

    let insertedCount = 0;
    let updatedCount = 0;

    if (importData.length > 0) {
      const chunks = this.sliceIntoChunks(
        importData,
        1000,
      ) as PhonePreImportDataDto[][];

      const chunkPercent = (Number(jobPercent) ?? 100) / chunks.length;

      for (const chunk of chunks) {
        const preparedData = await this.prepareToImport(chunk);

        const [inserted] = await Promise.all([
          this.insertPhones(preparedData, userId),
          this.updatePhones(preparedData, userId),
        ]);

        await this.emitProgress(Number(uploadHistoryId), chunkPercent);
        insertedCount += inserted.createdCount;
        updatedCount +=
          preparedData.phonesToUpdate.length + inserted.updatedCount;
      }

      await this.markAsCompleted(Number(uploadHistoryId), page);
      await this.saveResult(Number(uploadHistoryId), page, {
        insertedPhonesCount: insertedCount,
        updatedPhonesCount: updatedCount,
        metaData: {
          userId,
        },
      });
    }

    return true;
  }

  async prepareToImport(importData: PhonePreImportDataDto[]) {
    const phonesToInsert: IPhonesPreparedData[] = [];
    const phonesToUpdate: IPhonesPreparedData[] = [];

    const caseIds = new Set<number>();
    const contactPersonIds = new Set<number>();
    const phoneCase: Record<string, string> = {};

    const phoneTypesSet = new Set();
    const phoneSourcesSet = new Set();
    const partyTypesSet = new Set();
    const statusesSet = new Set();

    for (const data of importData) {
      phoneTypesSet.add(data.PhoneType);
      phoneSourcesSet.add(data.PhoneSource);
      partyTypesSet.add(data.PartyType);
      statusesSet.add(data.Status);

      phoneCase[String(data.PhoneNumber)] = String(data.CaseID);
    }

    const phoneCaseDebtorPairs = await this.getPhoneDebtorPairs(phoneCase);

    const [phoneTypes, phoneSources, partyTypes, phones, phoneStatuses] =
      await Promise.all([
        this.phoneTypeRepository.getListByNames([...phoneTypesSet]),
        this.dataSourceRepository.getPhoneSourceList([...phoneSourcesSet]),
        this.partyTypeRepository.getPartyTypeList([...partyTypesSet]),
        this.phoneRepository.getPhonesByNumbersAndDebtor(phoneCaseDebtorPairs),
        this.custom001PhoneStatusRepository.getListByNames([...statusesSet]),
      ]);

    const reducedTypes = this.accumulateData(phoneTypes, 'name');
    const reducedSources = this.accumulateData(phoneSources, 'name');
    const reducedPartyTypes = this.accumulateData(partyTypes, 'name');
    const reducedPhones = this.accumulateData(phones, 'phoneNumber');
    const reducesPhoneStatuses = this.accumulateData(phoneStatuses, 'name');

    for (const data of importData) {
      const phoneType = reducedTypes[data.PhoneType];
      const phoneSource = reducedSources[data.PhoneSource];
      const partyType = reducedPartyTypes[data.PartyType];
      const phone = reducedPhones[data.PhoneNumber];
      const phoneStatus = reducesPhoneStatuses[data.Status];

      const updatedData = {
        phoneId: data.PhoneID,
        existingPhoneId: phone?.id ?? null,
        caseId: data.CaseID,
        phoneNumber: data.PhoneNumber,
        phoneNote: data.PhoneNote,
        isActive: this.getBooleanVlue(data.IsActive) as boolean,
        isMobile: await this.isMobilePhone(data.PhoneNumber),
        phoneTypeId: phoneType?.id,
        phoneSourceId: phoneSource?.id,
        partyTypeId: partyType?.id,
        contactPersonId: data.ContactPersonID,
        isDeleted: phoneStatus?.id,
        canCall: this.getBooleanVlue(data.CanCall),
        canSendSms: this.getBooleanVlue(data.CanSendSms),
        predictiveOn: this.getBooleanVlue(data.PredictiveOn),
      };

      caseIds.add(data.CaseID);
      contactPersonIds.add(data.ContactPersonID);

      data.PhoneID
        ? phonesToUpdate.push(updatedData)
        : phonesToInsert.push(updatedData);
    }

    return {
      phonesToInsert,
      phonesToUpdate,
      caseIds: [...caseIds],
      contactPersonId: [...contactPersonIds],
    };
  }

  async preImport(importData: PhonePreImportDataDto[]) {
    return importData;
  }

  accumulateData<T>(data: T[], key: keyof T): Record<string, T> {
    return data.reduce((accumulator, item) => {
      const keyValue = item[key];
      if (typeof keyValue === 'string') {
        accumulator[keyValue] = item;
      } else {
        throw new TypeError(
          `Key value must be a string, got ${typeof keyValue}`,
        );
      }
      return accumulator;
    }, {} as Record<string, T>);
  }

  async insertPhones(preparedData: PreparedData, userId: number) {
    const { phonesToInsert, caseIds, contactPersonId } = preparedData;

    const existingPhones = [];
    const newPhones = [];

    for (const phone of phonesToInsert) {
      if (phone.existingPhoneId) {
        existingPhones.push(phone);
      } else {
        newPhones.push(phone);
      }
    }

    await this.createNewPhones(newPhones, caseIds, contactPersonId, userId);
    await this.createExistingPhones(
      existingPhones,
      caseIds,
      contactPersonId,
      userId,
    );

    return {
      updatedCount: existingPhones.length,
      createdCount: newPhones.length,
    };
  }

  async createExistingPhones(
    existingPhones: IPhonesPreparedData[],
    caseIds: number[],
    contactPersonId: number[],
    userId: number,
  ) {
    if (existingPhones.length === 0) {
      return;
    }

    const isCustomPhoneType =
      await this.parameters.getGlobalParameterValueByName('customPhoneType');

    const phoneToCaseData: PhoneToCase[] = [];
    const caseToPhones: Record<string, number[]> = {};
    const custom004PhonesToInsert: ICustom004PhoneData[] = [];

    for (const phone of existingPhones) {
      phoneToCaseData.push({
        phoneId: String(phone.existingPhoneId),
        caseId: String(phone.caseId),
        deactivated: phone.isActive ? 0 : 1,
        insertedUserId: userId,
        isDeleted: phone.isActive ? 0 : 1,
      });

      caseIds.push(phone.caseId);

      if (isCustomPhoneType) {
        custom004PhonesToInsert.push({
          phoneId: Number(phone.existingPhoneId),
          canCall: phone.canCall,
          canSendSms: phone.canSendSms,
          predictiveOn: phone.predictiveOn,
        });
      }

      caseToPhones[String(phone.caseId)]
        ? caseToPhones[phone.caseId].push(Number(phone.existingPhoneId))
        : (caseToPhones[phone.caseId] = [Number(phone.existingPhoneId)]);
    }

    await this.phoneToCaseRepository.upsert(phoneToCaseData, {
      conflictPaths: ['phoneId', 'caseId'],
    });

    const casesContactPersons = await this.getContactPersonsForUpdate(
      caseIds,
      contactPersonId,
    );

    await this.addPhoneToConactPerson(casesContactPersons, caseToPhones);
    await this.custom004PhoneRepository.insert(custom004PhonesToInsert);
  }

  async createNewPhones(
    newPhones: IPhonesPreparedData[],
    caseIds: number[],
    contactPersonId: number[],
    userId: number,
  ) {
    if (newPhones.length === 0) {
      return;
    }

    const newPhonesIds = await this.getNewPhonesIds(newPhones.length);
    const phoneData: Phone[] = [];
    const phoneToCaseData: PhoneToCase[] = [];
    const caseToPhones: Record<string, number[]> = {};

    const custom004PhonesToInsert: ICustom004PhoneData[] = [];

    const debtorByCases = await this.getDebtorsByCases(caseIds);

    const isCustomPhoneType =
      await this.parameters.getGlobalParameterValueByName('customPhoneType');

    for (const phone of newPhones) {
      const phoneId = newPhonesIds.shift() as string;

      phoneData.push({
        id: phoneId,
        debtorId: debtorByCases[phone.caseId],
        phoneNumber: phone.phoneNumber,
        typeId: phone.phoneTypeId,
        partId: phone.partyTypeId,
        dataSourceId: phone.phoneSourceId,
        isDeleted: phone.isDeleted,
        isMobile: await this.isMobilePhone(phone.phoneNumber),
        phoneNote: phone.phoneNote,
        insertedUserId: userId,
        inProcessing: 1,
        deactivatedDate: phone.isActive ? undefined : new Date(),
      });

      phoneToCaseData.push({
        phoneId: phoneId,
        caseId: String(phone.caseId),
        deactivated: phone.isActive ? 0 : 1,
        insertedUserId: userId,
        isDeleted: phone.isActive ? 0 : 1,
      });

      if (isCustomPhoneType) {
        custom004PhonesToInsert.push({
          phoneId: Number(phoneId),
          canCall: phone.canCall,
          canSendSms: phone.canSendSms,
          predictiveOn: phone.predictiveOn,
        });
      }

      caseToPhones[String(phone.caseId)]
        ? caseToPhones[String(phone.caseId)].push(Number(phoneId))
        : (caseToPhones[String(phone.caseId)] = [Number(phoneId)]);
    }

    // console.log('phoneData', phoneData);
    // console.log('phoneToCaseData', phoneToCaseData);

    const casesContactPersons = await this.getContactPersonsForUpdate(
      caseIds,
      contactPersonId,
    );

    await this.phoneRepository.insert(phoneData);
    await this.phoneToCaseRepository.insert(phoneToCaseData);
    await this.custom004PhoneRepository.insert(custom004PhonesToInsert);

    await this.addPhoneToConactPerson(casesContactPersons, caseToPhones);
  }

  async updatePhones(preparedData: PreparedData, userId: number) {
    const { phonesToUpdate, caseIds, contactPersonId } = preparedData;
    // console.log('phonesToUpdate', phonesToUpdate);

    if (phonesToUpdate.length === 0) {
      return;
    }

    const phoneNumberQueryCases = [];
    const phoneTypeQueryCases = [];
    const partyTypeQueryCases = [];
    const isMobileQueryCases = [];
    const isDeletedQueryCases = [];

    const custom004PredicitveCases = [];
    const custom004CanCallCases = [];
    const custom004CanSendSmsCases = [];

    for (const phone of phonesToUpdate) {
      phoneNumberQueryCases.push(
        `WHEN "ID" = ${phone.phoneId} THEN '${phone.phoneNumber}'`,
      );
      phoneTypeQueryCases.push(
        `WHEN "ID" = ${phone.phoneId} THEN ${phone.phoneTypeId}`,
      );
      partyTypeQueryCases.push(
        `WHEN "ID" = ${phone.phoneId} THEN ${phone.partyTypeId}`,
      );
      isMobileQueryCases.push(
        `WHEN "ID" = ${phone.phoneId} THEN ${phone.isMobile}`,
      );
      isDeletedQueryCases.push(
        `WHEN "ID" = ${phone.phoneId} THEN ${phone.isDeleted ?? 0}`,
      );

      if (phone.predictiveOn !== undefined) {
        custom004PredicitveCases.push(
          `WHEN "PhoneID" = ${phone.phoneId} THEN '${phone.predictiveOn}'`,
        );
      }

      if (phone.canCall !== undefined) {
        custom004CanCallCases.push(
          `WHEN "PhoneID" = ${phone.phoneId} THEN '${phone.canCall}'`,
        );
      }

      if (phone.canSendSms !== undefined) {
        custom004CanSendSmsCases.push(
          `WHEN "PhoneID" = ${phone.phoneId} THEN '${phone.canSendSms}'`,
        );
      }
    }

    const phonesToUpdateIds = phonesToUpdate
      .map((phone) => phone.phoneId)
      .join(',');

    const updateQuery = `
        UPDATE "Data"."Phone"
        SET
            "UpdatedUserID" = ${userId},
            "PhoneNumber" = CASE ${phoneNumberQueryCases.join(
              '\n',
            )} ELSE "PhoneNumber"
            END,
            "TypeID" = CASE ${phoneTypeQueryCases.join('\n')} ELSE "TypeID"
            END,
            "PartID" = CASE ${partyTypeQueryCases.join('\n')} ELSE "PartID"
            END,
            "IsMobile" = CASE ${isMobileQueryCases.join('\n')} ELSE "IsMobile"
            END
            ${
              isDeletedQueryCases.length > 0
                ? `, "IsDeleted" = CASE ${isDeletedQueryCases.join(
                    '\n',
                  )} ELSE "IsDeleted" END`
                : ''
            }
           
        WHERE "ID" IN (${phonesToUpdateIds});
    `;

    await getManager().query(updateQuery);

    const customPhonesQueries = [];

    if (custom004CanCallCases.length > 0) {
      customPhonesQueries.push(
        `"CanCall" = CASE ${custom004CanCallCases.join(
          '\n',
        )} ELSE "CanCall" END`,
      );
    }

    if (custom004PredicitveCases.length > 0) {
      customPhonesQueries.push(
        `"PredictiveOn" = CASE ${custom004PredicitveCases.join(
          '\n',
        )} ELSE "PredictiveOn" END`,
      );
    }

    if (custom004CanSendSmsCases.length > 0) {
      customPhonesQueries.push(
        `"CanSendSms" = CASE ${custom004CanSendSmsCases.join(
          '\n',
        )} ELSE "CanSendSms" END`,
      );
    }

    console.log('customPhonesQueries', customPhonesQueries);

    if (customPhonesQueries.length > 0) {
      const updateCustom004PhonesQuery = `
        UPDATE "Data"."Custom004Phone"
        SET
            ${customPhonesQueries.join(',')}
        WHERE "PhoneID" IN (${phonesToUpdateIds});
      `;
      await getManager().query(updateCustom004PhonesQuery);
    }
  }

  async getNewPhonesIds(count: number): Promise<string[]> {
    const data: any[] = await getManager().query(
      `SELECT nextval('"Data"."Phone_ID_seq"'::regclass) FROM generate_series(1, ${count});`,
    );

    return data.map((item) => item.nextval);
  }

  async isMobilePhone(phoneNumber: string): Promise<number> {
    const KEY = 'mobile-phone-codes';

    if (await this.redis.exists(KEY)) {
      const phoneCodes = await this.redis.get(KEY);

      if (phoneCodes) {
        return phoneCodes.split(',').includes(phoneNumber.slice(0, 3)) ? 1 : 0;
      }
    }

    const phoneCodes = await this.phoneCodeRepository.getMobileCodes();
    const codes = phoneCodes.map((code) => code.name);

    await this.redis.set(KEY, codes.join(','), 'EX', 120);
    return codes.includes(phoneNumber.slice(0, 3)) ? 1 : 0;
  }

  async getDebtorsByCases(caseIDs: number[]) {
    const cases = await this.caseRepository.find({
      where: { id: In(caseIDs) },
    });

    return cases.reduce((accumulator, caseModel) => {
      accumulator[caseModel.id] = caseModel.debtorId;
      return accumulator;
    }, {} as Record<string, string>);
  }

  async getContactPersonsForUpdate(
    caseIDs: number[],
    contactPersonIDs: number[],
  ): Promise<ContactPersonResult[]> {
    let contactPersonsByCases = [];
    let contactPersons = [];

    if (contactPersonIDs.length > 0) {
      contactPersons = await getManager().query(
        `SELECT dcp."ID" as "ContactPersonID",  dc."ID" as "CaseID", dcp."Value" as "Value"
        FROM "Data"."Case" AS dc
                JOIN "Data"."ContactPerson" AS dcp ON CAST(dcp."Value" ->> 'DebtorID' AS BIGINT) = dc."DebtorID"
        WHERE dcp."IsDeleted" = 0
            AND dcp."ID" = ${contactPersonIDs.join(',')}
      `,
      );
    }

    if (caseIDs.length > 0) {
      contactPersonsByCases = await getManager().query(
        `SELECT  dcp."ID" as "ContactPersonID",  dc."ID" as "CaseID", dcp."Value" as "Value"
        FROM "Data"."Case" AS dc
                JOIN "Data"."ContactPerson" AS dcp ON CAST(dcp."Value" ->> 'DebtorID' AS BIGINT) = dc."DebtorID"
        WHERE dc."ID" in (${caseIDs.join(',')})
          AND dcp."IsDeleted" = 0
          AND dcp."IsDebtor" is true;
        ;
      `,
      );
    }

    return [...contactPersonsByCases, ...contactPersons];
  }

  async addPhoneToConactPerson(
    casesContactPersons: ContactPersonResult[],
    caseToPhones: Record<string, number[]>,
  ) {
    // console.log('casesContactPersons', casesContactPersons);
    // console.log('caseToPhone', caseToPhones);

    const quertCases = [];
    const contactPersonsIds = [];

    for (const contactPerson of casesContactPersons) {
      const updatedContactPersonValue = {
        ...contactPerson.Value,
        PhoneID: [
          ...(contactPerson.Value.PhoneID ?? []),
          ...caseToPhones[contactPerson.CaseID],
        ],
      };

      contactPersonsIds.push(contactPerson.ContactPersonID);

      quertCases.push(
        `WHEN dcp."ID" = ${
          contactPerson.ContactPersonID
        } THEN '${JSON.stringify(updatedContactPersonValue)}'::jsonb`,
      );
    }

    await getManager().query(
      `UPDATE "Data"."ContactPerson" AS dcp
        SET "Value" = CASE
            ${quertCases.join('\n')}
        END
        WHERE dcp."ID" in (${contactPersonsIds.join(',')})
      `,
    );
  }

  async getPhoneDebtorPairs(phoneCase: Record<string, string>) {
    const caseIds = Object.values(phoneCase);

    const cases = await this.caseRepository.find({
      where: { id: In(caseIds) },
    });

    const phoneDebotorPairs: Record<string, string> = {};

    const reduceCases = this.accumulateData(cases, 'id');

    for (const [phoneNumber, caseId] of Object.entries(phoneCase)) {
      const caseEntity = reduceCases[caseId];
      phoneDebotorPairs[phoneNumber] = caseEntity.debtorId;
    }

    return phoneDebotorPairs;
  }

  getBooleanVlue(value: string | null | undefined) {
    if (!value || value === '') {
      return;
    }

    return ['1', 'yes', 'true'].includes(value);
  }
}
