export enum Queues {
  MaskedPhone = 'import:masked-phone',
  UpdateImportProgress = 'ProgressEvents',
  ContactPerson = 'import:contact-person',
  LetterPrintHouseData = 'import:letter-print-house-data',
  CaseAttachment = 'import:case-attachment',
  Interaction = 'import:interaction',
  CaseCloning = 'import:case-cloning',
  ExtraInfo = 'import:extra-info',
  ContractData = 'import:contract-data',
  LegalCase = 'import:legal-case',
  LegalDocument = 'import:legal-document',
  UpdateLegalDocument = 'import:update-legal-document',
  LegalInvoice = 'import:legal-invoice',
  CourtParameter = 'import:court-parameter',
  LegalCourt = 'import:legal-court',
  LegalAppellateCourt = 'import:legal-appellate-court',
  BasisFina = 'import:basis-fina',
  BasisCpiio = 'import:basis-cpiio',
  BasisEmployer = 'import:basis-employer',
  BasisComplaint = 'import:basis-complaint',
  TransferToCollectionAgency = 'import:transfer-to-collection-agency',
  ActivityParameter = 'import:activity-parameter',
  Discount = 'import:discount',
  Tag = 'import:tag',
  CaseParameter = 'import:case-parameter',
  RecalculateFinancialData = 'import:recalculate-financial-data',
  Attachment = 'import:attachment',
  Phone = 'import:phone',
  DeleteLegalCase = 'import:delete-legal-case',
  DeleteInteraction = 'import:delete-interaction',
  CaseState = 'import:case-state',
  SmsActivity = 'import:sms-activity',
  CheckLegalTaskRules = 'check_legal_task_rules',
  CaseDiscount = 'import:case-discount',
}
