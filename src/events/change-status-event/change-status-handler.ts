import { Injectable } from '@nestjs/common';
import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel } from 'amqplib';

@Injectable()
export class ChangeStatusHandler {
  protected channel: ChannelWrapper;
  constructor(
    protected readonly connection: AmqpConnectionManager,
    private readonly systemUserId: number,
  ) {}

  async changeStatus(
    caseIds: number[],
    historyResultId: number | undefined,
    statusReasonId: number | null | undefined,
    toolSourceID: number,
  ): Promise<void> {
    if (caseIds.length > 0) {
      this.channel = this.connection.createChannel({
        setup: (channel: ConfirmChannel) =>
          Promise.all([
            channel.assertQueue('change_status', {
              durable: true,
            }),
          ]),
      });
      const message = JSON.stringify({
        CaseID: caseIds,
        HistoryResultID: historyResultId,
        StatusReasonID: statusReasonId,
        UserID: this.systemUserId,
        ToolSourceID: toolSourceID,
      });
      await this.channel.sendToQueue('change_status', Buffer.from(message), {
        persistent: true,
      });
    }
  }
}
