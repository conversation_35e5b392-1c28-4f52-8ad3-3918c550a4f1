import { Injectable } from '@nestjs/common';
import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel } from 'amqplib';

@Injectable()
export class ChangeStageHandler {
  protected channel: ChannelWrapper;
  constructor(
    protected readonly connection: AmqpConnectionManager,
    private readonly systemUserId: number,
  ) {}

  async changeStage(
    caseIds: number[],
    stageID: number | undefined,
  ): Promise<void> {
    if (caseIds.length > 0) {
      this.channel = this.connection.createChannel({
        setup: (channel: ConfirmChannel) =>
          Promise.all([
            channel.assertQueue('change_stage', {
              durable: true,
            }),
          ]),
      });
      const message = JSON.stringify({
        CaseID: caseIds,
        StageID: stageID,
        UserID: this.systemUserId,
      });
      await this.channel.sendToQueue('change_stage', Buffer.from(message), {
        persistent: true,
      });
    }
  }
}
