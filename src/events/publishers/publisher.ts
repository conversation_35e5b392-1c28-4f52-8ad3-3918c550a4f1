import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel } from 'amqplib';
import { BaseEvent } from 'src/events/types/base-event.interface';

export abstract class Publisher<T extends BaseEvent> {
  abstract queueName: T['queueName'];
  abstract durable: T['durable'];
  protected channel: ChannelWrapper;
  protected connection: AmqpConnectionManager;

  constructor(connection: AmqpConnectionManager) {
    this.connection = connection;
  }

  private async configureChanel() {
    this.channel = await this.connection.createChannel({
      setup: (channel: ConfirmChannel) =>
        Promise.all([
          channel.assertQueue(this.queueName, {
            durable: this.durable,
          }),
        ]),
    });
  }

  async publish(data: T['data']): Promise<boolean> {
    await this.configureChanel();
    const message = JSON.stringify(data);
    return await this.channel.sendToQueue(
      this.queueName,
      Buffer.from(message),
      {
        persistent: true,
      },
    );
  }
}
