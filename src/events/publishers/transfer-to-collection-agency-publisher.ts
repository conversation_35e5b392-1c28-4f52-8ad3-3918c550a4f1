import { ImportPublisher } from 'src/events/publishers/import-publisher';
import { Queues } from 'src/events/queues.enum';
import { TransferToCollectionAgencyEvent } from '../types/import-info-to-collection-agency-event';

export class TransferToCollectionAgencyPublisher extends ImportPublisher<TransferToCollectionAgencyEvent> {
  queueName: Queues.TransferToCollectionAgency =
    Queues.TransferToCollectionAgency;
}
