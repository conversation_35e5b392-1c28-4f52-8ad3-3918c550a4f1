import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';

import { BaseImportEvent } from '../types/base-import-event.interface';

export abstract class ImportPublisher<T extends BaseImportEvent> {
  abstract queueName: T['queueName'];
  protected channel: ChannelWrapper;
  protected connection: AmqpConnectionManager;

  constructor(connection: AmqpConnectionManager) {
    this.connection = connection;
    this.channel = this.connection.createChannel({
      json: true,
      setup: (channel: any) =>
        channel.assertQueue(this.queueName, {
          durable: true,
        }),
    });
  }

  async publish(data: T['data']): Promise<void> {
    await this.channel.sendToQueue(this.queueName, data, {
      persistent: true,
    });
  }

  async close() {
    if (this.channel) {
      await this.channel.close();
    }
  }
}
