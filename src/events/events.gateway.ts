import { Logger } from '@nestjs/common';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  // local testing
  cors: {
    origin: '*',
  },
})
// @WebSocketGateway()
export class EventsGateway {
  private readonly logger = new Logger(EventsGateway.name);

  @WebSocketServer()
  server: Server;
  handleConnection(client: Socket): any {
    client.on('import', (data) => {
      const channel = `import:${data.id}:${data.userID}`;
      const channelImportProgress = `import:${data.id}`;
      client.join(channel);
      client.join(channelImportProgress);
      // this.server.in(channel).emit('progress', 75);
    });

    client.on('recalculation', (data) => {
      if (!data.caseId) {
        this.logger.error('No case id in recalculation event');
      } else {
        const channel = `recalculation:${data.caseId}`;
        client.join(channel);
      }
    });
  }
  handleDisconnect(client: Socket) {
    client.leave(client.id);
  }

  closeChannel(channel: string) {
    this.server
      .in(channel)
      .clients((error: Error | null, clientIds: string[]) => {
        if (error) {
          console.error(`Error fetching clients in channel ${channel}:`, error);
          return;
        }

        for (const clientId of clientIds) {
          const client = (this.server.sockets.sockets as any).get(clientId);
          if (client) {
            client.disconnect(true);
          }
        }
        console.log(`Closed all connections in channel ${channel}`);
      });
  }
}
