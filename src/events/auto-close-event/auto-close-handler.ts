import { Injectable } from '@nestjs/common';
import { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { ConfirmChannel } from 'amqplib';
import { In } from 'typeorm';
import { TransactionRepository } from '../../repositories/financial/transaction.repository';

@Injectable()
export class AutoCloseHandler {
  protected channel: ChannelWrapper;
  private caseToHistoryResult: any[];

  constructor(
    protected readonly connection: AmqpConnectionManager,
    private readonly transactionRepository: TransactionRepository,
    private readonly systemUserId: number,
  ) {}

  async getAutoCloseConditionData(CaseID: number[]): Promise<void> {
    const cases = CaseID.join(', ');
    this.caseToHistoryResult = await this.transactionRepository.query(
      `select * from "Data"."GetCaseFromCloseCondition" (ARRAY [${cases}])`,
    );
  }

  async markLegalTransactions(): Promise<void> {
    if (this.caseToHistoryResult && this.caseToHistoryResult.length > 0) {
      const transactionIDs: number[] = [];
      for (const item of this.caseToHistoryResult) {
        if (item.TransactionIDs && item.TransactionIDs.length > 0) {
          transactionIDs.push(...JSON.parse(item.TransactionIDs));
        }
      }
      if (transactionIDs.length > 0) {
        const chunks = this.chunkArray(transactionIDs, 5000);
        for (const chunk of chunks) {
          await this.transactionRepository.update(
            { id: In(chunk) },
            { isDeleted: 11 },
          );
        }
      }
    }
  }

  async changeStatusByAutoCloseCondition(toolSourceID: number): Promise<void> {
    if (this.caseToHistoryResult && this.caseToHistoryResult.length > 0) {
      this.channel = this.connection.createChannel({
        setup: (channel: ConfirmChannel) =>
          Promise.all([
            channel.assertQueue('change_status', {
              durable: true,
            }),
          ]),
      });

      for (const caseItem of this.caseToHistoryResult) {
        const message = JSON.stringify({
          CaseID: [caseItem.CaseID],
          HistoryResultID: caseItem.HistoryResultID,
          StatusReasonID: caseItem.StatusReasonID ?? null,
          UserID: this.systemUserId,
          ToolSourceID: toolSourceID,
        });

        await this.channel.sendToQueue('change_status', Buffer.from(message), {
          persistent: true,
        });
      }
    }
  }

  private chunkArray(array: any[], size: number): any[][] {
    return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
      array.slice(index * size, index * size + size),
    );
  }
}
