import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class FinaDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  @IsNumber()
  public BasisForPaymentID: number;

  @Expose()
  @IsString()
  public DeliveryDate: string;

  @Expose()
  @IsString()
  public DeliveryStatus: string;

  @Expose()
  @IsNumber()
  public DeliveryStatusID: number;

  @Expose()
  @IsString()
  public ReturnedByFina: string | boolean | null;

  @Expose()
  @IsString()
  public ReturningReason: string;

  @Expose()
  @IsString()
  public IncludedInFinaPriority: string | boolean | null;

  @Expose()
  @IsString()
  public ProcessedByFina: string | boolean | null;

  @Expose()
  @IsString()
  public DateOfDisposal: string;

  @Expose()
  @IsString()
  public DateOfElimination: string;
}
