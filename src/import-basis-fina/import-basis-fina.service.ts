import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { Custom004EEnforcementRequestToFina } from '../entities/legal/custom004-enforcement-request-to-fina.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportBasisFinaPublisher } from '../events/publishers/import-basis-fina-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { CourtRepository } from '../repositories/legal-ua/court.repository';
import { Custom004EEnforcementRequestToFinaRepository } from '../repositories/legal/custom004-enforcement-request-to-fina.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { FinaDTO } from './dto/fina.dto';

const INSERTED_FINA_NUMBER = 'Number of inserted FINA';
const UPDATED_FINA_NUMBER = 'Number of updated FINA';

@Injectable()
export class ImportBasisFinaService extends BaseImportService<FinaDTO> {
  catalog: CatalogEnum = CatalogEnum.BasisFina;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.BasisFina;
  protected publisher: ImportPublisher<any> = new ImportBasisFinaPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly courtRepository: CourtRepository,
    protected readonly custom004EEnforcementRequestDeliveryStatusRepository: Custom004EEnforcementRequestDeliveryStatusRepository,
    protected readonly custom004EEnforcementRequestToFinaRepository: Custom004EEnforcementRequestToFinaRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertFina = result?.insertFina;
    const deactivateFinaByBasis = result?.deactivateFinaByBasis;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_FINA_NUMBER]: 0,
          [UPDATED_FINA_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_FINA_NUMBER]:
        this.resultDetalization[id][0][INSERTED_FINA_NUMBER],
      [UPDATED_FINA_NUMBER]:
        this.resultDetalization[id][0][UPDATED_FINA_NUMBER],
    };

    if (deactivateFinaByBasis.length > 0) {
      await queryRunner.manager.update(
        Custom004EEnforcementRequestToFina,
        { BasisID: In(deactivateFinaByBasis), IsDeleted: 0 },
        { IsProcessedByFina: false },
      );
      detalization[UPDATED_FINA_NUMBER] += deactivateFinaByBasis.length;
    }

    if (insertFina.length > 0) {
      await this.custom004EEnforcementRequestToFinaRepository.save(insertFina);
      detalization[INSERTED_FINA_NUMBER] += insertFina.length;
    }

    this.resultDetalization[id][0][INSERTED_FINA_NUMBER] =
      detalization[INSERTED_FINA_NUMBER];
    this.resultDetalization[id][0][UPDATED_FINA_NUMBER] =
      detalization[UPDATED_FINA_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    data.DeliveryStatus = (
      await this.custom004EEnforcementRequestDeliveryStatusRepository.find({
        where: {
          isDeleted: 0,
        },
      })
    ).map((item) => item.name);

    data.ReturnedByFina = ['Yes', 'No'];
    data.IncludedInFinaPriority = ['Yes', 'No'];
    data.ProcessedByFina = ['Yes', 'No'];

    return data;
  }
}
