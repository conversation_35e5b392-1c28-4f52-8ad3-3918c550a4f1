import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { FinaDTO } from './dto/fina.dto';
import { ImportBasisFinaService } from './import-basis-fina.service';
import { ImportFinaDTO } from './dto/import-fina.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-basis-fina')
export class ImportBasisFinaController {
  constructor(private importBasisFinaService: ImportBasisFinaService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importLegalCourtDTO: ImportFinaDTO) {
    const { importFile, userID } = importLegalCourtDTO;

    return this.importBasisFinaService.importLargeFile(
      importFile,
      userID,
      FinaDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream = await this.importBasisFinaService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportFina - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportFina - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importBasisFinaService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
