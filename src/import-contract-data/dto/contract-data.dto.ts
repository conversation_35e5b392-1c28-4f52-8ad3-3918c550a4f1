import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';

export class ContractDataDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  public DateOfContracting: string | null;

  @Expose()
  public ContractStage: string | null;

  @Expose()
  public DateOfCancellation: string | null;

  @Expose()
  public PeriodInNetwork: string | null;

  @Expose()
  public DisconnectionReason: string | null;

  @Expose()
  public PenaltyRate: number | null;

  @Expose()
  public AllClientPaymentAmount: number | null;

  @Expose()
  public LastClientPaymentDate: string | null;

  @Expose()
  public LastClientPaymentAmount: number | null;

  @Expose()
  public InitialDPD: number | null;

  @Expose()
  public ActualDPD: number | null;

  @Expose()
  public BillingDate: string | null;

  @Expose()
  public FirstDueBill: string | null;

  @Expose()
  public LastInvoiceDueDate: string | null;

  @Expose()
  public ProductType: string | null;

  @Expose()
  public ProductDescription: string | null;

  @Expose()
  public ProductExtraDescription: string | null;

  @Expose()
  public InitialDebtStructure: string | null;

  @Expose()
  public NumberOfDueInvoices: number | null;

  @Expose()
  public ValueOfFirstDueBill: number | null;

  @Expose()
  public CountOfPhones: number | null;

  @Expose()
  public EquipmentType: string | null;

  @Expose()
  public DebtorType: string | null;

  @Expose()
  public DebtorTypeID: number | null;

  @Expose()
  public CaseType: string | null;

  @Expose()
  public NumberOfAllInvoices: number | null;

  @Expose()
  public AdditionalClientIdentifier: string | null;

  @Expose()
  public InvoicePaymentCode: string | null;

  @Expose()
  public ClientSegmentRisk: string | null;

  @Expose()
  public FinancedPeriod: string | null;

  @Expose()
  public FinancedValue: number | null;

  @Expose()
  public ValueOfInstallment: string | null;

  @Expose()
  public Bucket: string | null;

  @Expose()
  public FinancingStore: string | null;

  @Expose()
  public NumberFirstOverdueInstallment: number | null;

  @Expose()
  public PaperFileAuthorizationNumber: string | null;

  @Expose()
  public TotalAmountToCloseLoan: string | null;

  @Expose()
  public PaymentMethods: string | null;

  @Expose()
  public DateOfReconnection: string | null;

  @Expose()
  public ElectronicInvoice: string | null;

  @Expose()
  public TerminalInstallments: string | null;

  @Expose()
  public OldestPhoneNo: string | null;

  @Expose()
  public OldestPhoneNoActivationDate: string | null;

  @Expose()
  public NewestPhoneNo: string | null;

  @Expose()
  public NewestPhoneNoActivationDate: string | null;

  @Expose()
  public AdditionalFinancialInfo: string | null;

  @Expose()
  public CancelationFee: string | null;

  @Expose()
  public PORTDetails: string | null;

  @Expose()
  public TotalRONEquivalent: string | null;

  @Expose()
  public DebtorIsAlive: string | null;

  @Expose()
  public OverdraftAvailableBalance: number | null;

  @Expose()
  public NextInstallmentDate: string | null;

  @Expose()
  public TotalFeesOnAllAccounts: string | null;

  @Expose()
  public ClientWash: string | null;

  @Expose()
  public Collateral: string | null;

  @Expose()
  public NewInvoiceInformation: string | null;

  @Expose()
  public Occupation: string | null;

  @Expose()
  public CaseGroupingIdentifier: string | null;

  @Expose()
  public CaseGroupingRule: string | null;

  @Expose()
  public AddClosedCasesToGrouping: boolean | null;

  @Expose()
  public AegrmRnpmRegistrationCode: string | null;

  @Expose()
  public ProjectIBAN: string | null;

  @Expose()
  public CaseHasGDPRNotice: boolean | null;

  @Expose()
  public LastGDPRNoticeDate: string | null;

  @Expose()
  public LastGDPRNoticeSentUsing: string | null;

  @Expose()
  public MaturityDate: string | null;

  @Expose()
  public TotalNumberOfContractsLoans: number | null;

  @Expose()
  public InterestRate: number | null;

  @Expose()
  public OpenWithBank: string | null;

  @Expose()
  public Beneficiary: string | null;

  @Expose()
  public PaperFileBatch: string | null;

  @Expose()
  public PaperFileBatchDate: string | null;

  @Expose()
  public PaperFileStatus: string | null;

  @Expose()
  public PaperFileProblems: string | null;

  @Expose()
  public ContractLocation: string | null;

  @Expose()
  public LegalSelectionDate: string | null;

  @Expose()
  public ExclusionReason: string | null;

  @Expose()
  public BackToSoftCollectionDate: string | null;
}
