import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { AutoCloseHandler } from '../events/auto-close-event/auto-close-handler';
import { EventsGateway } from '../events/events.gateway';
import { TemporaryCustom005CaseAdditionalInfo } from '../events/listeners/contract-data-listener/temporary-tables/temporary-custom005-case-additional-info';
import { TemporaryDebtor } from '../events/listeners/contract-data-listener/temporary-tables/temporary-debtor';
import { ImportContractDataPublisher } from '../events/publishers/import-contract-data-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ContractDataDTO } from './dto/contract-data.dto';

const INSERTED_NUMBER = 'Number of inserted variables';
const UPDATED_NUMBER = 'Number of updated variables';
const UPDATED_DEBTOR_TYPES = 'Number of updated debtor types';

@Injectable()
export class ImportContractDataService extends BaseImportService<ContractDataDTO> {
  catalog: CatalogEnum = CatalogEnum.ContractData;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.ContractData;
  protected publisher: ImportPublisher<any> = new ImportContractDataPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly custom005CaseAdditionalInfoRepository: Custom005CaseAdditionalInfoRepository,
    protected readonly transactionRepository: TransactionRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertItems = result?.insertItems;
    const updateItems = result?.updateItems;
    const updateDebtors = result?.updateDebtors;

    const allCases = [
      ...insertItems.map((item: any) => item.CaseID),
      ...updateItems.map((item: any) => item.CaseID),
    ];

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_NUMBER]: 0,
          [UPDATED_NUMBER]: 0,
          [UPDATED_DEBTOR_TYPES]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_NUMBER]: this.resultDetalization[id][0][INSERTED_NUMBER],
      [UPDATED_NUMBER]: this.resultDetalization[id][0][UPDATED_NUMBER],
      [UPDATED_DEBTOR_TYPES]:
        this.resultDetalization[id][0][UPDATED_DEBTOR_TYPES],
    };

    if (insertItems.length > 0) {
      const models = insertItems.map((item: any) =>
        this.custom005CaseAdditionalInfoRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (updateItems.length > 0) {
      const temporaryAdditionalNote = new TemporaryCustom005CaseAdditionalInfo(
        queryRunner.manager,
        this.config,
        Object.keys(updateItems[0]),
      );
      await temporaryAdditionalNote.manualInitialize();

      const chunksTemporary = this.sliceIntoChunks(updateItems, 500);
      for (const chunk of chunksTemporary) {
        await temporaryAdditionalNote.insert(chunk);
        detalization[UPDATED_NUMBER] += chunk.length;
      }
    }

    if (updateDebtors.length > 0) {
      const temporaryDebtors = new TemporaryDebtor(
        queryRunner.manager,
        this.config,
      );
      await temporaryDebtors.initialize();
      await temporaryDebtors.insert(updateDebtors);
      detalization[UPDATED_DEBTOR_TYPES] += updateDebtors.length;
    }

    await this.autoCloseEvent(allCases);

    this.resultDetalization[id][0][INSERTED_NUMBER] =
      detalization[INSERTED_NUMBER];
    this.resultDetalization[id][0][UPDATED_NUMBER] =
      detalization[UPDATED_NUMBER];
    this.resultDetalization[id][0][UPDATED_DEBTOR_TYPES] =
      detalization[UPDATED_DEBTOR_TYPES];
  }

  async autoCloseEvent(caseIDs: number[]): Promise<void> {
    if (caseIDs.length > 0) {
      const systemUserId =
        await this.serviceParameterRepository.getGlobalParameterValueByName<number>(
          'SystemUserID',
        );
      const autoCloseHandler = new AutoCloseHandler(
        this.connection,
        this.transactionRepository,
        systemUserId ?? 1,
      );
      await autoCloseHandler.getAutoCloseConditionData(caseIDs);
      await autoCloseHandler.markLegalTransactions();
      await autoCloseHandler.changeStatusByAutoCloseCondition(1);
    }
  }

  protected getPageSize(): number {
    return 10_000;
  }
}
