import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ContractDataDTO } from './dto/contract-data.dto';
import { ImportContractDataService } from './import-contract-data.service';
import { ImportContractDataDTO } from './dto/import-contract-data.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-contract-data')
export class ImportContractDataController {
  constructor(private importContractDataService: ImportContractDataService) {}

  @Post()
  @CsvImport('file')
  import(@Body() importExtraInfoDto: ImportContractDataDTO) {
    const { importFile, userID } = importExtraInfoDto;
    return this.importContractDataService.importLargeFile(
      importFile,
      userID,
      ContractDataDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importContractDataService.getGeneratedCsv(
      query.id,
    );

    response.set(
      disposition,
      'attachment; filename=ImportContractData - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importContractDataService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
