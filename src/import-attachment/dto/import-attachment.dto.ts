import { Expose, Type } from 'class-transformer';
import { IsNumber, IsOptional } from 'class-validator';

export class ImportAttachmentDTO {
  @Expose()
  @Type(() => Number)
  @IsNumber()
  TypeID: number;

  @Expose()
  @Type(() => Number)
  @IsNumber()
  LevelID: number;

  @Expose()
  @IsOptional()
  DateOfDocument: string;

  @Expose()
  @IsOptional()
  DueDate: string;

  @Expose()
  @IsOptional()
  Note: string;
}
