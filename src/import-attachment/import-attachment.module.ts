import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { Custom005Document } from '../entities/data/custom005-document.entity';
import { EventsGateway } from '../events/events.gateway';
import { ArchiveService } from '../import-case-attachment/archive.service';
import { AdditionalNoteRepository } from '../repositories/data/additional-note.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { DocumentAdditionalInfoRepository } from '../repositories/data/document-additional-info.repository';
import { DocumentNoteRepository } from '../repositories/data/document-note.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportAttachmentController } from './import-attachment.controller';
import { ImportAttachmentService } from './import-attachment.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      AdditionalNoteRepository,
      DocumentRepository,
      DocumentAdditionalInfoRepository,
      DocumentNoteRepository,
      Custom005Document,
      CaseRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportAttachmentController],
  providers: [
    ImportAttachmentService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
    ArchiveService,
  ],
})
export class ImportAttachmentModule {}
