import {
  HttpService,
  Inject,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';

import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import path from 'path';
import { ApiClient } from 'src/common/api.client';
import * as fs from 'fs';
import FormData from 'form-data';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import stream from 'stream';
import { checkIfExists } from '../common/helpers/check-if-exists';
// eslint-disable-next-line unicorn/prevent-abbreviations
import { rmDir } from '../common/helpers/rm-dir';
import { splitOnChunks } from '../common/helpers/split-on-chunks';
import { CsvParserInterceptor } from '../common/interceptors/csv-parser.interceptor';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportAttachmentPublisher } from '../events/publishers/import-attachment-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { ArchiveService } from '../import-case-attachment/archive.service';
import { Import } from '../import/import.service';
import { UploadStatus } from '../import/upload-status.enum';
import { AdditionalNoteRepository } from '../repositories/data/additional-note.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { AttachmentDTO } from './dto/attachment.dto';
import { ImportAttachmentDTO } from './dto/import-attachment.dto';
import { In } from 'typeorm';

@Injectable()
export class ImportAttachmentService extends Import<ImportAttachmentDTO> {
  public catalog: CatalogEnum = CatalogEnum.Attachment;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.Attachment;
  protected publisher: ImportPublisher<any> = new ImportAttachmentPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly additionalNoteRepository: AdditionalNoteRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly documentRepository: DocumentRepository,
    protected readonly archiveService: ArchiveService,
  ) {
    super();
  }

  preValidate = undefined;

  public async upload(
    search: Express.Multer.File,
    archive: Express.Multer.File,
    importAttachmentDTO: ImportAttachmentDTO,
    userId: number,
  ) {
    if (!this.archiveService.isCSV(search)) {
      for (const file of [search.path, archive.path]) {
        if (await checkIfExists(file)) {
          await rmDir(file, {
            recursive: true,
          });
        }
      }
      throw new UnprocessableEntityException({ search: ['No cases found'] });
    }

    const searchBuffer = fs.readFileSync(search.path);
    const parser = new CsvParserInterceptor(AttachmentDTO);
    const searchData: AttachmentDTO[] = await parser.parseCsv(searchBuffer);

    const isSingleFile = !this.archiveService.isArchive(archive);

    const { files, path } = await this.getFilesToUpload(archive, userId);

    if (searchData.length === 0) {
      for (const file of [search.path, archive.path]) {
        if (await checkIfExists(file)) {
          await rmDir(file, {
            recursive: true,
          });
        }
      }
      throw new UnprocessableEntityException({ search: ['No cases found'] });
    }

    if (files.length === 0) {
      for (const file of [search.path, archive.path]) {
        if (await checkIfExists(file)) {
          await rmDir(file, {
            recursive: true,
          });
        }
      }
      throw new UnprocessableEntityException({ files: ['No files found'] });
    }

    const validationErrors = await this.validateSearch(searchData);
    if (validationErrors.length > 0) {
      throw new UnprocessableEntityException({
        success: false,
        errors: validationErrors,
      });
    }

    if (importAttachmentDTO.LevelID !== 4) {
      for (const file of [search.path, archive.path]) {
        if (await checkIfExists(file)) {
          await rmDir(file, {
            recursive: true,
          });
        }
      }
      throw new UnprocessableEntityException({
        LevelID: ['Only case level available'],
      });
    }

    const uploadHistory = await this.uploadHistoryRepository.save({
      importListId: this.catalog,
      insertedUserId: userId,
      statusId: UploadStatus.New,
    });

    const firstRecord = searchData.pop();
    if (firstRecord) {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.InProgress },
      );
      uploadHistory.statusId = UploadStatus.InProgress;

      const result = await this.saveFiles(
        files,
        path,
        firstRecord.CaseID,
        importAttachmentDTO,
        isSingleFile,
        archive.originalname,
      );

      if (result) {
        const FileIDs = await this.getFileIDs(result);
        const chunkPercent = (1 / Math.ceil(searchData.length)) * 100;

        if (searchData.length === 0) {
          await this.uploadHistoryRepository.update(
            { id: uploadHistory.id },
            { statusId: UploadStatus.Finished },
          );
          uploadHistory.statusId = UploadStatus.Finished;
        } else {
          const publisher = new ImportAttachmentPublisher(this.connection);
          for (const [index, record] of searchData.entries()) {
            const isLast = index + 1 === searchData.length;

            await publisher.publish({
              userId,
              uploadHistoryId: uploadHistory.id,
              importData: [record],
              chunk: index + 1,
              additionalParams: {
                chunkPercent,
                isLastChunk: isLast,
                importAttachmentDTO: importAttachmentDTO,
                files: FileIDs,
                removeAfterProcessing: [search.path, archive.path],
              },
            });
          }
          await publisher.close();
        }
      } else {
        await this.uploadHistoryRepository.update(
          { id: uploadHistory.id },
          { statusId: UploadStatus.Failed },
        );
        uploadHistory.statusId = UploadStatus.Failed;
      }
    }

    return {
      success: true,
      id: uploadHistory.id,
    };
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public async getGeneratedCsvFields(): Promise<stream.PassThrough> {
    let csvBody = '';
    csvBody = ['CaseID'].join(';');

    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }

  protected async getFilesToUpload(
    attachments: Express.Multer.File,
    userId: number,
  ) {
    return this.archiveService.isArchive(attachments)
      ? await this.archiveService.unpack(attachments, userId)
      : { files: [attachments.path], path: '' };
  }

  public async publish(): Promise<void> {
    //
  }

  protected async validateSearch(data: AttachmentDTO[]) {
    const errors = [];
    const caseIDs = data.map((r) => Number(r.CaseID));
    const chunkSize = 1000;
    const chunks = splitOnChunks(caseIDs, chunkSize);

    for (const [index1, chunk] of chunks.entries()) {
      const existsCases = await this.caseRepository.find({
        where: {
          id: In(chunk),
        },
      });

      const map = new Set(existsCases.map((caseModel) => Number(caseModel.id)));
      for (const [index2, caseID] of chunk.entries()) {
        if (!map.has(caseID)) {
          errors.push({
            column: 'CaseID',
            errorCode: 'CaseNotExistsError',
            row:
              chunkSize > 0
                ? index1 * chunkSize + index2
                : index1 * chunkSize + index2 + 2,
          });
        }
      }
    }

    if (errors.length === 0) {
      const duplicates: any = {};
      for (const [index, row] of data.entries()) {
        if (duplicates[row.CaseID] ?? false) {
          errors.push({
            column: 'CaseID',
            errorCode: 'CaseDuplicateError',
            row: index + 2,
          });
        } else {
          duplicates[row.CaseID] = true;
        }
      }
    }

    return errors;
  }

  protected async saveFiles(
    files: any[],
    storagePath: string,
    CaseID: number,
    params: any,
    isSingleFile: boolean,
    singleFilename: string,
  ): Promise<any[] | boolean> {
    const result: any[] = [];
    const caseServiceURL = this.config.get<string>('caseService.url');

    for (const file of files) {
      const formData = new FormData();
      formData.append('userID', String(params.userID));

      try {
        const filepath = path.join(process.cwd(), storagePath + '/' + file);

        if (!(await checkIfExists(filepath))) {
          console.log('file does not exist', filepath);
          return false;
        }

        formData.append('file', fs.createReadStream(filepath));
        formData.append('CaseID', String(CaseID));

        if (isSingleFile) {
          formData.append('FileName', singleFilename);
        } else {
          formData.append('FileName', file);
        }

        for (const key in params) {
          if (params[key]) {
            formData.append(key, params[key]);
          }
        }

        const response = await this.httpService
          .post(caseServiceURL + '/case-document', formData, {
            headers: { ...formData.getHeaders() },
            maxContentLength: 2048 * 1024 * 1024,
            maxBodyLength: 2048 * 1024 * 1024,
            timeout: 1_000_000,
          })
          .toPromise();

        result.push(response.data);
        await rmDir(filepath);
      } catch (error) {
        console.log('error', error);
      }
    }

    return result;
  }

  private async getFileIDs(result: any[] | boolean): Promise<number[]> {
    if (Array.isArray(result)) {
      const documentIDs = result.map((r) => r.ID);
      const documents = await this.documentRepository.find({
        where: {
          id: In(documentIDs),
        },
      });

      return documents.map((document) => Number(document.fileId));
    }

    return [];
  }
}
