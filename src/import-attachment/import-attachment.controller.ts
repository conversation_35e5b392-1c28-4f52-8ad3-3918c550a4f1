import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

import { UserID } from '../common/decorators/user-id.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportAttachmentService } from './import-attachment.service';
import { ImportAttachmentDTO } from './dto/import-attachment.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-attachment')
export class ImportAttachmentController {
  constructor(private importAttachmentService: ImportAttachmentService) {}

  @Post()
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: 'archive', maxCount: 1 },
        { name: 'search', maxCount: 1 },
      ],
      {
        storage: diskStorage({
          destination: './extracted',
          filename: (request, file, callback) => {
            const uniqueSuffix = `${Date.now()}-${Math.round(
              Math.random() * 1e9,
            )}`;
            const fileName = `${file.fieldname}-${uniqueSuffix}-${file.originalname}`;
            callback(null, fileName);
          },
        }),
      },
    ),
  )
  import(
    @UploadedFiles()
    files: {
      archive: Express.Multer.File[];
      search: Express.Multer.File[];
    },
    @Body() importAttachmentDTO: ImportAttachmentDTO,
    @UserID() userId: number,
  ) {
    const archive = files.archive[0];
    const search = files.search[0];

    return this.importAttachmentService.upload(
      search,
      archive,
      importAttachmentDTO,
      userId,
    );
  }
  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; extraFields: string[] },
  ): Promise<any> {
    const readStream =
      await this.importAttachmentService.getGeneratedCsvFields();

    response.set(
      disposition,
      'attachment; filename=ImportAttachment - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importAttachmentService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
