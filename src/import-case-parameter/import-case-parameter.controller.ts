import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { CaseParameterDTO } from './dto/case-parameter.dto';
import { ImportCaseParameterService } from './import-case-parameter.service';
import { ImportCaseParameterDTO } from './dto/import-case-parameter.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-case-parameter')
export class ImportCaseParameterController {
  constructor(private importCaseParameterService: ImportCaseParameterService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importCourtParameterDTO: ImportCaseParameterDTO) {
    const { importFile, userID } = importCourtParameterDTO;

    const validationResult =
      await this.importCaseParameterService.checkFileTemplate(importFile);

    if (!validationResult.success) {
      return validationResult;
    }

    return this.importCaseParameterService.importLargeFile(
      importFile,
      userID,
      CaseParameterDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStreamOrPath =
      await this.importCaseParameterService.getGeneratedCsvFields(
        query.id,
        query.parameters,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportCaseParameter - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      if (typeof readStreamOrPath === 'string') {
        const readStream = fs.createReadStream(readStreamOrPath);
        readStream.pipe(response);

        readStream.on('end', () => {
          fs.unlink(readStreamOrPath, (error) => {
            if (error) {
              console.error('Failed to delete temp file:', error);
            }
          });
        });
      } else {
        readStreamOrPath.pipe(response);
      }
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportCaseParameter - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');

      if (typeof readStreamOrPath !== 'string') {
        readStreamOrPath.pipe(response);
      }
    }
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importCaseParameterService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
