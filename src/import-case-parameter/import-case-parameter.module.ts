import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseParameterRepository } from '../repositories/data/case-parameter.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportCaseParameterController } from './import-case-parameter.controller';
import { ImportCaseParameterService } from './import-case-parameter.service';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      CaseRepository,
      ParameterRepository,
      CaseParameterRepository,
      CourtProcessRepository,
      DataTypeRepository,
      InvoiceRepository,
      EventRuleLogRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportCaseParameterController],
  providers: [
    ImportCaseParameterService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportCaseParameterModule {}
