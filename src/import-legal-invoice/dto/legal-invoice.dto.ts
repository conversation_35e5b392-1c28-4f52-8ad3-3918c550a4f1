import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';

export class LegalInvoiceDto {
  @Expose()
  @IsNumber({}, { message: 'InvoiceID must be a number' })
  public InvoiceID: number;

  @Expose()
  @IsNumber({}, { message: 'CaseID must be a number' })
  public CaseID: number;

  @Expose()
  @IsNumber({}, { message: 'LegalCaseID must be a number' })
  public LegalCaseID: number;
}
