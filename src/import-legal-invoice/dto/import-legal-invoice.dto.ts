import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';

import { LegalInvoiceDto } from './legal-invoice.dto';

function transformNumberString(value: any): any {
  if (IsNumberString(value)) {
    return Number(value);
  }
  return value;
}

export class ImportLegalInvoiceDto {
  @Transform(({ value }) => transformNumberString(value))
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: LegalInvoiceDto[];
}
