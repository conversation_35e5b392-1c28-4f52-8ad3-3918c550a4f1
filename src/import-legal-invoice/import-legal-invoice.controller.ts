import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportLegalInvoiceService } from './import-legal-invoice.service';
import { LegalInvoiceDto } from './dto/legal-invoice.dto';
import { ImportLegalInvoiceDto } from './dto/import-legal-invoice.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-legal-invoice')
export class ImportLegalInvoiceController {
  constructor(private importLegalInvoiceService: ImportLegalInvoiceService) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importLegalInvoiceService.getGeneratedCsv(
      query.id,
    );

    response.set(
      disposition,
      'attachment; filename=ImportLegalInvoice - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file', LegalInvoiceDto)
  import(@Body() importLegalInvoiceDto: ImportLegalInvoiceDto) {
    const { importData, userID } = importLegalInvoiceDto;
    return this.importLegalInvoiceService.import(importData, userID);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importLegalInvoiceService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
