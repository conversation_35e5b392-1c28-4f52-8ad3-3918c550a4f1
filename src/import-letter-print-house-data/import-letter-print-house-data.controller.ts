import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { Response } from 'express';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { ImportLetterPrintHouseDataDTO } from './dto/import-letter-print-house-data.dto';
import { LetterPrintHouseDataDTO } from './dto/letter-print-house-data.dto';
import { ImportLetterPrintHouseDataService } from './import-letter-print-house-data.service';

@Controller('admin/import-letter-print-house-data')
export class ImportLetterPrintHouseDataController {
  constructor(
    private importLetterPrintHouseDataService: ImportLetterPrintHouseDataService,
  ) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream =
      await this.importLetterPrintHouseDataService.getGeneratedCsv(query.id);

    if (query.id === 'dictionary') {
      response.set(
        'Content-disposition',
        'attachment; filename=ImportLetterPrintHouseData - ' +
          query.id +
          '.xlsx',
      );
      response.set(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        'Content-disposition',
        'attachment; filename=ImportLetterPrintHouseData - ' +
          query.id +
          '.csv',
      );
      response.set('Content-Type', 'text/plain');
    }

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file', LetterPrintHouseDataDTO)
  import(@Body() importLetterPrintHouseDataDto: ImportLetterPrintHouseDataDTO) {
    const { importData, userID } = importLetterPrintHouseDataDto;
    return this.importLetterPrintHouseDataService.import(importData, userID);
  }
}
