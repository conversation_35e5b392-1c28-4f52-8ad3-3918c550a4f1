import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';

import { LetterPrintHouseDataDTO } from './letter-print-house-data.dto';

export class ImportLetterPrintHouseDataDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: LetterPrintHouseDataDTO[];
}
