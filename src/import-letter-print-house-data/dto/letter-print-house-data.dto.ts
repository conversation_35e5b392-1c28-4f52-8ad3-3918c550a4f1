import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';
export class LetterPrintHouseDataDTO {
  @Expose()
  public LETTER_QUEUE_ID: string;

  @Expose()
  @IsNumber()
  public DENUMB: number;

  @Expose()
  public DENAME: string;

  @Expose()
  public data_receptie: string;

  @Expose()
  public template: string | null;

  @Expose()
  public comanda: string;

  @Expose()
  public curier: string | null;

  @Expose()
  public ordine: string | null;

  @Expose()
  public awb_lung: string;

  @Expose()
  public ['Data Posta']: string;
}
