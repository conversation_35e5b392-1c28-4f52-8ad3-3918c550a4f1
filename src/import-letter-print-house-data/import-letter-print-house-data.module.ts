import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportLetterPrintHouseDataController } from './import-letter-print-house-data.controller';
import { ImportLetterPrintHouseDataService } from './import-letter-print-house-data.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
    ]),
  ],
  controllers: [ImportLetterPrintHouseDataController],
  providers: [
    ImportLetterPrintHouseDataService,
    ValidatorDataAccess,
    ApiClient,
  ],
})
export class ImportLetterPrintHouseDataModule {}
