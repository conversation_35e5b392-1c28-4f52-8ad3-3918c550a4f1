import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../repositories/data/custom001-contact-person.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { DebtorTypeRepository } from '../repositories/debtor-type.repository';
import { DataSourceRepository } from '../repositories/dictionary/data-source.repository';
import { SexTypeRepository } from '../repositories/dictionary/sex-type.repository';
import { PartyTypeRepository } from '../repositories/party-type.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportContactPersonController } from './import-contact-person.controller';
import { ImportContactPersonService } from './import-contact-person.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      PartyTypeRepository,
      DebtorTypeRepository,
      UploadHistoryRepository,
      ContactPersonRepository,
      DebtorRepository,
      Custom001ContactPersonRepository,
      SexTypeRepository,
      DataSourceRepository,
    ]),
  ],
  controllers: [ImportContactPersonController],
  providers: [ImportContactPersonService, ValidatorDataAccess, ApiClient],
})
export class ImportContactPersonModule {}
