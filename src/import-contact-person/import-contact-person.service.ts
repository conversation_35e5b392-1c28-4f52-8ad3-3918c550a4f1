import { Inject, Injectable } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { Import } from 'src/import/import.service';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { ImportContactPersonPublisher } from '../events/publishers/import-contact-person-publisher';
import { DebtorTypeRepository } from '../repositories/debtor-type.repository';
import { DataSourceRepository } from '../repositories/dictionary/data-source.repository';
import { SexTypeRepository } from '../repositories/dictionary/sex-type.repository';
import { PartyTypeRepository } from '../repositories/party-type.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ContactPersonDTO } from './dto/contact-person.dto';

@Injectable()
export class ImportContactPersonService extends Import<ContactPersonDTO> {
  catalog: CatalogEnum = CatalogEnum.ContactPerson;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly partyTypeRepository: PartyTypeRepository,
    protected readonly debtorTypeRepository: DebtorTypeRepository,
    protected readonly sexTypeRepository: SexTypeRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly dataSourceRepository: DataSourceRepository,
  ) {
    super();
  }

  preValidate = undefined;

  public async publish(
    importData: ContactPersonDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<void> {
    new ImportContactPersonPublisher(this.connection).publish({
      importData,
      userId,
      uploadHistoryId,
    });
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    data.ContactPersonType = (
      await this.partyTypeRepository.getList('contact-person')
    ).map((item) => item.name);

    data.ContactPersonDebtorType = (
      await this.debtorTypeRepository.getList()
    ).map((item) => item.name);

    data.ContactPersonDataSource = (
      await this.dataSourceRepository.getContactPersonList()
    ).map((item: any) => item.name);

    data.Gender = (await this.sexTypeRepository.getList()).map(
      (item) => item.name,
    );

    return data;
  }
}
