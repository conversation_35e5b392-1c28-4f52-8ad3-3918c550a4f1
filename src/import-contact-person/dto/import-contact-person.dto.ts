import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';

import { ContactPersonDTO } from './contact-person.dto';

export class ImportContactPersonDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: ContactPersonDTO[];
}
