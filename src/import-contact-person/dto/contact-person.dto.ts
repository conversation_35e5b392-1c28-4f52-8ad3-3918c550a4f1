import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';
export class ContactPersonDTO {
  @Expose()
  @IsNumber()
  public ContactPersonID: number;

  @Expose()
  public IsDebtor: number | null;

  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  public ContactPersonType: string;

  @Expose()
  public ContactPersonDebtorType: string;

  @Expose()
  public ContactPersonFirstName: string;

  @Expose()
  public ContactPersonLastName: string;

  @Expose()
  public ContactPersonMiddleName: string;

  @Expose()
  public ContactPersonPassportSeries: string;

  @Expose()
  public ContactPersonPassportNumber: string;

  @Expose()
  public ContactPersonPassportInfo: string;

  @Expose()
  public ContactPersonOIB: string;

  @Expose()
  public SSN: string;

  @Expose()
  public ['PIN/CNP']: string;

  @Expose()
  public Gender: string;

  @Expose()
  public ContactPersonBirthDate: string;

  @Expose()
  public IsDied: number | null;

  @Expose()
  public DeathDate: string | null;

  @Expose()
  public IsDeleted: number | null;

  @Expose()
  public ContactPersonDataSource: string | null;
}
