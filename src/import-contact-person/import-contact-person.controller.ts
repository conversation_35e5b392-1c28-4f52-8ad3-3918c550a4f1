import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { Response } from 'express';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { ImportContactPersonService } from './import-contact-person.service';
import { ContactPersonDTO } from './dto/contact-person.dto';
import { ImportContactPersonDTO } from './dto/import-contact-person.dto';

@Controller('admin/import-contact-person')
export class ImportContactPersonController {
  constructor(private importContactPersonService: ImportContactPersonService) {}

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importContactPersonService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        'Content-disposition',
        'attachment; filename=ImportContactPerson - ' + query.id + '.xlsx',
      );
      response.set(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        'Content-disposition',
        'attachment; filename=ImportContactPerson - ' + query.id + '.csv',
      );
      response.set('Content-Type', 'text/plain');
    }

    readStream.pipe(response);
  }

  @Post()
  @CsvImport('file', ContactPersonDTO)
  import(@Body() importContactPersonDTO: ImportContactPersonDTO) {
    const { importData, userID } = importContactPersonDTO;
    return this.importContactPersonService.import(importData, userID);
  }
}
