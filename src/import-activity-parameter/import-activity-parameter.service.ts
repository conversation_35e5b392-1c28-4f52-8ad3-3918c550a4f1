import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import fs from 'fs';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import readline from 'readline';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import stream from 'stream';
import { In, QueryRunner } from 'typeorm';
import { HttpMethod } from '../common/enums/http-method.enum';
import { ToolSourceEnum } from '../common/enums/tool-source.enum';
import { BaseImportWithParameterService } from '../common/imports/base-import-with-parameter.service';
import { REDIS } from '../common/redis/constants';
import { ValidationResult } from '../common/validation/validation-result.type';
import { CaseChangeLog } from '../entities/activity/case-change-log.entity';
import { HistoryParameter } from '../entities/activity/history-parameter.entity';
import { History } from '../entities/activity/history.entity';
import { Case } from '../entities/data/case.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportActivityParameterPublisher } from '../events/publishers/import-activity-parameter-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseChangeLogRepository } from '../repositories/activity/case-change-log.repository';
import { HistoryParameterRepository } from '../repositories/activity/history-parameter.repository';
import { HistoryRepository } from '../repositories/activity/history.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { HistoryTypeToParameterRepository } from '../repositories/dictionary/history-type-to-parameter.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ActivityParameterDTO } from './dto/activity-parameter.dto';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

const INSERTED_ACTIVITY_NUMBER = 'Number of inserted activities';
const INSERTED_PARAMETER_NUMBER = 'Number of inserted parameters';

@Injectable()
export class ImportActivityParameterService extends BaseImportWithParameterService<ActivityParameterDTO> {
  catalog: CatalogEnum = CatalogEnum.ActivityParameter;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.ActivityParameter;
  protected publisher: ImportPublisher<any> =
    new ImportActivityParameterPublisher(this.connection);

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly historyParameterRepository: HistoryParameterRepository,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly historyRepository: HistoryRepository,
    protected readonly historyTypeToParameterRepository: HistoryTypeToParameterRepository,
    protected readonly caseChangeLogRepository: CaseChangeLogRepository,
    protected readonly dataTypeRepository: DataTypeRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly eventRuleLogRepository: EventRuleLogRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
      invoiceRepository,
      parameterRepository,
      dataTypeRepository,
      serviceParameterRepository,
      eventRuleLogRepository,
      caseRepository,
    );
  }

  preValidate = undefined;

  protected getValidateBySlugKey(): string {
    return 'HistoryTypeID';
  }

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertHistories = result?.histories;
    const casesToActivateLegalRules = result?.casesToActivateLegalRules;
    const insertActivityParameters = result?.activityParameters;
    const historyToCaseID = result?.historyToCaseID;
    const metaData = result?.metaData;

    const changeSettings = metaData.changeSettings;

    this.saveAppellateCourt(id, result);

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_ACTIVITY_NUMBER]: 0,
          [INSERTED_PARAMETER_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_ACTIVITY_NUMBER]:
        this.resultDetalization[id][0][INSERTED_ACTIVITY_NUMBER],
      [INSERTED_PARAMETER_NUMBER]:
        this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER],
    };

    if (insertHistories.length > 0) {
      const histories = insertHistories.map((item: any) => {
        return this.historyRepository.create(item);
      });

      await queryRunner.manager.save(histories);
      this.saveHistoryIDs(
        id,
        histories.map((item: any) => item.id),
      );
      this.postActionData[id].userId = metaData.userId;
      detalization[INSERTED_ACTIVITY_NUMBER] += histories.length;
    }

    if (insertActivityParameters.length > 0) {
      const activityParameters = insertActivityParameters.map((item: any) => {
        return this.historyParameterRepository.create(item);
      });

      await queryRunner.manager.save(activityParameters);
      detalization[INSERTED_PARAMETER_NUMBER] += activityParameters.length;
    }

    this.resultDetalization[id][0][INSERTED_ACTIVITY_NUMBER] +=
      detalization[INSERTED_ACTIVITY_NUMBER];
    this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER] +=
      detalization[INSERTED_PARAMETER_NUMBER];

    if (changeSettings) {
      const caseAttributes = {} as Case;

      if (changeSettings.setCaseStatusId) {
        caseAttributes.statusId = changeSettings.setCaseStatusId;
      }

      if (changeSettings.setCaseStatusReasonNull) {
        caseAttributes.statusReasonId = null;
      } else if (changeSettings.setCaseStatusReasonId) {
        caseAttributes.statusReasonId = changeSettings.setCaseStatusReasonId;
      }

      if (changeSettings.setCaseStageId) {
        caseAttributes.stageId = changeSettings.setCaseStageId;
      }

      if (Object.keys(caseAttributes).length > 0) {
        const caseIDs = insertHistories.map((history: any) => history.caseId);

        if (caseIDs.length > 0) {
          await queryRunner.manager.update(
            Case,
            { id: In(caseIDs) },
            caseAttributes,
          );
        }

        const caseLogs: CaseChangeLog[] = insertHistories.map(
          (history: any) => {
            const fields: any = {};
            if (caseAttributes.stageId) {
              fields['StageID'] = caseAttributes.stageId;
            }

            if (caseAttributes.statusId) {
              fields['StatusID'] = caseAttributes.statusId;
            }

            if (caseAttributes.statusReasonId !== undefined) {
              fields['StatusReasonID'] = caseAttributes.statusReasonId;
            }

            return this.caseChangeLogRepository.create({
              historyId: String(history.id),
              value: { ...fields, ToolSourceID: 1 },
            });
          },
        );

        await queryRunner.manager.save(caseLogs);
      }
    }

    if (casesToActivateLegalRules.length > 0) {
      const caseChanges = this.getCaseChanges(
        insertActivityParameters,
        historyToCaseID,
      );
      this.activateLegalRules(
        casesToActivateLegalRules,
        caseChanges,
        id,
        metaData?.userId,
      );
    }
  }

  public async getGeneratedCsvFields(
    id: string,
    typeID: number,
  ): Promise<stream.PassThrough | string> {
    const parameters: string[] =
      await this.historyTypeToParameterRepository.getParameterSlugs(typeID);
    if (id === 'dictionary') {
      const dictionaries = await this.parameterRepository.getSlugDictionary(
        parameters,
      );
      return this.generateCustomExcelFile(dictionaries);
    }
    let csvBody = '';
    csvBody =
      parameters && parameters.length > 0
        ? [
            'LegalCaseID',
            ...parameters.map((parameter) => `${parameter}`),
          ].join(';')
        : ['LegalCaseID'].join(';');

    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  async checkFileTemplate(
    typeID: number,
    filePath: string,
  ): Promise<ValidationResult> {
    const parameters: string[] =
      await this.historyTypeToParameterRepository.getParameterSlugs(typeID);
    const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Number.POSITIVE_INFINITY,
    });

    let headers: string[] = [];
    for await (const line of rl) {
      headers = Object.keys(JSON.parse(line));
      break;
    }

    headers = headers.filter((header) => header !== 'LegalCaseID');

    if (headers.length === 0) {
      return {
        success: true,
        errors: [],
      } as ValidationResult;
    }

    for (const header of headers) {
      if (!parameters.includes(header)) {
        return {
          success: false,
          errors: [
            {
              column: header,
              errorCode: 'HeaderIsNotAcceptable',
              row: 1,
            },
          ],
        } as ValidationResult;
      }
    }

    type parameter = {
      ID: number;
      Name: string;
      Slug: string;
      IsRequired: number;
    };
    try {
      const dictionaryServiceURL = this.config.get<string>(
        'dictionaryService.url',
      );
      const availableParams = await this.apiClient.sendRequest<parameter[]>(
        HttpMethod.GET,
        dictionaryServiceURL + '/history-parameter-by-type?TypeID=' + typeID,
        { timeout: 5000 },
      );

      const requiredSlugs = new Set(
        availableParams
          .filter((parameter: parameter) => parameter.IsRequired)
          .map((parameter: parameter) => parameter.Slug),
      );

      for (const requiredSlug of requiredSlugs) {
        if (!headers.includes(requiredSlug)) {
          return {
            success: false,
            errors: [
              {
                column: requiredSlug,
                errorCode: 'HeaderRequired',
                row: 1,
              },
            ],
          } as ValidationResult;
        }
      }

      const availableSlugs = new Set(
        availableParams.map((parameter: parameter) => parameter.Slug),
      );

      for (const header of headers) {
        if (!availableSlugs.has(header)) {
          return {
            success: false,
            errors: [
              {
                column: header,
                errorCode: 'HeaderIsNotAcceptable',
                row: 1,
              },
            ],
          } as ValidationResult;
        }
      }
    } catch (error) {
      console.log(error);
      return {
        success: false,
        errors: [
          {
            column: 'Headers',
            errorCode: 'HeaderValidationFailed',
            row: 1,
          },
        ],
      } as ValidationResult;
    }

    return {
      success: true,
    } as ValidationResult;
  }

  protected getCaseChanges(
    insertActivityParameters: HistoryParameter[],
    historyToCaseID: any,
  ): { [key: number]: any } {
    const caseChanges: any = {};
    for (const activityParameter of insertActivityParameters) {
      const caseId = historyToCaseID[activityParameter.historyId];
      caseChanges[caseId] = caseChanges[caseId] ?? [];
      caseChanges[caseId].push({
        ParameterID: activityParameter.parameterId,
        ParameterValue: activityParameter.value,
      });
    }

    return caseChanges;
  }

  protected getToolSource() {
    return ToolSourceEnum.ImportActivityParameter;
  }
}
