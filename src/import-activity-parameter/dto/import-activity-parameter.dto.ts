import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { ActivityParameterDTO } from './activity-parameter.dto';

export class ImportActivityParameterDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: ActivityParameterDTO[];

  importFile: string;

  typeID: number;
}
