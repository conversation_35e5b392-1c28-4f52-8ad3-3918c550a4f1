import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseChangeLogRepository } from '../repositories/activity/case-change-log.repository';
import { HistoryParameterRepository } from '../repositories/activity/history-parameter.repository';
import { HistoryRepository } from '../repositories/activity/history.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { HistoryTypeToParameterRepository } from '../repositories/dictionary/history-type-to-parameter.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportActivityParameterController } from './import-activity-parameter.controller';
import { ImportActivityParameterService } from './import-activity-parameter.service';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      TransactionRepository,
      InvoiceRepository,
      ParameterRepository,
      HistoryRepository,
      HistoryParameterRepository,
      HistoryTypeToParameterRepository,
      CaseChangeLogRepository,
      DataTypeRepository,
      CaseRepository,
      EventRuleLogRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportActivityParameterController],
  providers: [
    ImportActivityParameterService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportActivityParameterModule {}
