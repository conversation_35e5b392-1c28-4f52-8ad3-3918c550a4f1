import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportActivityParameterDTO } from './dto/import-activity-parameter.dto';
import { ActivityParameterDTO } from './dto/activity-parameter.dto';
import { ImportActivityParameterService } from './import-activity-parameter.service';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-activity-parameter')
export class ImportActivityParameterController {
  constructor(
    private importActivityParameterService: ImportActivityParameterService,
  ) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importActivityParameterDTO: ImportActivityParameterDTO) {
    const { importFile, userID, typeID } = importActivityParameterDTO;

    const validationResult =
      await this.importActivityParameterService.checkFileTemplate(
        typeID,
        importFile,
      );

    if (!validationResult.success) {
      return validationResult;
    }

    return this.importActivityParameterService.importLargeFile(
      importFile,
      userID,
      ActivityParameterDTO,
      { typeId: typeID },
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; typeID: number },
  ): Promise<any> {
    const readStreamOrPath =
      await this.importActivityParameterService.getGeneratedCsvFields(
        query.id,
        query.typeID,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportActivityParameter - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      if (typeof readStreamOrPath === 'string') {
        const readStream = fs.createReadStream(readStreamOrPath);
        readStream.pipe(response);

        readStream.on('end', () => {
          fs.unlink(readStreamOrPath, (error) => {
            if (error) {
              console.error('Failed to delete temp file:', error);
            }
          });
        });
      } else {
        readStreamOrPath.pipe(response);
      }
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportActivityParameter - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');

      if (typeof readStreamOrPath !== 'string') {
        readStreamOrPath.pipe(response);
      }
    }
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importActivityParameterService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
