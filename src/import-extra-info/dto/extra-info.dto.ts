import { Expose } from 'class-transformer';
import { IsNumber } from 'class-validator';
export class ExtraInfoDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  public CaseExtra1: any;

  @Expose()
  public CaseExtra2: any;

  @Expose()
  public CaseExtra3: any;

  @Expose()
  public CaseExtra4: any;

  @Expose()
  public CaseExtra5: any;

  @Expose()
  public CaseExtra6: any;

  @Expose()
  public CaseExtra7: any;

  @Expose()
  public CaseExtra8: any;

  @Expose()
  public CaseExtra9: any;

  @Expose()
  public CaseExtra10: any;
}
