import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';

import { ExtraInfoDTO } from './extra-info.dto';

export class ImportExtraInfoDto {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: ExtraInfoDTO[];
}
