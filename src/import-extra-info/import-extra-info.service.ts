import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import stream from 'stream';
import { QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { AdditionalNote } from '../entities/data/additional-note.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { TemporaryAdditionalNote } from '../events/listeners/extra-info-listener/temporary-tables/temporary-additional-note';
import { ImportExtraInfoPublisher } from '../events/publishers/import-extra-info-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { AdditionalNoteRepository } from '../repositories/data/additional-note.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ExtraInfoDTO } from './dto/extra-info.dto';

const INSERTED_NUMBER = 'Number of inserted variables';
const UPDATED_NUMBER = 'Number of updated variables';

@Injectable()
export class ImportExtraInfoService extends BaseImportService<ExtraInfoDTO> {
  public catalog: CatalogEnum = CatalogEnum.ExtraInfo;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.ExtraInfo;
  protected publisher: ImportPublisher<any> = new ImportExtraInfoPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly additionalNoteRepository: AdditionalNoteRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertItems = result?.insertItems;
    const updateItems = result?.updateItems;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_NUMBER]: 0,
          [UPDATED_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_NUMBER]: this.resultDetalization[id][0][INSERTED_NUMBER],
      [UPDATED_NUMBER]: this.resultDetalization[id][0][UPDATED_NUMBER],
    };

    if (insertItems.length > 0) {
      const models = insertItems.map((item: any) =>
        this.additionalNoteRepository.create(item),
      );

      const insertChunks = this.sliceIntoChunks(models, 5000);
      for (const chunk of insertChunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (Object.keys(updateItems).length > 0) {
      const itemsToUpdate: AdditionalNote[] = [];

      for (const additionalNoteId of Object.keys(updateItems)) {
        itemsToUpdate.push(
          this.additionalNoteRepository.create({
            id: Number(additionalNoteId),
            value: updateItems[additionalNoteId],
          }),
        );
      }

      const temporaryAdditionalNote = new TemporaryAdditionalNote(
        queryRunner.manager,
        this.config,
      );
      await temporaryAdditionalNote.initialize();
      const updateChunks = this.sliceIntoChunks(itemsToUpdate, 5000);
      for (const chunk of updateChunks) {
        await temporaryAdditionalNote.insert(chunk);
        detalization[UPDATED_NUMBER] += chunk.length;
      }
    }

    this.resultDetalization[id][0][INSERTED_NUMBER] =
      detalization[INSERTED_NUMBER];
    this.resultDetalization[id][0][UPDATED_NUMBER] =
      detalization[UPDATED_NUMBER];
  }

  public async getGeneratedCsvFields(
    fields: string[],
  ): Promise<stream.PassThrough> {
    const csvBody = [
      'CaseID',
      ...fields.map((field) => `CaseExtra${field}`),
    ].join(';');

    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }
}
