import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ExtraInfoDTO } from './dto/extra-info.dto';
import { ImportExtraInfoService } from './import-extra-info.service';
import { ImportExtraInfoDto } from './dto/import-extra-info.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-extra-info')
export class ImportExtraInfoController {
  constructor(private importExtraInfoService: ImportExtraInfoService) {}

  @Post()
  @CsvImport('file', ExtraInfoDTO)
  import(@Body() importExtraInfoDto: ImportExtraInfoDto) {
    return this.importExtraInfoService.apply(importExtraInfoDto);
  }
  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; extraFields: string[] },
  ): Promise<any> {
    const readStream = await this.importExtraInfoService.getGeneratedCsvFields(
      query.extraFields,
    );

    response.set(
      disposition,
      'attachment; filename=ImportExtraInfo - ' + query.id + '.csv',
    );
    response.set(contentType, 'text/plain');

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importExtraInfoService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
