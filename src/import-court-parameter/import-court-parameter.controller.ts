import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { CourtParameterDTO } from './dto/court-parameter.dto';
import { ImportCourtParameterService } from './import-court-parameter.service';
import { ImportCourtParameterDTO } from './dto/import-court-parameter.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-court-parameter')
export class ImportCourtParameterController {
  constructor(
    private importCourtParameterService: ImportCourtParameterService,
  ) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importCourtParameterDTO: ImportCourtParameterDTO) {
    const { importFile, userID } = importCourtParameterDTO;

    const validationResult =
      await this.importCourtParameterService.checkFileTemplate(importFile);

    if (!validationResult.success) {
      return validationResult;
    }

    return this.importCourtParameterService.importLargeFile(
      importFile,
      userID,
      CourtParameterDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStreamOrPath =
      await this.importCourtParameterService.getGeneratedCsvFields(
        query.id,
        query.parameters,
      );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportCourtParameter - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      if (typeof readStreamOrPath === 'string') {
        const readStream = fs.createReadStream(readStreamOrPath);
        readStream.pipe(response);

        readStream.on('end', () => {
          fs.unlink(readStreamOrPath, (error) => {
            if (error) {
              console.error('Failed to delete temp file:', error);
            }
          });
        });
      } else {
        readStreamOrPath.pipe(response);
      }
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportCourtParameter - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');

      if (typeof readStreamOrPath !== 'string') {
        readStreamOrPath.pipe(response);
      }
    }
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importCourtParameterService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
