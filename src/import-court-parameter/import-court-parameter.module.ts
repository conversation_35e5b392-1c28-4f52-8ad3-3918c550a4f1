import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { CaseRepository } from '../repositories/data/case.repository';
import { CourtProcessParameterRepository } from '../repositories/data/court-process-parameter.repository';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportCourtParameterController } from './import-court-parameter.controller';
import { ImportCourtParameterService } from './import-court-parameter.service';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';
import { EventRuleLogRepository } from 'src/repositories/list/event-rule-log.repository';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      CaseRepository,
      TransactionRepository,
      CourtProcessRepository,
      ParameterRepository,
      CourtProcessParameterRepository,
      DataTypeRepository,
      InvoiceRepository,
      EventRuleLogRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportCourtParameterController],
  providers: [
    ImportCourtParameterService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportCourtParameterModule {}
