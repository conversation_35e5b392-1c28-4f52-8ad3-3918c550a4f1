import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { plainToClass } from 'class-transformer';
import fs from 'fs';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import readline from 'readline';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import stream from 'stream';
import { getManager, In, QueryRunner } from 'typeorm';
import { HttpMethod } from '../common/enums/http-method.enum';
import { ToolSourceEnum } from '../common/enums/tool-source.enum';
import { splitOnChunks } from '../common/helpers/split-on-chunks';
import { BaseImportWithParameterService } from '../common/imports/base-import-with-parameter.service';
import { REDIS } from '../common/redis/constants';
import { ValidationResult } from '../common/validation/validation-result.type';
import { CourtProcessParameter } from '../entities/data/court-process-parameter.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportCourtParameterPublisher } from '../events/publishers/import-court-parameter-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { CaseRepository } from '../repositories/data/case.repository';
import { CourtProcessParameterRepository } from '../repositories/data/court-process-parameter.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { CourtProcessRepository } from '../repositories/legal/court-process.repository';
import { EventRuleLogRepository } from '../repositories/list/event-rule-log.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { CourtParameterDTO } from './dto/court-parameter.dto';
import { DataTypeRepository } from 'src/repositories/dictionary/data-type.repository';

const INSERTED_PARAMETER_NUMBER = 'Number of inserted documents';
const DELETED_PARAMETER_NUMBER = 'Number of deleted parameters';

@Injectable()
export class ImportCourtParameterService extends BaseImportWithParameterService<CourtParameterDTO> {
  catalog: CatalogEnum = CatalogEnum.CourtParameter;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.CourtParameter;
  protected publisher: ImportPublisher<any> = new ImportCourtParameterPublisher(
    this.connection,
  );

  protected useRowsCantBeClearedFeature = true;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly courtProcessRepository: CourtProcessRepository,
    protected readonly invoiceRepository: InvoiceRepository,
    protected readonly parameterRepository: ParameterRepository,
    protected readonly courtProcessParameterRepository: CourtProcessParameterRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly dataTypeRepository: DataTypeRepository,
    protected readonly eventRuleLogRepository: EventRuleLogRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
      invoiceRepository,
      parameterRepository,
      dataTypeRepository,
      serviceParameterRepository,
      eventRuleLogRepository,
      caseRepository,
    );
  }

  preValidate = undefined;

  protected formatRow(lines: string[], type: any): any[] {
    const array: any[] = [];
    for (const line of lines) {
      array.push(JSON.parse(line));
    }

    return plainToClass(type, array, {
      excludeExtraneousValues: false,
      enableImplicitConversion: true,
    });
  }

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertParameters = result?.parametersCreate;
    const deleteParameters = result?.parametersDelete;
    const casesToActivateLegalRules = result?.casesToActivateLegalRules;
    const userID = result?.userId;

    this.saveAppellateCourt(id, result);

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_PARAMETER_NUMBER]: 0,
          [DELETED_PARAMETER_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_PARAMETER_NUMBER]:
        this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER],
      [DELETED_PARAMETER_NUMBER]:
        this.resultDetalization[id][0][DELETED_PARAMETER_NUMBER],
    };

    if (deleteParameters.length > 0) {
      const updateChunks = splitOnChunks(deleteParameters, 5000);
      for (const updateChunk of updateChunks) {
        await queryRunner.manager.update(
          CourtProcessParameter,
          { id: In(updateChunk) },
          { isDeleted: 1, updatedUserId: userID },
        );
      }

      detalization[DELETED_PARAMETER_NUMBER] += deleteParameters.length;
    }

    if (insertParameters.length > 0) {
      const parameters = insertParameters.map((item: any) => {
        return this.courtProcessParameterRepository.create(item);
      });

      const insertChunks = splitOnChunks(parameters, 5000);
      for (const insertChunk of insertChunks) {
        await queryRunner.manager.save(insertChunk);
      }

      detalization[INSERTED_PARAMETER_NUMBER] += parameters.length;
    }

    if (casesToActivateLegalRules.length > 0) {
      const caseChanges = await this.getCaseChanges(
        insertParameters,
        deleteParameters,
      );

      this.activateLegalRules(
        casesToActivateLegalRules,
        caseChanges,
        id,
        result?.userId,
      );
    }

    this.resultDetalization[id][0][INSERTED_PARAMETER_NUMBER] =
      detalization[INSERTED_PARAMETER_NUMBER];
    this.resultDetalization[id][0][DELETED_PARAMETER_NUMBER] =
      detalization[DELETED_PARAMETER_NUMBER];
  }

  public async getGeneratedCsvFields(
    id: string,
    parameters: string[],
  ): Promise<stream.PassThrough | string> {
    if (id === 'dictionary') {
      const dictionaries = await this.parameterRepository.getSlugDictionary(
        parameters,
      );
      return this.generateCustomExcelFile(dictionaries);
    }

    const csvBody = [
      'LegalCaseID',
      ...parameters.map((parameter) => `${parameter}`),
    ].join(';');

    const buffer = Buffer.from('\uFEFF' + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }

  async checkFileTemplate(filePath: string): Promise<ValidationResult> {
    const fileStream = fs.createReadStream(filePath, { encoding: 'utf8' });
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Number.POSITIVE_INFINITY,
    });

    let headers: string[] = [];
    for await (const line of rl) {
      headers = Object.keys(JSON.parse(line));
      break;
    }

    headers = headers.filter((header) => header !== 'LegalCaseID');

    if (headers.length === 0) {
      return {
        success: false,
        errors: [
          {
            column: 'Header',
            errorCode: 'EmptyHeaders',
            row: 1,
          },
        ],
      } as ValidationResult;
    }

    type parameter = { ID: number; Name: string; Slug: string };
    try {
      const dictionaryServiceURL = this.config.get<string>(
        'dictionaryService.url',
      );
      const availableParams = await this.apiClient.sendRequest<parameter[]>(
        HttpMethod.GET,
        dictionaryServiceURL + '/document-parameter-by-court-process',
        { timeout: 5000 },
      );

      const availableSlugs = new Set(
        availableParams.map((parameter: parameter) => parameter.Slug),
      );

      for (const header of headers) {
        if (!availableSlugs.has(header)) {
          return {
            success: false,
            errors: [
              {
                column: header,
                errorCode: 'HeaderIsNotAcceptable',
                row: 1,
              },
            ],
          } as ValidationResult;
        }
      }
    } catch (error) {
      console.error(error);
      return {
        success: false,
        errors: [
          {
            column: 'Headers',
            errorCode: 'HeaderValidationFailed',
            row: 1,
          },
        ],
      } as ValidationResult;
    }

    return {
      success: true,
    } as ValidationResult;
  }

  async postActionHandler(id: number): Promise<void> {
    await this.callAppellateCourtMapping(id);
  }

  protected async getCaseChanges(
    create: CourtProcessParameter[],
    deleteIDs: number[],
  ): Promise<{ [key: number]: any }> {
    const caseChanges: { [key: number]: any } = {};
    const deletedParameterToCase: any = {};
    const courtProcessToCase: any = {};

    const deletedChunks = splitOnChunks(deleteIDs, 1000);
    for (const chunk of deletedChunks) {
      const records = await getManager().query(
        `select dcp."ID", lcp."CaseID" from "Data"."CourtProcessParameter" dcp
    left join "LegalUA"."CourtProcess" lcp on lcp."ID" = dcp."CourtProcessID"
    where dcp."ID" in (${chunk.join(',')});
      `,
      );

      for (const record of records) {
        deletedParameterToCase[record.ID] = record.CaseID;
      }
    }

    const insertedChunks = splitOnChunks(create, 1000);
    for (const chunk of insertedChunks) {
      const records = await getManager().query(
        `select dcp."CourtProcessID" as "ID", lcp."CaseID" from "Data"."CourtProcessParameter" dcp
    left join "LegalUA"."CourtProcess" lcp on lcp."ID" = dcp."CourtProcessID"
    where dcp."CourtProcessID" in (${chunk
      .map((i) => i.courtProcessId)
      .join(',')});
      `,
      );

      for (const record of records) {
        courtProcessToCase[record.ID] = record.CaseID;
      }
    }

    for (const record of create) {
      const caseId = courtProcessToCase[record.courtProcessId];
      caseChanges[caseId] = caseChanges[caseId] ?? [];
      caseChanges[caseId].push({
        ParameterID: record.parameterId,
        ParameterValue: record.value,
      });
    }

    for (const id of deleteIDs) {
      const caseId = deletedParameterToCase[id];
      caseChanges[caseId] = caseChanges[caseId] ?? [];
      caseChanges[caseId].push({
        DeletedParameterID: id,
      });
    }

    return caseChanges;
  }

  protected getToolSource() {
    return ToolSourceEnum.ImportCourtProcessParameter;
  }
}
