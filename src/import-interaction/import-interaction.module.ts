import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { EntityForAttachmentMappingRepository } from '../repositories/dictionary/entity-for-attachment-mapping.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportInteractionController } from './import-interaction.controller';
import { ImportInteractionService } from './import-interaction.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      CaseRepository,
      EntityForAttachmentMappingRepository,
    ]),
  ],
  controllers: [ImportInteractionController],
  providers: [ImportInteractionService, ValidatorDataAccess, ApiClient],
})
export class ImportInteractionModule {}
