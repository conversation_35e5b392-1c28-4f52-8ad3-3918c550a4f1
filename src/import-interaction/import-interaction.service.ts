import { Inject, Injectable } from '@nestjs/common';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { Import } from 'src/import/import.service';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import stream from 'stream';
import {
  ErrorItem,
  FailValidationResult,
} from '../common/validation/interfaces';
import { ValidationResult } from '../common/validation/validation-result.type';
import { ImportInteractionPublisher } from '../events/publishers/import-interaction-publisher';
import { UploadStatus } from '../import/upload-status.enum';
import { UploadDTO } from './dto/upload.dto';

@Injectable()
export class ImportInteractionService extends Import<any> {
  catalog: CatalogEnum = CatalogEnum.Interaction;

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super();
  }

  preValidate = undefined;

  public async publish(
    importData: UploadDTO[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<void> {
    await new ImportInteractionPublisher(this.connection).publish({
      importData,
      userId,
      uploadHistoryId,
    });
  }

  public async upload(params: UploadDTO) {
    const validationResult = await this.customValidate(params);

    const uploadHistory = await this.uploadHistoryRepository.save({
      importListId: this.catalog,
      insertedUserId: params.userID,
      statusId: UploadStatus.InProgress,
    });

    if (validationResult.success) {
      uploadHistory.statusId = UploadStatus.InProgress;
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: uploadHistory.statusId },
      );

      await this.publish([params], params.userID, uploadHistory.id);

      return Object.assign({ id: uploadHistory.id }, validationResult);
    } else {
      await this.uploadHistoryRepository.update(
        { id: uploadHistory.id },
        { statusId: UploadStatus.ValidationFailed },
      );
      return validationResult;
    }
  }

  private async customValidate(params: UploadDTO): Promise<ValidationResult> {
    const notValidResults: FailValidationResult[] = [];
    const errors: ErrorItem[] = [];

    const type = params.type;
    const uniqueValues = new Set();
    for (const [index, row] of params.importData.entries()) {
      switch (type) {
        case 'case':
          if (uniqueValues.has(row.CaseID)) {
            errors.push({
              row: index + 2,
              column: 'CaseID',
              errorCode: 'CaseIDUniqueError',
              errorParams: null,
            });
          } else {
            uniqueValues.add(row.CaseID);
          }
          break;
        case 'invoice':
          if (uniqueValues.has(row.InvoiceNum)) {
            errors.push({
              row: index + 2,
              column: 'InvoiceNum',
              errorCode: 'InvoiceNumUniqueError',
              errorParams: null,
            });
          } else {
            uniqueValues.add(row.InvoiceNum);
          }
          break;
        case 'client':
          if (uniqueValues.has(row.ClientCaseID)) {
            errors.push({
              row: index + 2,
              column: 'ClientCaseID',
              errorCode: 'ClientCaseIDUniqueError',
              errorParams: null,
            });
          } else {
            uniqueValues.add(row.ClientCaseID);
          }
          break;
      }

      if (row?.CreationDateTime) {
        if (
          !/^(0[1-9]|[12]\d|3[01])\.(0[1-9]|1[0-2])\.\d{4} ([01]\d|2[0-3])(?::[0-5]\d){2}$/.test(
            row.CreationDateTime,
          )
        ) {
          errors.push({
            row: index + 2,
            column: 'CreationDateTime',
            errorCode: 'DateTimeFormatError',
            errorParams: null,
          });
        }

        if (errors.length === 0 && this.checkFutureDate(row.CreationDateTime)) {
          errors.push({
            row: index + 2,
            column: 'CreationDateTime',
            errorCode: 'DateTimeInFutureError',
            errorParams: null,
          });
        }
      }
    }

    if (errors.length > 0) {
      notValidResults.push({
        success: false,
        errors,
      });
    }

    return {
      success: notValidResults.length === 0,
      ...(notValidResults.length > 0 && {
        errors: notValidResults.flatMap((r) => r.errors),
      }),
    } as ValidationResult;
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public getTemplate(type: string): stream.PassThrough {
    let headersString = '';
    if (type === 'invoice') {
      headersString = 'InvoiceNum';
    }

    if (type === 'case') {
      headersString = 'CaseID';
    }

    if (type === 'client') {
      headersString = 'ClientCaseID';
    }

    headersString += ';Note;CreationDateTime';

    const buffer = Buffer.from('\uFEFF' + headersString);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }
  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }

  protected checkFutureDate(CreationDateTime: string): boolean {
    const [datePart, timePart] = CreationDateTime.split(' ');
    const [day, month, year] = datePart
      .split('.')
      .map((element) => Number(element));
    const [hours, minutes, seconds] = timePart
      .split(':')
      .map((element) => Number(element));
    const inputDate = new Date(year, month - 1, day, hours, minutes, seconds);
    const currentDate = new Date();
    return inputDate > currentDate;
  }
}
