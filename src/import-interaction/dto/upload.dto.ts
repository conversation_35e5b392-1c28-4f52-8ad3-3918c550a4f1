import { Transform } from 'class-transformer';
import {
  IsNumber,
  IsNumberString,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ImportDataDTO } from './import-data.dto';
export class UploadDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @IsString()
  type: string;

  @IsString()
  ActionForMultipleCases: string;

  @Transform(({ value }) => JSON.parse(value))
  params: { [key: string]: any };

  @ValidateNested({ each: true })
  importData: ImportDataDTO[];
}
