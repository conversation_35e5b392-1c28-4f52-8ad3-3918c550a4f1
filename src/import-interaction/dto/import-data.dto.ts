import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';
export class ImportDataDTO {
  @Expose()
  @IsOptional()
  @IsNumber()
  public CaseID?: number;

  @Expose()
  @IsOptional()
  @IsString()
  public ClientCaseID?: string;

  @Expose()
  @IsOptional()
  @IsString()
  public InvoiceNum?: string;

  @Expose()
  @IsOptional()
  @IsString()
  public Note?: string;

  @Expose()
  @IsOptional()
  @IsString()
  public CreationDateTime?: string;
}
