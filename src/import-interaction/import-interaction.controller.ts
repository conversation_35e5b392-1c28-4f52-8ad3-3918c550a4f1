import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import { CsvImport } from '../common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { ImportDataDTO } from './dto/import-data.dto';
import { UploadDTO } from './dto/upload.dto';

import { ImportInteractionService } from './import-interaction.service';

@Controller('admin/import-interaction')
export class ImportInteractionController {
  constructor(private importInteractionService: ImportInteractionService) {}

  @Post()
  @CsvImport('file', ImportDataDTO)
  upload(@Body() params: UploadDTO) {
    return this.importInteractionService.upload(params);
  }

  @Get()
  template(@Res() response: Response, @Query() params: { type: string }) {
    response.set(
      'Content-disposition',
      'attachment; filename=import-interaction-by-' + params.type + '.csv',
    );
    response.set('Content-Type', 'text/plain');
    const stream = this.importInteractionService.getTemplate(params.type);
    stream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importInteractionService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      'Content-disposition',
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');
    file.pipe(response);
  }
}
