import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import * as fs from 'fs';
import { diskStorage } from 'multer';
import path from 'path';
import { UserID } from '../common/decorators/user-id.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';

import { PreviewDTO } from './dto/preview.dto';
import { SearchDTO } from './dto/search.dto';
import { UploadDTO } from './dto/upload.dto';
import { ImportCaseAttachmentService } from './import-case-attachment.service';

@Controller('admin/import-case-attachment')
export class ImportCaseAttachmentController {
  constructor(
    private importCaseAttachmentService: ImportCaseAttachmentService,
  ) {}

  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './extracted',
      }),
    }),
  )
  @Post('preview')
  async preview(
    @UploadedFile() file: Express.Multer.File,
    @Body() params: PreviewDTO,
    @Res() response: Response,
    @UserID() userId: number,
  ) {
    const result = await this.importCaseAttachmentService.preview(
      file,
      params,
      params.cacheKey ?? null,
      userId,
    );

    if (params.download) {
      this.downloadCsv(
        response,
        result.columns,
        [],
        'case-attachment-preview.csv',
      );
      return;
    }

    const columns = result.columns;
    const cacheKey = result.cacheKey;

    const page = params.page ?? 1;
    const perPage = params.perPage ?? 20;
    const total = result.columns.length;

    response.set({ 'X-Cache-Key': cacheKey });
    response.set({ 'X-Pagination-Total-Count': total });
    response.set({ 'X-Pagination-Page-Count': Math.ceil(total / perPage) });
    response.set({ 'X-Pagination-Current-Page': page });
    response.set({ 'X-Pagination-Per-Page': perPage });

    if (params.page) {
      const page = params.page < 1 ? 1 : params?.page;
      const perPage = Number(params.perPage ?? 20);
      const start = (page - 1) * perPage;
      const end = start + Number(perPage);
      response.json(columns.slice(start, end));
      return;
    }
    response.json(columns);
  }

  @Post('search')
  async search(
    @Body() params: SearchDTO,
    @Res() response: Response,
    @UserID() userId: number,
  ) {
    const requiredOneOfFields = [
      'CaseID',
      'InvoiceID',
      'AccountNum',
      'InvoiceNum',
      'ClientCaseID',
    ];

    if (Object.values(params.mapping).filter((value) => value).length === 0) {
      throw new BadRequestException(['Mapping can not be empty']);
    }

    let isSelected = false;
    for (const field of requiredOneOfFields) {
      if (Object.values(params.mapping).includes(field)) {
        isSelected = true;
        break;
      }
    }

    if (!isSelected) {
      throw new BadRequestException([
        'One of these fields is required: ' + requiredOneOfFields.join(', '),
      ]);
    }

    const data = await this.importCaseAttachmentService.search(params, userId);
    if (params.download) {
      this.downloadCsv(
        response,
        data,
        ['Filename'],
        'case-attachment-mapping.csv',
      );
      return;
    }

    const page = params.page ?? 1;
    const perPage = params.perPage ?? 20;
    const total = data.length;
    response.set({ 'X-Pagination-Total-Count': total });
    response.set({ 'X-Pagination-Page-Count': Math.ceil(total / perPage) });
    response.set({ 'X-Pagination-Current-Page': page });
    response.set({ 'X-Pagination-Per-Page': perPage });

    if (params.page) {
      const page = params.page < 1 ? 1 : params?.page;
      const perPage = Number(params.perPage ?? 20);
      const start = (page - 1) * perPage;
      const end = start + Number(perPage);
      response.json(data.slice(start, end));
      return;
    }
    response.json(data);
  }

  @UseInterceptors(FileInterceptor('file'))
  @Post('upload')
  upload(
    @UploadedFile() file: Express.Multer.File,
    @Body() params: UploadDTO,
    @UserID() userId: number,
  ) {
    const requiredOneOfFields = [
      'CaseID',
      'InvoiceID',
      'AccountNum',
      'InvoiceNum',
      'ClientCaseID',
    ];

    if (Object.values(params.mapping).filter((value) => value).length === 0) {
      throw new BadRequestException(['Mapping can not be empty']);
    }

    let isSelected = false;
    for (const field of requiredOneOfFields) {
      if (Object.values(params.mapping).includes(field)) {
        isSelected = true;
        break;
      }
    }

    if (!isSelected) {
      throw new BadRequestException([
        'One of these fields is required: ' + requiredOneOfFields.join(', '),
      ]);
    }

    return this.importCaseAttachmentService.upload(file, params, userId);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importCaseAttachmentService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      'Content-disposition',
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');
    file.pipe(response);
  }

  downloadCsv(
    response: Response,
    data: any[],
    exclude: string[] = [],
    filename: string,
  ) {
    const stream = this.importCaseAttachmentService.generateCsv(data, exclude);
    response.set('Content-disposition', 'attachment; filename=' + filename);
    response.set('Content-Type', 'text/plain');
    stream.pipe(response);
  }
}
