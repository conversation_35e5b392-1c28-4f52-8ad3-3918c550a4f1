import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import * as fs from 'fs/promises';
import path from 'path';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { Import } from 'src/import/import.service';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import stream from 'stream';
import { getManager } from 'typeorm';
import { Case } from '../entities/data/case.entity';
import { Invoice } from '../entities/data/invoice.entity';
import { ImportCaseAttachmentPublisher } from '../events/publishers/import-case-attachment-publisher';
import { UploadStatus } from '../import/upload-status.enum';
import { CaseRepository } from '../repositories/data/case.repository';
import { EntityForAttachmentMappingRepository } from '../repositories/dictionary/entity-for-attachment-mapping.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ArchiveService } from './archive.service';
import { PreviewDTO } from './dto/preview.dto';
import { SearchDTO } from './dto/search.dto';
import { UploadDTO } from './dto/upload.dto';

const legalInvoices = 'di.legalInvoices';
const legalInvoicesDeleted = 'li."IsDeleted" = 0';

const fileExists = async (path: string) =>
  !!(await fs.stat(path).catch(() => false));
@Injectable()
export class ImportCaseAttachmentService extends Import<any> {
  catalog: CatalogEnum = CatalogEnum.CaseAttachment;
  protected documentTypes: { [key: string]: number } = {};

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly archiveService: ArchiveService,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly caseRepository: CaseRepository,
    protected readonly entityForAttachmentMappingRepository: EntityForAttachmentMappingRepository,
  ) {
    super();
  }

  preValidate = undefined;

  public async publish(
    importData: any[],
    userId: number,
    uploadHistoryId: string,
  ): Promise<void> {
    new ImportCaseAttachmentPublisher(this.connection).publish({
      importData,
      userId,
      uploadHistoryId,
    });
  }

  public async preview(
    file: Express.Multer.File | null,
    params: PreviewDTO,
    cacheKey: string | null,
    userId: number,
  ) {
    let files: string[] = [];
    let newCacheKey = '';
    if (cacheKey) {
      files = await this.archiveService.getFilesFromCache(cacheKey);
    } else if (file) {
      const result = await this.archiveService.unpack(file, userId);
      files = result.files;
      newCacheKey = await this.archiveService.encrypt(result.path);
      await fs.rm(file.path);
    }

    if (files.length === 0) {
      throw new BadRequestException(['No file found in archive root']);
    }

    return {
      columns: this.getColumns(files, params?.delimeters ?? []),
      cacheKey: newCacheKey.length > 0 ? newCacheKey : cacheKey,
    };
  }

  public generateCsv(data: any[], exclude: string[] = []): stream.PassThrough {
    const excludeColumns = new Set(exclude);
    const headers = Object.keys(data[0]);
    const headersString =
      headers.filter((column) => !excludeColumns.has(column)).join(';') + '\n';
    let csvBody = '';
    csvBody = data
      .map((row) => {
        return headers
          .filter((column) => !excludeColumns.has(column))
          .map((column) => row[column])
          .join(';');
      })
      .join('\n');

    const buffer = Buffer.from('\uFEFF' + headersString + csvBody);
    const readStream = new stream.PassThrough();
    readStream.end(buffer);

    return readStream;
  }
  public async search(params: SearchDTO, userId: number) {
    this.documentTypes = {};
    const { columns } = await this.preview(
      null,
      params,
      params.cacheKey,
      userId,
    );
    const errors = await this.validateEntities(columns, params.mapping);
    if (errors) {
      throw errors;
    }

    const cases = await this.findCases(columns, params);
    return this.makeMatchTable(cases, params.mapping, columns);
  }

  public async upload(
    file: Express.Multer.File,
    params: UploadDTO,
    userId: number,
  ) {
    await this.preview(null, params, params.cacheKey, userId);
    const fileMapping = await this.search(params, userId);
    let attachments = this.applyUploadChanges(
      fileMapping,
      params.UploadChanges,
    );

    if (params.ActionForMultipleCases) {
      attachments = await this.applyActionForMultipleCases(
        attachments,
        params.ActionForMultipleCases,
      );
    }

    if (params.ExcludeSystemColumnsFromFileName) {
      attachments = await this.applyCustomFilename(
        attachments,
        params.mapping,
        params.delimeters,
      );
    }

    attachments = await this.applyCustomDocumentType(
      attachments,
      params.mapping,
      params.TypeID,
    );

    attachments = await this.applyCustomNote(
      attachments,
      params.mapping,
      params.Note,
    );

    const filenames = new Set(
      attachments.map((attachment) => attachment.Filename),
    );
    const archivePath = await this.archiveService.decrypt(params.cacheKey);

    attachments = attachments
      .map((attachment) => {
        return {
          CaseID: attachment.CaseID,
          Filename: attachment.Filename,
          CustomName: attachment.CustomName ?? null,
          IsHidden: params.IsHidden,
          DateOfDocument: params.DateOfDocument,
          DueDate: params.DueDate,
          Note: attachment.Note,
          SourceID: params.SourceID,
          TypeID: attachment.TypeID,
          ReplyTypeID: params.ReplyTypeID,
        };
      })
      .filter((attachment) => filenames.has(attachment.Filename));

    const uploadHistory = await this.uploadHistoryRepository.save({
      importListId: this.catalog,
      insertedUserId: userId,
      statusId: UploadStatus.InProgress,
    });
    this.publish(
      [{ directory: archivePath, attachments }],
      userId,
      uploadHistory.id,
    );
    return uploadHistory.id;
  }

  private hasBothInvoiceKeys(mapping: { [key: string]: string }): boolean {
    let hasInvoiceNumber = false;
    let hasAccountNumber = false;

    for (const column of Object.keys(mapping)) {
      const entity = mapping[column];
      if (entity === 'InvoiceNum') {
        hasInvoiceNumber = true;
      }
      if (entity === 'AccountNum') {
        hasAccountNumber = true;
      }
    }

    return hasAccountNumber && hasInvoiceNumber;
  }

  private makeMatchTable(
    cases: { [key: string]: any[] },
    mapping: { [key: string]: string },
    rows: { [key: string]: string | null | number | any[] }[],
  ) {
    const excludeColumns = new Set(['DocumentNote', 'DocumentType']);
    const table = [];

    let invoiceNumberColumn = '';
    let accountNumberColumn = '';
    let invoiceIdColumn = '';

    const invoiceIdColumnIndex = Object.values(mapping).indexOf('InvoiceID');
    const invoiceNumberColumnIndex =
      Object.values(mapping).indexOf('InvoiceNum');
    const accountNumberColumnIndex =
      Object.values(mapping).indexOf('AccountNum');

    if (invoiceIdColumnIndex !== -1) {
      invoiceIdColumn = Object.keys(mapping)[invoiceIdColumnIndex];
    }

    if (invoiceNumberColumnIndex !== -1) {
      invoiceNumberColumn = Object.keys(mapping)[invoiceNumberColumnIndex];
    }

    if (accountNumberColumnIndex !== -1) {
      accountNumberColumn = Object.keys(mapping)[accountNumberColumnIndex];
    }

    const invoiceKeys = new Set(['InvoiceNum', 'AccountNum', 'InvoiceID']);

    for (const row of rows) {
      let searchKey = '';

      let invoiceKeysAdded = false;
      for (const column of Object.keys(mapping)) {
        if (mapping[column] && !excludeColumns.has(mapping[column])) {
          if (invoiceKeys.has(mapping[column])) {
            if (!invoiceKeysAdded) {
              invoiceKeysAdded = true;

              if (invoiceIdColumn) {
                searchKey += row[invoiceIdColumn] + '_';
              }

              if (invoiceNumberColumn) {
                searchKey += row[invoiceNumberColumn] + '_';
              }

              if (accountNumberColumn) {
                searchKey += row[accountNumberColumn] + '_';
              }
            }
          } else {
            searchKey += row[column] + '_';
          }
        }
      }

      const records = cases[searchKey.slice(0, searchKey.lastIndexOf('_'))];
      row['CaseID'] = [];

      if (records) {
        for (const record of records) {
          row['CaseID'].push(Number(record.CaseID));
        }
        table.push(row);
      } else {
        table.push(row);
      }
    }

    return table;
  }

  private async findCases(
    rows: { [key: string]: string }[],
    params: SearchDTO,
  ) {
    let invoicesAdded = false;
    const mapping = params.mapping;
    const query = this.caseRepository.createQueryBuilder('dc');
    query.leftJoinAndSelect('dc.package', 'dp');
    query.andWhere('dp."IsDeleted" = 0');
    query.andWhere('dc."IsDeleted" = 0');

    if (params.PackageIDList?.length) {
      query.andWhere('dc."PackageID" IN (:...packages)', {
        packages: params.PackageIDList,
      });
    }

    if (params.ContragentIDList?.length) {
      query.andWhere('dp."ContragentID" IN (:...contragentList)', {
        contragentList: params.ContragentIDList,
      });
    }

    for (const column of Object.keys(mapping)) {
      if (mapping[column] === 'CaseID') {
        const values = rows.map((row) => Number(row[column]));
        query.andWhere('dc."ID" IN (:...ids)', { ids: values });
      }

      if (mapping[column] === 'AccountNum') {
        const values = rows.map((row) => row[column]);
        if (!invoicesAdded) {
          // eslint-disable-next-line radar/no-duplicate-string
          query.leftJoinAndSelect('dc.invoices', 'di');
          // eslint-disable-next-line radar/no-duplicate-string
          query.leftJoinAndSelect(legalInvoices, 'li', legalInvoicesDeleted);
          invoicesAdded = true;
        }
        query.andWhere('di."AccountNum" IN (:...acc_nums)', {
          acc_nums: values,
        });
      }

      if (mapping[column] === 'InvoiceID') {
        const values = rows.map((row) => row[column]);
        if (!invoicesAdded) {
          query.leftJoinAndSelect('dc.invoices', 'di');
          query.leftJoinAndSelect(legalInvoices, 'li', legalInvoicesDeleted);
          invoicesAdded = true;
        }
        query.andWhere('di."ID" IN (:...inv_ids)', {
          inv_ids: values,
        });
      }

      if (mapping[column] === 'InvoiceNum') {
        const values = rows.map((row) => row[column]);
        if (!invoicesAdded) {
          query.leftJoinAndSelect('dc.invoices', 'di');
          query.leftJoinAndSelect(legalInvoices, 'li', legalInvoicesDeleted);
          invoicesAdded = true;
        }
        query.andWhere('di."InvoiceNum" IN (:...inv_nums)', {
          inv_nums: values,
        });
      }

      if (mapping[column] === 'ClientCaseID') {
        const values = rows.map((row) => row[column]);
        query.andWhere('dc."ContragentCaseID" IN (:...contragents)', {
          contragents: values,
        });
      }
    }
    const cases = await query.getMany();

    const attributes: any = {};
    const columns = Object.keys(mapping);

    let hasInvoiceNumber = false;
    let hasAccountNumber = false;
    let hasInvoiceID = false;

    for (const column of columns) {
      const entity = mapping[column];
      if (entity === 'InvoiceNum') {
        hasInvoiceNumber = true;
      }

      if (entity === 'AccountNum') {
        hasAccountNumber = true;
      }

      if (entity === 'InvoiceID') {
        hasInvoiceID = true;
      }
    }

    for (const model of cases) {
      let key = '';
      const keys = [];
      const legalKeys: { [key: string]: number[] } = {};

      let lastIndex = 0;
      for (const column of columns) {
        const entity = mapping[column];
        if (entity === 'CaseID') {
          key += model.id + '_';
        }

        if (entity === 'ClientCaseID') {
          key += model.contragentCaseId + '_';
        }

        if (entity === 'InvoiceNum') {
          for (const invoice of model.invoices) {
            const invoiceKey = this.getInvoiceKey(
              key,
              invoice,
              hasInvoiceID,
              hasInvoiceNumber,
              hasAccountNumber,
            );

            keys.push(invoiceKey);

            if (!legalKeys[invoiceKey]) {
              for (const legalInvoice of invoice.legalInvoices) {
                if (legalKeys[invoiceKey]) {
                  legalKeys[invoiceKey].push(legalInvoice.caseId);
                } else {
                  legalKeys[invoiceKey] = [legalInvoice.caseId];
                }
              }
            }
          }
        }

        if (entity === 'AccountNum') {
          for (const invoice of model.invoices) {
            const invoiceKey = this.getInvoiceKey(
              key,
              invoice,
              hasInvoiceID,
              hasInvoiceNumber,
              hasAccountNumber,
            );
            keys.push(invoiceKey);

            if (!legalKeys[invoiceKey]) {
              for (const legalInvoice of invoice.legalInvoices) {
                if (legalKeys[invoiceKey]) {
                  legalKeys[invoiceKey].push(legalInvoice.caseId);
                } else {
                  legalKeys[invoiceKey] = [legalInvoice.caseId];
                }
              }
            }
          }
        }

        if (entity === 'InvoiceID') {
          for (const invoice of model.invoices) {
            const invoiceKey = this.getInvoiceKey(
              key,
              invoice,
              hasInvoiceID,
              hasInvoiceNumber,
              hasAccountNumber,
            );
            keys.push(invoiceKey);

            if (!legalKeys[invoiceKey]) {
              for (const legalInvoice of invoice.legalInvoices) {
                if (legalKeys[invoiceKey]) {
                  legalKeys[invoiceKey].push(legalInvoice.caseId);
                } else {
                  legalKeys[invoiceKey] = [legalInvoice.caseId];
                }
              }
            }
          }
        }

        lastIndex++;

        if (lastIndex === columns.length) {
          if (keys.length === 0) {
            keys.push(key);
          }
          for (const key of keys) {
            const clearKey = key.slice(0, key.lastIndexOf('_'));
            if (attributes[clearKey]) {
              const existTheSameCase = attributes[clearKey].find(
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                (item) => item.CaseID === model.id,
              );
              if (existTheSameCase) {
                continue;
              }
              attributes[clearKey].push({
                CaseID: model.id,
                CountCaseIDs: 1,
              });
            } else {
              attributes[clearKey] = [{ CaseID: model.id, CountCaseIDs: 1 }];
            }

            if (legalKeys[key]) {
              for (const legalCaseId of legalKeys[key]) {
                attributes[clearKey].push({
                  CaseID: legalCaseId,
                  CountCaseIDs: 1,
                });
              }
            }

            for (let index = 0; index < attributes[clearKey].length; index++) {
              attributes[clearKey][index]['CountCaseIDs'] =
                attributes[clearKey].length;
            }

            attributes[clearKey] = attributes[clearKey].sort(
              (a: any, b: any) => {
                return a.CaseID < b.CaseID ? 1 : -1;
              },
            );
          }
        }
      }
    }

    const caseIDs: number[] = [];
    for (const key of Object.keys(attributes)) {
      caseIDs.push(
        ...attributes[key].map((index: any) => Number(index.CaseID)),
      );
    }

    if (caseIDs.length > 0 && params.CaseStatusIDList?.length) {
      const actualCasesMap: { [key: string]: number } = {};
      const chunks = this.sliceIntoChunks(caseIDs, 5000);
      for (const chunk of chunks) {
        const query = this.caseRepository
          .createQueryBuilder('dc')
          .select('dc.id');
        query.andWhere('dc."StatusID" IN (:...caseStatuses)', {
          caseStatuses: params.CaseStatusIDList,
        });
        query.andWhere('dc."ID" IN (:...caseIDs)', {
          caseIDs: chunk,
        });

        const actualCases = await query.getMany();

        for (const actualCase of actualCases) {
          actualCasesMap[actualCase.id] = 1;
        }
      }

      for (const key of Object.keys(attributes)) {
        const filteredCaseIDs = [];
        for (const attribute of attributes[key]) {
          if (actualCasesMap[attribute.CaseID]) {
            filteredCaseIDs.push(attribute.CaseID);
          }
        }

        attributes[key] = filteredCaseIDs.map((caseID: string) => {
          return {
            CaseID: caseID,
            CountCaseIDs: filteredCaseIDs.length,
          };
        });
      }
    }

    return attributes;
  }
  private async validateEntities(
    rows: { [key: string]: string }[],
    mapping: { [key: string]: string },
  ) {
    const errors: any[] = [];

    let index = 0;
    for (const row of rows) {
      for (const column of Object.keys(row)) {
        const error = await this.validateEntity(mapping[column], row[column]);
        if (error) {
          errors.push({
            row: index + 1,
            message: error,
          });
        }
      }
      index++;
    }

    if (errors.length > 0) {
      return new BadRequestException(errors);
    }
    return false;
  }

  private async validateEntity(entity: string, value: string) {
    switch (entity) {
      case 'CaseID':
        if (value !== String(Math.round(Number(value)))) {
          return 'CaseID must be an integer';
        }
        break;
      case 'InvoiceID':
        if (value !== String(Math.round(Number(value)))) {
          return 'InvoiceID must be an integer';
        }
        break;
      case 'AccountNum':
        break;
      case 'ClientCaseID':
        break;
      case 'DocumentType':
        if (Object.keys(this.documentTypes).length === 0) {
          const documentTypes = await getManager().query(
            `select "ID", "Name" from "Dictionary"."DocumentType" where "IsDeleted" = 0;`,
          );

          const documentTypeToIdMap: any = {};
          for (const documentType of documentTypes) {
            documentTypeToIdMap[documentType['Name']] = documentType['ID'];
          }
          this.documentTypes = documentTypeToIdMap;
        }

        if (!this.documentTypes[value]) {
          return `DocumentType '${value}' does not exist`;
        }
        break;
    }
    return false;
  }
  private getColumns(files: string[], delimeters: string[]) {
    const expression = new RegExp(
      '[' + delimeters.map((c) => '\\' + c).join('') + ']+',
    );
    const splitted = files.map((filename) => {
      const originalFileName = filename;
      const extension = filename.split('.').pop();
      filename = filename.slice(
        0,
        filename.length - (extension?.length ?? 0) - 1,
      );
      return [originalFileName, ...filename.split(expression), extension];
    });

    return splitted.map((fileParts) => {
      const object: any = {};
      const lastIndex = fileParts.length - 1;
      for (const [index, part] of fileParts.entries()) {
        let columnName = 'Column' + index;
        if (index === lastIndex) {
          columnName = 'Extension';
        }
        if (index === 0) {
          columnName = 'Filename';
        }
        object[columnName] = part;
      }
      return object;
    });
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    return {};
  }

  public async getDetailsPath(id: number) {
    const model = await this.uploadHistoryRepository.findOne(id);
    if (model) {
      return model.resultFilePath;
    }
    return '';
  }

  private applyUploadChanges(items: any[], changes: any) {
    const result = [];
    for (const item of items) {
      for (const changeItem of changes) {
        if (item.Filename === changeItem.Filename) {
          item['CaseID'] = changeItem.CaseID;
          break;
        }
      }
      if (item.CaseID.length > 0) {
        result.push(item);
      }
    }
    return result;
  }

  private async applyCustomFilename(
    items: any[],
    mapping: { [key: string]: string },
    delimeters: string[] | undefined,
  ) {
    const result = [];
    const systemMapping =
      await this.entityForAttachmentMappingRepository.getList();

    for (const item of items) {
      item['CustomName'] = item['Filename'].replace('.' + item.Extension, '');
      for (const system of systemMapping) {
        if (Object.values(mapping).includes(system.name)) {
          const index = Object.values(mapping).indexOf(system.name);
          const columnName = Object.keys(mapping)[index];
          const valueToRemove = item[columnName];

          let regexString = `(${valueToRemove})`;
          if (delimeters) {
            regexString =
              `([${delimeters.join('')}])*` +
              regexString +
              `([${delimeters.join('')}])*`;
          }

          const regEx = new RegExp(regexString);
          item['CustomName'] = item['CustomName'].replace(regEx, '');
        }
      }
      item['CustomName'] =
        item['CustomName'].length > 0
          ? item['CustomName'] + '.' + item.Extension
          : null;
      result.push(item);
    }

    return result;
  }

  private async applyCustomNote(
    items: any[],
    mapping: { [key: string]: string },
    note: string | null,
  ) {
    const result = [];

    let noteColumn = null;
    for (const [index, map] of Object.values(mapping).entries()) {
      if (map === 'DocumentNote') {
        noteColumn = Object.keys(mapping)[index];
      }
    }

    for (const item of items) {
      item['Note'] = noteColumn ? item[noteColumn] ?? null : note;
      result.push(item);
    }

    return result;
  }

  private async applyCustomDocumentType(
    items: any[],
    mapping: { [key: string]: string },
    typeId: number | null,
  ) {
    const documentTypes = await getManager().query(
      `select "ID", "Name" from "Dictionary"."DocumentType" where "IsDeleted" = 0;`,
    );

    const documentTypeToIdMap: any = {};
    for (const documentType of documentTypes) {
      documentTypeToIdMap[documentType['Name']] = documentType['ID'];
    }

    const result = [];

    let typeColumn = null;
    for (const [index, map] of Object.values(mapping).entries()) {
      if (map === 'DocumentType') {
        typeColumn = Object.keys(mapping)[index];
      }
    }

    for (const item of items) {
      item['TypeID'] = typeColumn
        ? documentTypeToIdMap[item[typeColumn]] ?? null
        : typeId;
      result.push(item);
    }

    return result;
  }

  private async applyActionForMultipleCases(items: any[], action: string) {
    switch (action) {
      case 'dont_upload':
        return items.filter((attachment) => attachment.CaseID.length === 1);
      case 'upload_recent':
        let caseIDs: number[] = [];
        for (const attachment of items) {
          caseIDs = [...caseIDs, ...attachment.CaseID];
        }
        const uniqueCases = new Set(caseIDs);
        let cases: Case[] = [];
        if (uniqueCases.size > 0) {
          cases = await this.caseRepository
            .createQueryBuilder('dc')
            .where('dc."ID" IN (:...ids)', {
              ids: [...uniqueCases],
            })
            .orderBy('dc."Inserted"', 'DESC')
            .getMany();
        }

        const activeCases = new Set(
          cases
            .filter((caseModel) => caseModel.caseProcess === 1)
            .map((caseModel) => Number(caseModel.id)),
        );

        const inactiveCases = new Set(
          cases
            .filter((caseModel) => caseModel.caseProcess !== 1)
            .map((caseModel) => Number(caseModel.id)),
        );

        let index = 0;
        attachmentsLoop: for (const attachment of items) {
          if (attachment.CaseID.length > 1) {
            for (const caseID of attachment.CaseID) {
              if (activeCases.has(caseID)) {
                items[index].CaseID = [caseID];
                index++;
                continue attachmentsLoop;
              }
            }
            for (const caseID of attachment.CaseID) {
              if (inactiveCases.has(caseID)) {
                items[index].CaseID = [caseID];
                index++;
                continue attachmentsLoop;
              }
            }
          }
          index++;
        }

        return items;
      case 'upload_all':
      default:
        return items;
    }
  }

  sliceIntoChunks(items: any[], chunkSize: number) {
    const result = [];
    for (let index = 0; index < items.length; index += chunkSize) {
      const chunk = items.slice(index, index + chunkSize);
      result.push(chunk);
    }
    return result;
  }

  @Cron(CronExpression.EVERY_HOUR)
  async handleCron() {
    const extractedFolder = './extracted';
    const extractedPath = path.join(process.cwd(), extractedFolder);

    if (await fileExists(extractedPath)) {
      const users = await fs.readdir(extractedPath);
      const today = Date.now();
      for (const user of users) {
        if ((await fs.stat(extractedPath + '/' + user)).isDirectory()) {
          const userUploads = await fs.readdir(extractedPath + '/' + user);
          for (const userUpload of userUploads) {
            const differenceInHours =
              (today - Number(userUpload)) / 1000 / 60 / 60;

            if (differenceInHours > 3) {
              await fs.rm(extractedPath + '/' + user + '/' + userUpload, {
                recursive: true,
              });
            }
          }
        } else {
          const timestamp = (await fs.stat(extractedPath + '/' + user))
            .birthtimeMs;

          const differenceInHours = (today - timestamp) / 1000 / 60 / 60;
          if (differenceInHours > 3) {
            await fs.rm(extractedPath + '/' + user);
          }
        }
      }
    }
  }

  private getInvoiceKey(
    invoiceKey: string,
    invoice: Invoice,
    hasInvoiceID: boolean,
    hasInvoiceNumber: boolean,
    hasAccountNumber: boolean,
  ): string {
    if (hasInvoiceID) {
      invoiceKey += invoice.id + '_';
    }

    if (hasInvoiceNumber) {
      invoiceKey += invoice.invoiceNum + '_';
    }

    if (hasAccountNumber) {
      invoiceKey += invoice.accountNum + '_';
    }

    return invoiceKey;
  }
}
