import { Injectable } from '@nestjs/common';
import { exec } from 'child_process';
import * as fs from 'fs/promises';
import { createCipheriv, createDecipheriv, scrypt } from 'crypto';
import path from 'path';
import { getManager } from 'typeorm';
import { promisify } from 'util';

const extractedPath = './extracted';

const iv = Buffer.alloc(16, 'initialvector');
const algorithm = 'aes-256-cbc';

const fileExists = async (path: string) =>
  !!(await fs.stat(path).catch(() => false));

const execAsync = promisify(exec);

@Injectable()
export class ArchiveService {
  public async getFilesFromCache(cacheKey: string) {
    const uploadPath = await this.decrypt(cacheKey);
    const directoryPath = path.join(process.cwd(), uploadPath);

    if (await fileExists(directoryPath)) {
      if ((await fs.lstat(directoryPath)).isDirectory()) {
        return await fs.readdir(directoryPath);
      } else {
        const rawData = await fs.readFile(directoryPath);
        return JSON.parse(rawData.toString());
      }
    }
    return [];
  }

  public async unpack(file: Express.Multer.File, userId: number) {
    return await this.processUnar(file, userId);
  }

  private async checkDirectoryExists(path: string) {
    if (!(await fileExists(path))) {
      await fs.mkdir(path, { recursive: true });
    }
    return true;
  }

  private async processUnar(file: Express.Multer.File, userId: number) {
    const extractPath = `${extractedPath}/${userId}/${Date.now()}`;
    const filepath = `${extractPath}/${file.originalname}`;
    const directoryPath = path.join(process.cwd(), extractPath);
    await this.checkDirectoryExists(extractPath);

    const customEncoding = await this.getCustomEncoding();

    await fs.copyFile(file.path, filepath);
    const command =
      'cd ' +
      directoryPath +
      " && unar -D '" +
      file.originalname +
      "'" +
      (customEncoding ? ' -e ' + customEncoding : '') +
      " && rm '" +
      file.originalname +
      "' > /dev/null 2>&1";

    try {
      await execAsync(command);
    } catch (error) {
      console.log(error);
    }

    return { files: await fs.readdir(directoryPath), path: extractPath };
  }

  public async encrypt(data: string): Promise<string> {
    const key = (await promisify(scrypt)('nest', 'salt', 32)) as Buffer;
    const cipher = createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  public async decrypt(data: string) {
    const key = (await promisify(scrypt)('nest', 'salt', 32)) as Buffer;
    const decipher = createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  private async getCustomEncoding(): Promise<string> {
    const data: any[] = await getManager().query(
      'SELECT * FROM "Dictionary"."ServiceParameter" where "Name" = \'customCaseAttachmentEncoding\' and "IsDeleted" = 0;',
    );

    if (data.length > 0) {
      const encoding = data[0].Value;
      if (encoding) {
        return encoding;
      }
    }

    return '';
  }

  public isArchive(file: Express.Multer.File): boolean {
    const archiveMimeTypes = [
      'application/zip',
      'application/x-rar-compressed',
      'application/x-zip-compressed',
      'application/x-7z-compressed',
      'application/x-tar',
      'application/gzip',
    ];

    return archiveMimeTypes.includes(file.mimetype);
  }

  public isCSV(file: Express.Multer.File): boolean {
    const archiveMimeTypes = [
      'text/csv',
      'application/csv',
      'application/vnd.ms-excel',
      'application/x-csv-compressed',
      'text/plain',
    ];

    return archiveMimeTypes.includes(file.mimetype);
  }
}
