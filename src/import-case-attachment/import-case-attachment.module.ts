import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CaseRepository } from '../repositories/data/case.repository';
import { ContactPersonRepository } from '../repositories/data/contact-person.repository';
import { Custom001ContactPersonRepository } from '../repositories/data/custom001-contact-person.repository';
import { DebtorRepository } from '../repositories/data/debtor.repository';
import { DebtorTypeRepository } from '../repositories/debtor-type.repository';
import { EntityForAttachmentMappingRepository } from '../repositories/dictionary/entity-for-attachment-mapping.repository';
import { PartyTypeRepository } from '../repositories/party-type.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ArchiveService } from './archive.service';
import { ImportCaseAttachmentController } from './import-case-attachment.controller';
import { ImportCaseAttachmentService } from './import-case-attachment.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('caseService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      PartyTypeRepository,
      DebtorTypeRepository,
      UploadHistoryRepository,
      ContactPersonRepository,
      DebtorRepository,
      Custom001ContactPersonRepository,
      CaseRepository,
      EntityForAttachmentMappingRepository,
    ]),
  ],
  controllers: [ImportCaseAttachmentController],
  providers: [
    ImportCaseAttachmentService,
    ArchiveService,
    ValidatorDataAccess,
    ApiClient,
  ],
})
export class ImportCaseAttachmentModule {}
