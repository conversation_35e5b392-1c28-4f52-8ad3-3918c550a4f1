import { SearchDTO } from './search.dto';
export class UploadDTO extends SearchDTO {
  UploadChanges: { Filename: string; CaseID: number[] }[];
  ExcludeSystemColumnsFromFileName: boolean;
  ActionForMultipleCases: string;
  IsHidden: boolean;
  delimeters?: string[];
  mapping: { [key: string]: string };
  CaseStatusIDList?: number[];
  PackageIDList?: number[];
  ContragentIDList?: number[];
  DateOfDocument: string;
  DueDate: string | null;
  Note: string | null;
  SourceID: number;
  TypeID: number;
  ReplyTypeID: number;
}
