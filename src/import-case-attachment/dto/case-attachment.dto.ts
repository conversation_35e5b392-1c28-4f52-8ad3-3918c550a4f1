import { Expose } from 'class-transformer';
export class CaseAttachmentDTO {
  @Expose()
  public directory: string;

  @Expose()
  public attachments: {
    Filename: string;
    CaseID: number[];
    CustomName: string | null;
    IsHidden: boolean;
    DateOfDocument: string;
    DueDate: string | null;
    Note: string | null;
    SourceID: number;
    TypeID: number;
    ReplyTypeID: number;
  }[];
}
