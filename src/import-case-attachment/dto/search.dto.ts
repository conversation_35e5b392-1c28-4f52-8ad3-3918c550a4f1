import { Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
export class SearchDTO {
  @IsOptional()
  delimeters?: string[];

  mapping: { [key: string]: string };

  @IsOptional()
  CaseStatusIDList?: number[];

  @IsOptional()
  PackageIDList?: number[];

  @IsOptional()
  ContragentIDList?: number[];

  @IsOptional()
  download?: boolean;

  @IsOptional()
  @Transform(({ value }) => JSON.parse(value))
  filenames?: string[];

  @IsOptional()
  page?: number;

  @IsOptional()
  perPage?: number;

  @IsOptional()
  cacheKey: string;
}
