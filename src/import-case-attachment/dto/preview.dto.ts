import { Transform } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';
export class PreviewDTO {
  @IsOptional()
  @Transform(({ value }) => JSON.parse(value))
  delimeters?: string[];

  @IsOptional()
  page?: number;

  @IsOptional()
  perPage?: number;

  @IsOptional()
  cacheKey?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  download?: boolean;
}
