import { Transform } from 'class-transformer';
import { IsNumber, IsNumberString, ValidateNested } from 'class-validator';
import { DeleteInteractionDTO } from './delete-interaction.dto';
export class UploadDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @ValidateNested({ each: true })
  importData: DeleteInteractionDTO[];

  importFile: string;
}
