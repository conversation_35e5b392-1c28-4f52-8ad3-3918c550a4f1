import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { DeletedHistory } from '../entities/activity/deleted-history.entity';
import { History } from '../entities/activity/history.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportDeleteInteractionPublisher } from '../events/publishers/import-delete-interaction-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { DeleteInteractionDTO } from './dto/delete-interaction.dto';

const ACTIVATED_CONTACTS_NUMBER = 'Number of activated contacts';
const DEACTIVATED_CONTACTS_NUMBER = 'Number of deactivated contacts';

@Injectable()
export class ImportDeleteInteractionService extends BaseImportService<DeleteInteractionDTO> {
  catalog: CatalogEnum = CatalogEnum.DeleteInteraction;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.DeleteInteraction;
  protected publisher: ImportPublisher<any> =
    new ImportDeleteInteractionPublisher(this.connection);

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const activateContacts = result?.activateInteractions;
    const deactivateContacts = result?.deactivateInteractions;
    const metaData = result?.metaData;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [ACTIVATED_CONTACTS_NUMBER]: 0,
          [DEACTIVATED_CONTACTS_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [ACTIVATED_CONTACTS_NUMBER]:
        this.resultDetalization[id][0][ACTIVATED_CONTACTS_NUMBER],
      [DEACTIVATED_CONTACTS_NUMBER]:
        this.resultDetalization[id][0][DEACTIVATED_CONTACTS_NUMBER],
    };

    if (activateContacts.length > 0) {
      const chunks = this.sliceIntoChunks(activateContacts, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          History,
          { id: In(chunk) },
          { isDeleted: 0, updatedUserID: metaData.userId },
        );
      }
      detalization[ACTIVATED_CONTACTS_NUMBER] += activateContacts.length;
    }

    if (deactivateContacts.length > 0) {
      const chunks = this.sliceIntoChunks(deactivateContacts, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          History,
          { id: In(chunk) },
          { isDeleted: 1, updatedUserID: metaData.userId },
        );

        const deletedHistory = [];
        for (const historyId of chunk) {
          deletedHistory.push({
            historyId,
            userId: metaData.userId,
            isDeleted: 0,
          });
        }

        await queryRunner.manager.insert(DeletedHistory, deletedHistory);
      }
      detalization[DEACTIVATED_CONTACTS_NUMBER] += deactivateContacts.length;
    }

    this.resultDetalization[id][0][ACTIVATED_CONTACTS_NUMBER] =
      detalization[ACTIVATED_CONTACTS_NUMBER];
    this.resultDetalization[id][0][DEACTIVATED_CONTACTS_NUMBER] =
      detalization[DEACTIVATED_CONTACTS_NUMBER];
  }
}
