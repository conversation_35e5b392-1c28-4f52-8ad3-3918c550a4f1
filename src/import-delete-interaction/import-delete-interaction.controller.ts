import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { DeleteInteractionDTO } from './dto/delete-interaction.dto';
import { UploadDTO } from './dto/upload.dto';

import { ImportDeleteInteractionService } from './import-delete-interaction.service';

@Controller('admin/import-delete-interaction')
export class ImportDeleteInteractionController {
  constructor(
    private importDeleteInteractionService: ImportDeleteInteractionService,
  ) {}

  @Post()
  @CsvImport('file')
  async import(@Body() uploadDTO: UploadDTO) {
    const { importFile, userID } = uploadDTO;

    return this.importDeleteInteractionService.importLargeFile(
      importFile,
      userID,
      DeleteInteractionDTO,
    );
  }

  @Get()
  async template(@Res() response: Response, @Query() query: { id: string }) {
    const stream = await this.importDeleteInteractionService.getGeneratedCsv(
      query.id,
    );

    response.set(
      'Content-disposition',
      'attachment; filename=import-delete-interaction - ' + query.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');

    stream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importDeleteInteractionService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      'Content-disposition',
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');
    file.pipe(response);
  }
}
