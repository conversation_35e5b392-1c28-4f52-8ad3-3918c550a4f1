import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { Custom004EEnforcementRequestToCpiio } from '../entities/legal/custom004-enforcement-request-to-cpiio.entity';
import { EventsGateway } from '../events/events.gateway';
import { ImportBasisCpiioPublisher } from '../events/publishers/import-basis-cpiio-publisher';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { Custom004EEnforcementRequestToCpiioRepository } from '../repositories/legal/custom004-enforcement-request-to-cpiio.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { CpiioDTO } from './dto/cpiio.dto';

const INSERTED_CPIIO_NUMBER = 'Number of inserted CPIIO';
const UPDATED_CPIIO_NUMBER = 'Number of updated CPIIO';

@Injectable()
export class ImportBasisCpiioService extends BaseImportService<CpiioDTO> {
  catalog: CatalogEnum = CatalogEnum.BasisCpiio;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.BasisCpiio;
  protected publisher: ImportPublisher<any> = new ImportBasisCpiioPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly custom004EEnforcementRequestDeliveryStatusRepository: Custom004EEnforcementRequestDeliveryStatusRepository,
    protected readonly custom004EEnforcementRequestToCpiioRepository: Custom004EEnforcementRequestToCpiioRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const insertCpiio = result?.insertCpiio;
    const deactivateCpiioByBasis = result?.deactivateCpiioByBasis;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_CPIIO_NUMBER]: 0,
          [UPDATED_CPIIO_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_CPIIO_NUMBER]:
        this.resultDetalization[id][0][INSERTED_CPIIO_NUMBER],
      [UPDATED_CPIIO_NUMBER]:
        this.resultDetalization[id][0][UPDATED_CPIIO_NUMBER],
    };

    if (deactivateCpiioByBasis.length > 0) {
      await queryRunner.manager.update(
        Custom004EEnforcementRequestToCpiio,
        { BasisID: In(deactivateCpiioByBasis), IsDeleted: 0 },
        { IsProcessed: false },
      );
      detalization[UPDATED_CPIIO_NUMBER] += deactivateCpiioByBasis.length;
    }

    if (insertCpiio.length > 0) {
      await this.custom004EEnforcementRequestToCpiioRepository.save(
        insertCpiio,
      );
      detalization[INSERTED_CPIIO_NUMBER] += insertCpiio.length;
    }

    this.resultDetalization[id][0][INSERTED_CPIIO_NUMBER] =
      detalization[INSERTED_CPIIO_NUMBER];
    this.resultDetalization[id][0][UPDATED_CPIIO_NUMBER] =
      detalization[UPDATED_CPIIO_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    data.DeliveryStatus = (
      await this.custom004EEnforcementRequestDeliveryStatusRepository.find({
        where: {
          isDeleted: 0,
        },
      })
    ).map((item) => item.name);

    data.IsProcessed = ['Yes', 'No'];

    return data;
  }
}
