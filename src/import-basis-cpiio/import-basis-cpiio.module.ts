import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { DocumentParameterRepository } from '../repositories/data/document-parameter.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { Custom004EEnforcementRequestDeliveryStatusRepository } from '../repositories/dictionary/custom004-enforcement-request-delivery-status.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { Custom004EEnforcementRequestToCpiioRepository } from '../repositories/legal/custom004-enforcement-request-to-cpiio.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportBasisCpiioController } from './import-basis-cpiio.controller';
import { ImportBasisCpiioService } from './import-basis-cpiio.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      TransactionRepository,
      DocumentRepository,
      DocumentParameterRepository,
      InvoiceRepository,
      ParameterRepository,
      Custom004EEnforcementRequestToCpiioRepository,
      Custom004EEnforcementRequestDeliveryStatusRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportBasisCpiioController],
  providers: [
    ImportBasisCpiioService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportBasisCpiioModule {}
