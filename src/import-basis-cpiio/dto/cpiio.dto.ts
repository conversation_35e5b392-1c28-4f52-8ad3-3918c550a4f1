import { Expose } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class CpiioDTO {
  @Expose()
  @IsNumber()
  public CaseID: number;

  @Expose()
  @IsNumber()
  public BasisForPaymentID: number;

  @Expose()
  @IsString()
  public DeliveryDate: string;

  @Expose()
  @IsString()
  public DeliveryStatus: string;

  @Expose()
  @IsNumber()
  public DeliveryStatusID: number;

  @Expose()
  @IsString()
  public IsProcessed: string | boolean | null;
}
