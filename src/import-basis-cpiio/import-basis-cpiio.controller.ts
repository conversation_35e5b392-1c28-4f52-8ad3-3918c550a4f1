import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { CpiioDTO } from './dto/cpiio.dto';
import { ImportBasisCpiioService } from './import-basis-cpiio.service';
import { ImportCpiioDTO } from './dto/import-cpiio.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-basis-cpiio')
export class ImportBasisCpiioController {
  constructor(private importBasisCpiioService: ImportBasisCpiioService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() importLegalCourtDTO: ImportCpiioDTO) {
    const { importFile, userID } = importLegalCourtDTO;

    return this.importBasisCpiioService.importLargeFile(
      importFile,
      userID,
      CpiioDTO,
    );
  }

  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string; parameters: string[] },
  ): Promise<any> {
    const readStream = await this.importBasisCpiioService.getGeneratedCsv(
      query.id,
    );

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportCpiio - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportCpiio - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importBasisCpiioService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
