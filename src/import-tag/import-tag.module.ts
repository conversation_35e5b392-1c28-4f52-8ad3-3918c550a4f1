import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { TagForCaseDebtorNetworkRepository } from '../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../repositories/data/tag-for-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportTagController } from './import-tag.controller';
import { ImportTagService } from './import-tag.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      TagForInvoiceRepository,
      TagForCaseRepository,
      TagForCasePhoneRepository,
      TagForCaseDebtorNetworkRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportTagController],
  providers: [ImportTagService, ValidatorDataAccess, ApiClient, EventsGateway],
})
export class ImportTagModule {}
