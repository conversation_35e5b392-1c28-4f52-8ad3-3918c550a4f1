import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';
import { ApiClient } from 'src/common/api.client';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { getManager, In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { TagForCaseDebtorNetwork } from '../entities/data/tag-for-case-debtor-network.entity';
import { TagForCasePhone } from '../entities/data/tag-for-case-phone.entity';
import { TagForCase } from '../entities/data/tag-for-case.entity';
import { TagForInvoice } from '../entities/data/tag-for-invoice.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { ImportTagPublisher } from '../events/publishers/import-tag-publisher';
import { TagForCaseDebtorNetworkRepository } from '../repositories/data/tag-for-case-debtor-network.repository';
import { TagForCasePhoneRepository } from '../repositories/data/tag-for-case-phone.repository';
import { TagForCaseRepository } from '../repositories/data/tag-for-case.repository';
import { TagForInvoiceRepository } from '../repositories/data/tag-for-invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { TagDTO } from './dto/tag.dto';

const INSERTED_NUMBER = 'Number of inserted tags';
const DELETED_NUMBER = 'Number of deleted tags';

@Injectable()
export class ImportTagService extends BaseImportService<TagDTO> {
  public catalog: CatalogEnum = CatalogEnum.Tag;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.Tag;
  protected publisher: ImportPublisher<any> = new ImportTagPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
    protected readonly tagForInvoiceRepository: TagForInvoiceRepository,
    protected readonly tagForCaseRepository: TagForCaseRepository,
    protected readonly tagForCasePhoneRepository: TagForCasePhoneRepository,
    protected readonly tagForCaseDebtorNetworkRepository: TagForCaseDebtorNetworkRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);

    const caseTags = result?.caseTags;
    const caseTagsDelete = result?.caseTagsDelete;

    const casePhoneTags = result?.casePhoneTags;
    const casePhoneTagsDelete = result?.casePhoneTagsDelete;

    const caseEmailTags = result?.caseEmailTags;
    const caseEmailTagsDelete = result?.caseEmailTagsDelete;

    const caseInvoiceTags = result?.caseInvoiceTags;
    const caseInvoiceTagsDelete = result?.caseInvoiceTagsDelete;

    const metaData = result?.metaData;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [INSERTED_NUMBER]: 0,
          [DELETED_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [INSERTED_NUMBER]: this.resultDetalization[id][0][INSERTED_NUMBER],
      [DELETED_NUMBER]: this.resultDetalization[id][0][DELETED_NUMBER],
    };

    if (caseTagsDelete.length > 0) {
      const chunks = this.sliceIntoChunks(caseTagsDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          TagForCase,
          { id: In(chunk), isDeleted: 0 },
          { isDeleted: 1, deletionSourceId: metaData.deletionSourceId },
        );
      }

      detalization[DELETED_NUMBER] += caseTagsDelete.length;
    }

    if (caseTags.length > 0) {
      const models = caseTags.map((item: any) =>
        this.tagForCaseRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (casePhoneTagsDelete.length > 0) {
      const chunks = this.sliceIntoChunks(casePhoneTagsDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          TagForCasePhone,
          { id: In(chunk), isDeleted: 0 },
          { isDeleted: 1, deletionSourceId: metaData.deletionSourceId },
        );
      }

      detalization[DELETED_NUMBER] += caseTagsDelete.length;
    }

    if (casePhoneTags.length > 0) {
      const models = casePhoneTags.map((item: any) =>
        this.tagForCasePhoneRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (caseEmailTagsDelete.length > 0) {
      const chunks = this.sliceIntoChunks(caseEmailTagsDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          TagForCaseDebtorNetwork,
          { id: In(chunk), isDeleted: 0 },
          { isDeleted: 1, deletionSourceId: metaData.deletionSourceId },
        );
      }

      detalization[DELETED_NUMBER] += caseTagsDelete.length;
    }

    if (caseEmailTags.length > 0) {
      const models = caseEmailTags.map((item: any) =>
        this.tagForCaseDebtorNetworkRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    if (caseInvoiceTagsDelete.length > 0) {
      const chunks = this.sliceIntoChunks(caseInvoiceTagsDelete, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.update(
          TagForInvoice,
          { id: In(chunk), isDeleted: 0 },
          { isDeleted: 1, deletionSourceId: metaData.deletionSourceId },
        );
      }

      detalization[DELETED_NUMBER] += caseTagsDelete.length;
    }

    if (caseInvoiceTags.length > 0) {
      const models = caseInvoiceTags.map((item: any) =>
        this.tagForInvoiceRepository.create(item),
      );

      const chunks = this.sliceIntoChunks(models, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(chunk);
        detalization[INSERTED_NUMBER] += chunk.length;
      }
    }

    this.resultDetalization[id][0][INSERTED_NUMBER] =
      detalization[INSERTED_NUMBER];
    this.resultDetalization[id][0][DELETED_NUMBER] =
      detalization[DELETED_NUMBER];
  }

  public async getDictionaryData(): Promise<{ [p: string]: string[] }> {
    const data: { [key: string]: string[] } = {};

    const levels = await getManager().query(`select "Name"
from "Dictionary"."Level" as dl
where dl."IsDeleted" = 0
  and dl."IsUseForTag" = 1
order by 1;`);

    const tags = await getManager().query(`select "Name"
from "Dictionary"."Tag" as dl
where dl."IsDeleted" = 0
order by 1;`);

    data.Level = levels.map((index: any) => index.Name);
    data.Tag = tags.map((index: any) => index.Name);

    return data;
  }
}
