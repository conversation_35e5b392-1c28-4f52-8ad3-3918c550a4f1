import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';

import { CsvImport } from 'src/common/decorators/csv-import.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { TagDTO } from './dto/tag.dto';
import { ImportTagService } from './import-tag.service';
import { ImportTagDTO } from './dto/import-tag.dto';

const disposition = 'Content-disposition';
const contentType = 'Content-Type';

@Controller('admin/import-tag')
export class ImportTagController {
  constructor(private importTagService: ImportTagService) {}

  @Post()
  @CsvImport('file', TagDTO)
  import(@Body() importTagDTO: ImportTagDTO) {
    return this.importTagService.apply(importTagDTO);
  }
  @Get()
  async getGeneratedCsv(
    @Res() response: Response,
    @Query() query: { id: string },
  ): Promise<any> {
    const readStream = await this.importTagService.getGeneratedCsv(query.id);

    if (query.id === 'dictionary') {
      response.set(
        disposition,
        'attachment; filename=ImportTag - ' + query.id + '.xlsx',
      );
      response.set(
        contentType,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
    } else {
      response.set(
        disposition,
        'attachment; filename=ImportTag - ' + query.id + '.csv',
      );
      response.set(contentType, 'text/plain');
    }

    readStream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importTagService.getDetailsPath(params.id);
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      disposition,
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set(contentType, 'text/plain');
    file.pipe(response);
  }
}
