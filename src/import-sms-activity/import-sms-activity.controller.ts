import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import { CsvImport } from 'src/common/decorators/csv-import-large-file.decorator';
import { checkIfExists } from '../common/helpers/check-if-exists';
import { SmsActivityDTO } from './dto/sms-activity.dto';
import { UploadDTO } from './dto/upload.dto';

import { ImportSmsActivityService } from './import-sms-activity.service';

@Controller('admin/import-sms-activity')
export class ImportSmsActivityController {
  constructor(private importSmsActivityService: ImportSmsActivityService) {}

  @Post()
  @CsvImport('file')
  async import(@Body() uploadDTO: UploadDTO) {
    const { importFile, userID, ActivityTimeFrom, ActivityTimeTo } = uploadDTO;

    return this.importSmsActivityService.importLargeFile(
      importFile,
      userID,
      SmsActivityDTO,
      {
        ActivityTimeFrom,
        ActivityTimeTo,
      },
    );
  }

  @Get()
  async template(@Res() response: Response, @Query() query: { id: string }) {
    const stream = await this.importSmsActivityService.getGeneratedCsv(
      query.id,
    );

    response.set(
      'Content-disposition',
      'attachment; filename=import-sms-activity - ' + query.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');

    stream.pipe(response);
  }

  @Get(':id')
  async detalization(
    @Res() response: Response,
    @Param() params: { id: number },
  ) {
    const output = await this.importSmsActivityService.getDetailsPath(
      params.id,
    );
    if (!output) {
      throw new NotFoundException('Detalization file not found');
    }

    const filepath = path.join(process.cwd(), output);
    if (!(await checkIfExists(filepath))) {
      throw new NotFoundException('Detalization file not found');
    }

    const file = fs.createReadStream(path.join(process.cwd(), output));
    response.set(
      'Content-disposition',
      'attachment; filename=details-' + params.id + '.csv',
    );
    response.set('Content-Type', 'text/plain');
    file.pipe(response);
  }
}
