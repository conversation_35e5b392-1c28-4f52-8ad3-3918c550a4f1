import { HttpService, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AmqpConnectionManager } from 'amqp-connection-manager';
import { Redis } from 'ioredis';
import { Logger } from 'nestjs-pino';

import { RABBITMQ } from 'src/common/rabbitmq/constants';
import { ApiClient } from 'src/common/api.client';
import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogEnum } from 'src/entities/enum/catalog.enum';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { In, QueryRunner } from 'typeorm';
import { BaseImportService } from '../common/imports/base-import.service';
import { REDIS } from '../common/redis/constants';
import { Comment } from '../entities/activity/comment.entity';
import { History } from '../entities/activity/history.entity';
import { SMSService } from '../entities/activity/sms-service.entity';
import { CatalogNameEnum } from '../entities/enum/catalog-name.enum';
import { EventsGateway } from '../events/events.gateway';
import { ImportPublisher } from '../events/publishers/import-publisher';
import { ImportSmsActivityPublisher } from '../events/publishers/import-sms-activity-publisher';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { SmsActivityDTO } from './dto/sms-activity.dto';

const NEW_ACTIVITY_NUMBER = 'Number of inserted activities';

@Injectable()
export class ImportSmsActivityService extends BaseImportService<SmsActivityDTO> {
  catalog: CatalogEnum = CatalogEnum.SmsActivity;
  protected catalogName: CatalogNameEnum = CatalogNameEnum.SmsActivity;
  protected publisher: ImportPublisher<any> = new ImportSmsActivityPublisher(
    this.connection,
  );

  constructor(
    @Inject(RABBITMQ)
    protected readonly connection: AmqpConnectionManager,
    protected readonly serviceParameterRepository: ServiceParameterRepository,
    protected readonly uploadHistoryRepository: UploadHistoryRepository,
    protected readonly config: ConfigService,
    protected readonly messageGateway: EventsGateway,
    @Inject(REDIS)
    protected readonly redis: Redis,
    protected readonly logger: Logger,
    protected readonly validatorDataAccess: ValidatorDataAccess,
    protected readonly apiClient: ApiClient,
    protected readonly httpService: HttpService,
    protected readonly catalogColumnRepository: CatalogColumnRepository,
  ) {
    super(
      connection,
      config,
      uploadHistoryRepository,
      messageGateway,
      redis,
      logger,
      validatorDataAccess,
      apiClient,
      httpService,
      catalogColumnRepository,
    );
  }

  preValidate = undefined;

  async handleSavePageResult(
    queryRunner: QueryRunner,
    pageResult: string,
    id: number,
  ): Promise<void> {
    const result = JSON.parse(pageResult);
    const importActivities = result?.importActivities;
    const importComments = result?.importComments;
    const importSmsServices = result?.importSmsServices;

    if (this.resultDetalization[id].length === 0) {
      this.resultDetalization[id] = [
        {
          [NEW_ACTIVITY_NUMBER]: 0,
        },
      ];
    }

    const detalization = {
      [NEW_ACTIVITY_NUMBER]:
        this.resultDetalization[id][0][NEW_ACTIVITY_NUMBER],
    };

    if (importActivities.length > 0) {
      const chunks = this.sliceIntoChunks(importActivities, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(History, chunk);
      }
      detalization[NEW_ACTIVITY_NUMBER] += importActivities.length;
    }

    if (importComments.length > 0) {
      const chunks = this.sliceIntoChunks(importComments, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(Comment, chunk);
      }
    }

    if (importSmsServices.length > 0) {
      const chunks = this.sliceIntoChunks(importSmsServices, 500);
      for (const chunk of chunks) {
        await queryRunner.manager.save(SMSService, chunk);
      }
    }

    this.resultDetalization[id][0][NEW_ACTIVITY_NUMBER] =
      detalization[NEW_ACTIVITY_NUMBER];
  }
}
