import { Transform } from 'class-transformer';
import {
  IsNumber,
  IsNumberString,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';
import { SmsActivityDTO } from './sms-activity.dto';
export class UploadDTO {
  @Transform(({ value }) => {
    if (IsNumberString(value)) {
      return Number(value);
    }
    return value;
  })
  @IsNumber()
  userID: number;

  @IsString()
  @Matches(/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/, {
    message: 'ActivityTimeFrom must be in the format hh:mm:ss',
  })
  ActivityTimeFrom: string;

  @IsString()
  @Matches(/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/, {
    message: 'ActivityTimeTo must be in the format hh:mm:ss',
  })
  ActivityTimeTo: string;

  @ValidateNested({ each: true })
  importData: SmsActivityDTO[];

  importFile: string;
}
