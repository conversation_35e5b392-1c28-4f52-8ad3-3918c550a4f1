import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'CatalogColumn', schema: 'Import' })
export class CatalogColumn {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: string;

  @Column({ name: 'CatalogID', type: 'smallint' })
  public catalogId: number;

  @Column({ name: 'ColumnID', type: 'smallint' })
  public columnId: number;

  @Column({ name: 'Validation', type: 'jsonb' })
  public validation: Record<string, any>;

  @Column({ name: 'Priority', type: 'smallint', default: 10 })
  public priority: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
