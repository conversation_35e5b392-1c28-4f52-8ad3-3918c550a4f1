import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'ColumnToValidation', schema: 'Import' })
export class ColumnToValidation {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: string;

  @Column({ name: 'ColumnID', type: 'smallint' })
  public columnId: number;

  @Column({ name: 'ValidationID', type: 'smallint' })
  public validationId: number;

  @Column({ name: 'Params', type: 'jsonb' })
  public params: Record<string, any>;

  @Column({ name: 'SkipOnEmpty', type: 'boolean', default: true })
  public skipOnEmpty: boolean;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'DependentColumn', type: 'jsonb', nullable: true })
  public dependentColumn: Record<string, any>;
}
