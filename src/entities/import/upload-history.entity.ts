import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ActionToImportHistory } from '../data/action-to-import-history.entity';
import { UploadStatus } from './upload-status.entity';

@Entity({ name: 'UploadHistory', schema: 'Import' })
export class UploadHistory {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: string;

  @Column({ name: 'ImportListID', type: 'smallint' })
  public importListId: number;

  @Column({ name: 'StatusID', type: 'smallint' })
  public statusId: number;

  @Column({ name: 'InsertedUserID', type: 'smallint' })
  public insertedUserId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'ResultFilePath', type: 'varchar' })
  public resultFilePath: string | null;

  @OneToOne(() => UploadStatus)
  @JoinColumn({ name: 'StatusID' })
  status: UploadStatus;

  @OneToMany(() => ActionToImportHistory, (atih) => atih.import)
  actionToImportHistory: ActionToImportHistory[];
}
