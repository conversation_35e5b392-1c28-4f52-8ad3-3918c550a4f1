import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'CloneCase', schema: 'Activity' })
export class CloneCase {
  @PrimaryGeneratedColumn('identity', {
    name: 'HistoryID',
    type: 'bigint',
    generatedIdentity: 'BY DEFAULT',
  })
  public historyId: string;

  @Column({ name: 'NewCaseID', type: 'bigint', nullable: false })
  public newCaseID: string;

  @Column({ name: 'NewPackageID', type: 'bigint', nullable: false })
  public newPackageID: string;
}
