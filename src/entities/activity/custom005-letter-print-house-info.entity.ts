import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom005LetterPrintHouseInfo', schema: 'Activity' })
export class Custom005LetterPrintHouseInfo {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'HistoryID', type: 'bigint', nullable: false })
  public historyId: string;

  @Column({ name: 'CaseID', type: 'bigint', nullable: false })
  public caseId: number;

  @Column({ name: 'AddressCheckDigit', type: 'varchar', nullable: false })
  public addressCheckDigit: string;

  @Column({ name: 'DebtorFullName', type: 'varchar', nullable: true })
  public debtorFullName: string | null;

  @Column({ name: 'PrintDate', type: 'date', nullable: true })
  public printDate: string | null;

  @Column({ name: 'ActualOrderName', type: 'varchar', nullable: true })
  public actualOrderName: string | null;

  @Column({ name: 'LetterSenderID', type: 'smallint' })
  public letterSenderId: null | number | string;

  @Column({ name: 'AWBNumber', type: 'varchar', nullable: true })
  public awbNumber: string | null;

  @Column({ name: 'PostDate', type: 'date', nullable: true })
  public postDate: string | null;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
