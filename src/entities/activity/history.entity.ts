import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'History', schema: 'Activity' })
export class History {
  @PrimaryGeneratedColumn('identity', {
    name: 'ID',
    type: 'bigint',
    generatedIdentity: 'BY DEFAULT',
  })
  public id: string;

  @Column({ name: 'TypeID', type: 'smallint', nullable: false })
  public typeId: number;

  @Column({ name: 'CaseID', type: 'bigint', nullable: false })
  public caseId: number;

  @Column({ name: 'PhoneID', type: 'bigint' })
  public phoneId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'CreationUserID', type: 'integer' })
  public creationUserID: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserID: number;

  @Column({
    name: 'CreationDate',
    type: 'timestamp',
    default: () => 'now()',
  })
  public creationDate: Date;
}
