import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'HistoryParameter', schema: 'Activity' })
export class HistoryParameter {
  @PrimaryGeneratedColumn({
    name: 'ID',
    type: 'bigint',
  })
  public id: string;

  @Column({
    name: 'HistoryID',
    type: 'bigint',
  })
  public historyId: string;

  @Column({ name: 'ParameterID', type: 'smallint', nullable: false })
  public parameterId: number;

  @Column({ name: 'Value', type: 'jsonb' })
  public value: any;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;
}
