import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'CaseChangeLog', schema: 'Activity' })
export class CaseChangeLog {
  @PrimaryColumn({
    name: 'HistoryID',
    type: 'bigint',
  })
  public historyId: string;

  @Column({ name: 'Value', type: 'jsonb' })
  public value: Record<string, any> | null;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
