import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'EventRuleExecutionLog', schema: 'List' })
export class EventRuleLog {
  @PrimaryColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'EventRuleIDs', type: 'jsonb' })
  public EventRuleIDs: number[];

  @Column({ name: 'LevelID', type: 'smallint' })
  public LevelID: number;

  @Column({ name: 'ToolSourceID', type: 'smallint' })
  public ToolSourceID: number;

  @Column({ name: 'LevelValue', type: 'bigint' })
  public LevelValue: number;

  @Column({ name: 'StatusID', type: 'smallint' })
  public StatusID: number;

  @Column({ name: 'ConditionValue', type: 'jsonb' })
  public ConditionValue: Record<string, any>;

  @Column({ name: 'ActionValue', type: 'jsonb' })
  public ActionValue: number[];

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public IsDeleted: number;

  @Column({ name: 'Inserted', type: 'timestamp' })
  public Inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({ name: 'Updated', type: 'timestamp' })
  public Updated: Date;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public UpdatedUserID: number;
}
