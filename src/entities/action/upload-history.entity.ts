import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ActionToImportHistory } from '../data/action-to-import-history.entity';
import { Catalog } from './catalog.entity';
import { UploadStatus } from './upload-status.entity';

@Entity({ name: 'UploadHistory', schema: 'Action' })
export class UploadHistory {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: string;

  @Column({ name: 'CatalogID', type: 'smallint' })
  public catalogId: number;

  @Column({ name: 'StatusID', type: 'smallint' })
  public statusId: number;

  @Column({ name: 'InsertedUserID', type: 'smallint' })
  public insertedUserId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @OneToOne(() => UploadStatus)
  @JoinColumn({ name: 'StatusID' })
  status: UploadStatus;

  @OneToOne(() => Catalog)
  @JoinColumn({ name: 'CatalogID' })
  catalog: Catalog;

  @OneToMany(() => ActionToImportHistory, (atih) => atih.action)
  actionToImportHistory: ActionToImportHistory[];
}
