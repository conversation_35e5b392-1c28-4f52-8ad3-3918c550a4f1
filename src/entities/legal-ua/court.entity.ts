import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Court', schema: 'LegalUA' })
export class Court {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'Name', type: 'varchar', length: 70, nullable: false })
  public Name: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0, nullable: false })
  public IsDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
    nullable: false,
  })
  public Inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
    nullable: false,
  })
  public Updated: Date;

  @Column({ name: 'TypeID', type: 'integer', default: 1, nullable: false })
  public TypeID: number;

  @Column({ name: 'CityCode', type: 'varchar', length: 6, nullable: false })
  public CityCode: string;

  @Column({ name: 'RegionID', type: 'integer', default: 1, nullable: false })
  public RegionID: number;

  @Column({ name: 'City', type: 'varchar', length: 200, nullable: false })
  public City: string;

  @Column({ name: 'District', type: 'varchar', length: 200, nullable: true })
  public District: string | null;

  @Column({ name: 'Street', type: 'varchar', length: 200, nullable: false })
  public Street: string;

  @Column({ name: 'House', type: 'varchar', length: 200, nullable: false })
  public House: string;

  @Column({ name: 'Building', type: 'varchar', length: 200, nullable: true })
  public Building: string | null;

  @Column({ name: 'Apartment', type: 'varchar', length: 200, nullable: true })
  public Apartment: string | null;

  @Column({
    name: 'ChangeCourtID',
    type: 'integer',
    nullable: true,
  })
  public ChangeCourtID: number | null;

  @Column({ name: 'Receiver', type: 'varchar', length: 200, nullable: false })
  public Receiver: string;

  @Column({
    name: 'InvoiceNumber',
    type: 'varchar',
    length: 200,
    nullable: false,
  })
  public InvoiceNumber: string;

  @Column({
    name: 'ReceiverCode',
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  public ReceiverCode: string;

  @Column({
    name: 'PaymentDestination',
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  public PaymentDestination: string;

  @Column({ name: 'PhoneNumber', type: 'varchar', length: 100, nullable: true })
  public PhoneNumber: string | null;

  @Column({ name: 'Email', type: 'varchar', length: 200, nullable: true })
  public Email: string | null;

  @Column({
    name: 'InsertedUserID',
    type: 'integer',
    nullable: false,
  })
  public InsertedUserID: number;

  @Column({
    name: 'UpdatedUserID',
    type: 'integer',
    nullable: false,
  })
  public UpdatedUserID: number;
}
