import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Invoice as MainInvoice } from '../data/invoice.entity';

@Entity({ name: 'Invoice', schema: 'LegalUA' })
export class Invoice {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'InvoiceID', type: 'bigint' })
  public invoiceId: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: number;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;

  @ManyToOne(() => MainInvoice, (model) => model.legalInvoices)
  @JoinColumn({ name: 'InvoiceID', referencedColumnName: 'id' })
  public mainInvoice: MainInvoice;
}
