import { Column, <PERSON>tity, OneToOne, PrimaryColumn } from 'typeorm';
import { Case } from './case.entity';

@Entity({ name: 'ContactPerson', schema: 'Data' })
export class ContactPerson {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'TypeID', type: 'smallint' })
  public typeId: number;

  @Column({ name: 'DebtorTypeID', type: 'smallint', default: 1 })
  public debtorTypeId: number;

  @Column({ name: 'DataSourceID', type: 'smallint', default: null })
  public dataSourceId: number;

  @Column({ name: 'LastName', type: 'varchar', length: 255 })
  public lastName: string;

  @Column({ name: 'FirstName', type: 'varchar', length: 255 })
  public firstName: string | null;

  @Column({ name: 'MiddleName', type: 'varchar', length: 255 })
  public middleName: string | null;

  @Column({ name: 'PassportSeries', type: 'varchar', length: 2 })
  public passportSeries: string | null;

  @Column({ name: 'PassportNumber', type: 'varchar', length: 200 })
  public passportNumber: string | null;

  @Column({ name: 'PassportInfo', type: 'varchar', length: 255 })
  public passportInfo: string | null;

  @Column({ name: 'TaxNumber', type: 'varchar', length: 280 })
  public taxNumber: string | null;

  @Column({ name: 'BirthDate', type: 'date' })
  public birthDate: string | null;

  @Column({ name: 'Value', type: 'jsonb' })
  public value: Record<string, any> | null;

  @Column({ name: 'Note', type: 'varchar', length: 200 })
  public note: string | null;

  @Column({ name: 'IsDebtor', type: 'boolean', default: false })
  public isDebtor: boolean;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @OneToOne(() => Case)
  public case: Case;
}
