import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'DocumentAdditionalInfo', schema: 'Data' })
export class DocumentAdditionalInfo {
  @PrimaryColumn({ name: 'DocumentID', type: 'bigint' })
  public documentId: string;

  @Column({
    name: 'SourceID',
    type: 'smallint',
  })
  public sourceId: number;

  @Column({
    name: 'ReplyTypeID',
    type: 'smallint',
  })
  public replyTypeId: number;

  @Column({
    name: 'UploadDate',
    type: 'date',
  })
  public uploadDate: string;

  @Column({
    name: 'IsHidden',
    type: 'smallint',
  })
  public isHidden: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer', nullable: true })
  public insertedUserId: number | null;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number | null;
}
