import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Invoice as LegalInvoice } from '../legal-ua/invoice.entity';
import { Case } from './case.entity';

@Entity({ name: 'Invoice', schema: 'Data' })
export class Invoice {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: string;

  @Column({ name: 'InvoiceNum', type: 'varchar', length: 200 })
  public invoiceNum: string;

  @Column({ name: 'AccountNum', type: 'varchar', length: 100 })
  public accountNum: string;

  @Column({ name: 'InvoiceAgreementCloseDate', type: 'date', nullable: true })
  public invoiceAgreementCloseDate: string | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'IsDeleted', type: 'smallint', default: () => '0' })
  public isDeleted: number;

  @ManyToOne(() => Case, (model) => model.invoices)
  @JoinColumn({ name: 'CaseID', referencedColumnName: 'id' })
  public case: Case;

  @OneToMany(() => LegalInvoice, (invoice) => invoice.mainInvoice)
  @JoinColumn({ name: 'ID', referencedColumnName: 'invoiceId' })
  legalInvoices: LegalInvoice[];
}
