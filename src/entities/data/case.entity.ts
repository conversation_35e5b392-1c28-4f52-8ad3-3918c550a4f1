import {
  Column,
  <PERSON><PERSON><PERSON>,
  Join<PERSON>olumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { Debtor } from './debtor.entity';
import { Invoice } from './invoice.entity';
import { Package } from './package.entity';
import { ContactPerson } from './contact-person.entity';

@Entity({ name: 'Case', schema: 'Data' })
export class Case {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({
    name: 'CheckDigit',
    type: 'character varying',
    nullable: true,
    length: 2,
  })
  public checkDigit: string | null;

  @Column({ name: 'DebtorID', type: 'bigint' })
  public debtorId: string;

  @Column({ name: 'PackageID', type: 'integer' })
  public packageId: number;

  @Column({
    name: 'ContragentCaseID',
    type: 'character varying',
    nullable: true,
    length: 100,
  })
  public contragentCaseId: string | null;

  @Column({ name: 'StatusID', type: 'smallint' })
  public statusId: number;

  @Column({ name: 'StageID', type: 'smallint', default: () => '1' })
  public stageId: number;

  @Column({ name: 'CurrencyID', type: 'smallint' })
  public currencyId: number;

  @Column({ name: 'CaseProcess', type: 'smallint' })
  public caseProcess: number;

  @Column({ name: 'ReasonID', type: 'smallint', nullable: true })
  public reasonId: number | null;

  @Column({ name: 'AbrogateDate', type: 'date', nullable: true })
  public abrogateDate: string | null;

  @Column({ name: 'CloseDate', type: 'date', nullable: true })
  public closeDate: string | null;

  @Column({ name: 'OrigDPD', type: 'integer', nullable: true })
  public origDpd: number | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer', nullable: true })
  public insertedUserId: number | null;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number | null;

  @Column({ name: 'AdditionalInfo', type: 'jsonb', nullable: true })
  public additionalInfo: Record<string, any> | null;

  @Column({ name: 'MergingRuleID', type: 'smallint', nullable: true })
  public mergingRuleId: number | null;

  @Column({ name: 'IsDeleted', type: 'smallint', default: () => '0' })
  public isDeleted: number;

  @Column({ name: 'ScoringDetalization', type: 'jsonb', nullable: true })
  public scoringDetalization: Record<string, any> | null;

  @Column({ name: 'InitialScoringDetalization', type: 'jsonb', nullable: true })
  public initialScoringDetalization: Record<string, any> | null;

  @Column({ name: 'ParentDebtorID', type: 'bigint', nullable: true })
  public parentDebtorId: string | null;

  @Column({ name: 'StatusReasonID', type: 'smallint', nullable: true })
  public statusReasonId: number | null;

  @OneToMany(() => Invoice, (invoice) => invoice.case)
  @JoinColumn({ name: 'ID', referencedColumnName: 'caseId' })
  invoices: Invoice[];

  @OneToOne(() => Package)
  @JoinColumn({ name: 'PackageID', referencedColumnName: 'id' })
  package: Package;

  @ManyToOne(() => Debtor, (model) => model.cases)
  @JoinColumn({ name: 'DebtorID', referencedColumnName: 'id' })
  public debtor: Debtor;

  @OneToOne(() => ContactPerson)
  @JoinColumn({ name: 'DebtorID', referencedColumnName: 'id' })
  public contactPerson: ContactPerson;
}
