import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom001Debtor', schema: 'Data' })
export class Custom001Debtor {
  @PrimaryGeneratedColumn({ name: 'DebtorID', type: 'bigint' })
  public debtorId: string;

  @Column({ name: 'DeathDate', type: 'date' })
  public deathDate: string | null;

  @Column({ name: 'RegistrationNumber', type: 'varchar', length: 100 })
  public registrationNumber: string | null;
}
