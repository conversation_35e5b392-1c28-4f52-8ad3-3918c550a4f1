import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'ContactPersonRelation', schema: 'Data' })
export class ContactPersonRelationEntity {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'ParentContactPersonID', type: 'bigint' })
  public parentContactPersonId: number;

  @Column({ name: 'ContactPersonID', type: 'bigint' })
  public contactPersonId: number;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;
}
