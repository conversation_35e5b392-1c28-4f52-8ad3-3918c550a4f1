import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'TransferToCollectionAgency', schema: 'Data' })
export class TransferToCollectionAgency {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'int' })
  public ID: number;

  @Column({ name: 'InvoiceID', type: 'bigint' })
  public InvoiceID: string;

  @Column({ name: 'TransferDate', type: 'date' })
  public TransferDate: string | null;

  @Column({ name: 'CollectionAgencyName', type: 'varchar' })
  public CollectionAgencyName: string | null;

  @Column({ name: 'CollectionAgencyAddress', type: 'varchar' })
  public CollectionAgencyAddress: string | null;

  @Column({ name: 'CollectionAgencyEmail', type: 'varchar' })
  public CollectionAgencyEmail: string | null;

  @Column({ name: 'EDRPOU', type: 'int' })
  public EDRPOU: string | null;

  @Column({ name: 'IsDeleted', type: 'int' })
  public IsDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public Inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public Updated: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public UpdatedUserID: number;
}
