import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'AdditionalNote', schema: 'Data' })
export class AdditionalNote {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'AttributeID', type: 'smallint' })
  public attributeId: number;

  @Column({ name: 'AttributeValue', type: 'bigint' })
  public attributeValue: number;

  @Column({ name: 'FieldNumber', type: 'smallint' })
  public fieldNumber: number;

  @Column({ name: 'Value', type: 'varchar', length: 1000 })
  public value: string;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
