import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Custom001ContactPerson', schema: 'Data' })
export class Custom001ContactPerson {
  @PrimaryColumn({ name: 'ContactPersonID', type: 'bigint' })
  public contactPersonId: string;

  @Column({ name: 'BirthName', type: 'varchar', length: 100 })
  public birthName: string;

  @Column({ name: 'BirthPlace', type: 'varchar', length: 100 })
  public birthPlace: string;

  @Column({ name: 'MotherName', type: 'varchar', length: 100 })
  public motherName: string;

  @Column({ name: 'RegistrationNumber', type: 'varchar', length: 200 })
  public registrationNumber: string | null;

  @Column({ name: 'IsDied', type: 'boolean', default: false })
  public isDied: boolean;

  @Column({ name: 'DeathDate', type: 'date' })
  public deathDate: string | null;

  @Column({ name: 'ProcedureFromDate', type: 'date' })
  public procedureFromDate: string | null;

  @Column({ name: 'CEO<PERSON><PERSON>', type: 'varchar', length: 100 })
  public ceoName: string;

  @Column({ name: 'ContactPersonName', type: 'varchar', length: 100 })
  public contactPersonName: string;

  @Column({ name: 'OriginalLastName', type: 'varchar', length: 255 })
  public originalLastName: string;

  @Column({ name: 'OriginalFirstName', type: 'varchar', length: 100 })
  public originalFirstName: string;

  @Column({ name: 'OriginalMiddleName', type: 'varchar', length: 100 })
  public originalMiddleName: string;

  @Column({ name: 'ProcedureTypeID', type: 'smallint' })
  public procedureTypeId: number;

  @Column({ name: 'SexID', type: 'smallint', default: 3 })
  public sexId: number;

  @Column({ name: 'PassportIssuingDate', type: 'date' })
  public passportIssuingDate: string | null;

  @Column({ name: 'PersonDocumentTypeID', type: 'smallint' })
  public personDocumentTypeId: number;

  @Column({ name: 'LegalEntityName', type: 'varchar', length: 100 })
  public legalEntityName: string;
}
