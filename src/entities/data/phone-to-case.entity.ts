import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'PhoneToCase', schema: 'Data' })
export class PhoneToCase {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id?: number;

  @Column({ name: 'PhoneID', type: 'bigint' })
  public phoneId: string;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: string;

  @Column({ name: 'Deactivated', type: 'smallint', default: 0 })
  public deactivated: number;

  @Column({ name: 'Inserted', type: 'timestamp', default: () => 'now()' })
  public inserted?: Date;

  @Column({ name: 'Updated', type: 'timestamp', default: () => 'now()' })
  public updated?: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId?: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId?: number;

  @Column({ name: 'DataSourceID', type: 'smallint' })
  public dataSourceId?: number;

  @Column({ name: 'ToolSourceID', type: 'smallint' })
  public toolSourceId?: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'DeactivatedDate', type: 'timestamp' })
  public deactivatedDate?: Date;

  @Column({ name: 'DeactivatedUserID', type: 'integer' })
  public deactivatedUserId?: number;

  @Column({ name: 'ReactivatedDate', type: 'timestamp' })
  public reactivatedDate?: Date;

  @Column({ name: 'ReactivatedUserID', type: 'integer' })
  public reactivatedUserId?: number;
}
