import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'CourtProcessParameter', schema: 'Data' })
export class CourtProcessParameter {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({
    name: 'CourtProcessID',
    type: 'integer',
  })
  public courtProcessId: number;

  @Column({
    name: 'ParameterID',
    type: 'smallint',
  })
  public parameterId: number;

  @Column({ name: 'Value', type: 'jsonb', nullable: true })
  public value: Record<string, any> | null;

  @Column({
    name: 'IsDeleted',
    type: 'smallint',
  })
  public isDeleted: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;
}
