import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Phone', schema: 'Data' })
export class Phone {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'DebtorID', type: 'bigint' })
  public debtorId: string;

  @Column({ name: 'PhoneNumber', type: 'varchar', length: 15 })
  public phoneNumber: string | null;

  @Column({ name: 'TypeID', type: 'smallint' })
  public typeId: number;

  @Column({ name: 'PartID', type: 'smallint' })
  public partId: number;

  @Column({ name: 'Priority', type: 'smallint', default: 100 })
  public priority?: number;

  @Column({ name: 'IsMobile', type: 'smallint', default: 0 })
  public isMobile?: number;

  @Column({ name: 'TimeZone', type: 'smallint' })
  public timeZone?: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted?: number;

  @Column({ name: 'PhoneNote', type: 'varchar', length: 1000 })
  public phoneNote: string;

  @Column({ name: 'Inserted', type: 'timestamp', default: () => 'now()' })
  public inserted?: Date;

  @Column({ name: 'Updated', type: 'timestamp', default: () => 'now()' })
  public updated?: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId?: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId?: number;

  @Column({ name: 'ECollectPhoneID', type: 'bigint' })
  public eCollectPhoneId?: string;

  @Column({ name: 'DataSourceID', type: 'smallint' })
  public dataSourceId?: number;

  @Column({ name: 'ContactStatusID', type: 'smallint' })
  public contactStatusId?: number;

  @Column({ name: 'DeactivatedToDate', type: 'date' })
  public deactivatedToDate?: Date;

  @Column({ name: 'DeactivatedDate', type: 'timestamp' })
  public deactivatedDate?: Date;

  @Column({ name: 'DeactivatedUserID', type: 'integer' })
  public deactivatedUserId?: number;

  @Column({ name: 'InProcessing', type: 'smallint', default: 0 })
  public inProcessing: number;

  @Column({ name: 'ReactivatedDate', type: 'timestamp' })
  public reactivatedDate?: Date;

  @Column({ name: 'ReactivatedUserID', type: 'integer' })
  public reactivatedUserId?: number;

  @Column({ name: 'UpdatedByUser', type: 'timestamp' })
  public updatedByUser?: Date;

  @Column({ name: 'DeactivationReasonID', type: 'smallint' })
  public deactivationReasonId?: number;

  @Column({ name: 'UpdatedIsDeleted', type: 'smallint' })
  public updatedIsDeleted?: Date;
}
