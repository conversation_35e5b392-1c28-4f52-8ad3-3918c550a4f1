import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'CaseStageChangeLog', schema: 'Data' })
export class CaseStageChangeLog {
  @PrimaryColumn({ name: 'ID', type: 'int' })
  public id: string;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: number;

  @Column({ name: 'StageID', type: 'smallint' })
  public stageId: number;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;

  @Column({ name: 'InsertedUserID', type: 'int' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'int' })
  public updatedUserId: number;

  @Column({
    name: 'FromDate',
    type: 'timestamp',
  })
  public fromDate: Date;

  @Column({
    name: 'ToDate',
    type: 'timestamp',
  })
  public toDate: Date;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
