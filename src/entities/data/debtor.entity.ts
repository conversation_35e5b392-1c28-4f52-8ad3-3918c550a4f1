import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, PrimaryColumn } from 'typeorm';
import { Case } from './case.entity';

@Entity({ name: 'Debtor', schema: 'Data' })
export class Debtor {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'TypeID', type: 'smallint' })
  public typeId: number;

  @Column({ name: 'LastName', type: 'varchar', length: 200 })
  public lastName: string;

  @Column({ name: 'FirstName', type: 'varchar', length: 200 })
  public firstName: string;

  @Column({ name: 'MiddleName', type: 'varchar', length: 200 })
  public middleName: string;

  @Column({ name: 'IsDied', type: 'smallint', default: 0 })
  public isDied: number;

  @Column({ name: 'SexID', type: 'smallint', default: 3 })
  public sexId: number;

  @Column({ name: 'TaxNumber', type: 'varchar', length: 280 })
  public taxNumber: string;

  @Column({ name: 'BirthDate', type: 'date' })
  public birthDate: string | null;

  @Column({ name: 'BirthPlace', type: 'varchar', length: 50 })
  public birthPlace: string;

  @Column({ name: 'PersAccPassword', type: 'integer' })
  public persAccPassword: number;

  @Column({ name: 'PassportSeries', type: 'varchar', length: 20 })
  public passportSeries: string;

  @Column({ name: 'PassportNumber', type: 'varchar', length: 200 })
  public passportNumber: string;

  @Column({ name: 'PassportInfo', type: 'varchar', length: 255 })
  public passportInfo: string;

  @Column({ name: 'GroupID', type: 'integer' })
  public groupId: number;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;

  @Column({ name: 'CaseID2', type: 'integer' })
  public caseId2: number;

  @Column({ name: 'PersonDocumentTypeID', type: 'smallint', default: 1 })
  public personDocumentTypeId: number;

  @Column({ name: 'PassportIssuingDate', type: 'date' })
  public passportIssuingDate: string | null;

  @Column({ name: 'CodeIssuingAuthority', type: 'varchar', length: 30 })
  public codeIssuingAuthority: string;

  @Column({ name: 'WorkPlace', type: 'varchar' })
  public workPlace: string;

  @Column({ name: 'Position', type: 'varchar' })
  public position: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @OneToMany(() => Case, (model) => model.debtor)
  @JoinColumn({ name: 'ID', referencedColumnName: 'debtorId' })
  cases: Case[];
}
