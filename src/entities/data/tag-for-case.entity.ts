import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'TagForCase', schema: 'Data' })
export class TagForCase {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: number;

  @Column({ name: 'TagID', type: 'integer' })
  public tagId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'ValidTo', type: 'date' })
  public validTo: string;

  @Column({ name: 'DeletionSourceID', type: 'smallint', default: null })
  public deletionSourceId: number | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
