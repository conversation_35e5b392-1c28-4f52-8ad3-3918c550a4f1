import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'DocumentParameter', schema: 'Data' })
export class DocumentParameter {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'DocumentID', type: 'integer' })
  public documentId: number;

  @Column({ name: 'ParameterID', type: 'smallint' })
  public parameterId: number;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;

  @Column({ name: 'Value', type: 'jsonb' })
  public value: number | string;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;
}
