import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Custom005Document', schema: 'Data' })
export class Custom005Document {
  @PrimaryColumn({ name: 'DocumentID', type: 'bigint' })
  public documentId: string;

  @Column({
    name: 'DateOfDocument',
    type: 'date',
  })
  public dateOfDocument: string;

  @Column({
    name: 'DueDate',
    type: 'date',
  })
  public dueDate: string;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
