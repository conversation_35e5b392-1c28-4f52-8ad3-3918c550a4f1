import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Document', schema: 'Data' })
export class Document {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({
    name: 'IsDeleted',
    type: 'smallint',
  })
  public isDeleted: number;

  @Column({
    name: 'LevelID',
    type: 'smallint',
  })
  public levelId: number;

  @Column({
    name: 'FileID',
    type: 'bigint',
  })
  public fileId: string;

  @Column({
    name: 'TypeID',
    type: 'smallint',
  })
  public typeId: number;

  @Column({
    name: 'LevelValue',
    type: 'bigint',
  })
  public levelValue: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer', nullable: true })
  public insertedUserId: number | null;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number | null;
}
