import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Custom005CaseAdditionalInfo', schema: 'Data' })
export class Custom005CaseAdditionalInfo {
  @PrimaryColumn({ name: 'CaseID', type: 'bigint' })
  CaseID: number;

  @Column({ name: 'DateOfContracting', type: 'date', nullable: true })
  DateOfContracting: string | null;

  @Column({
    name: 'ContractStage',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ContractStage: string | null;

  @Column({ name: 'DateOfCancellation', type: 'date', nullable: true })
  DateOfCancellation: string | null;

  @Column({
    name: 'PeriodInNetwork',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PeriodInNetwork: string | null;

  @Column({
    name: 'DisconnectionReason',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  DisconnectionReason: string | null;

  @Column({
    name: 'PenaltyRate',
    type: 'numeric',
    precision: 12,
    scale: 4,
    nullable: true,
  })
  PenaltyRate: number | null;

  @Column({
    name: 'AllClientPaymentAmount',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  AllClientPaymentAmount: number | null;

  @Column({ name: 'LastClientPaymentDate', type: 'date', nullable: true })
  LastClientPaymentDate: string | null;

  @Column({
    name: 'LastClientPaymentAmount',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  LastClientPaymentAmount: number | null;

  @Column({ name: 'InitialDPD', type: 'int', nullable: true })
  InitialDPD: number | null;

  @Column({ name: 'ActualDPD', type: 'int', nullable: true })
  ActualDPD: number | null;

  @Column({ name: 'BillingDate', type: 'varchar', length: 255, nullable: true })
  BillingDate: string | null;

  @Column({
    name: 'FirstDueBill',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  FirstDueBill: string | null;

  @Column({
    name: 'LastInvoiceDueDate',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  LastInvoiceDueDate: string | null;

  @Column({ name: 'ProductType', type: 'varchar', length: 255, nullable: true })
  ProductType: string | null;

  @Column({
    name: 'ProductDescription',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ProductDescription: string | null;

  @Column({
    name: 'ProductExtraDescription',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  ProductExtraDescription: string | null;

  @Column({
    name: 'InitialDebtStructure',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  InitialDebtStructure: string | null;

  @Column({ name: 'NumberOfDueInvoices', type: 'int', nullable: true })
  NumberOfDueInvoices: number | null;

  @Column({
    name: 'ValueOfFirstDueBill',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  ValueOfFirstDueBill: number | null;

  @Column({ name: 'CountOfPhones', type: 'int', nullable: true })
  CountOfPhones: number | null;

  @Column({
    name: 'EquipmentType',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  EquipmentType: string | null;

  @Column({ name: 'CaseType', type: 'varchar', length: 255, nullable: true })
  CaseType: string | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  Inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  Updated: Date;

  @Column({ name: 'NumberOfAllInvoices', type: 'int', nullable: true })
  NumberOfAllInvoices: number | null;

  @Column({
    name: 'AdditionalClientIdentifier',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  AdditionalClientIdentifier: string | null;

  @Column({
    name: 'InvoicePaymentCode',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  InvoicePaymentCode: string | null;

  @Column({
    name: 'ClientSegmentRisk',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  ClientSegmentRisk: string | null;

  @Column({
    name: 'FinancedPeriod',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  FinancedPeriod: string | null;

  @Column({
    name: 'FinancedValue',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  FinancedValue: number | null;

  @Column({
    name: 'ValueOfInstallment',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  ValueOfInstallment: string | null;

  @Column({ name: 'Bucket', type: 'varchar', length: 500, nullable: true })
  Bucket: string | null;

  @Column({
    name: 'FinancingStore',
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  FinancingStore: string | null;

  @Column({
    name: 'NumberFirstOverdueInstallment',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  NumberFirstOverdueInstallment: number | null;

  @Column({
    name: 'PaperFileAuthorizationNumber',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PaperFileAuthorizationNumber: string | null;

  @Column({
    name: 'TotalAmountToCloseLoan',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  TotalAmountToCloseLoan: string | null;

  @Column({
    name: 'PaymentMethods',
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  PaymentMethods: string | null;

  @Column({
    name: 'DateOfReconnection',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  DateOfReconnection: string | null;

  @Column({
    name: 'ElectronicInvoice',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ElectronicInvoice: string | null;

  @Column({
    name: 'TerminalInstallments',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  TerminalInstallments: string | null;

  @Column({
    name: 'OldestPhoneNo',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  OldestPhoneNo: string | null;

  @Column({ name: 'OldestPhoneNoActivationDate', type: 'date', nullable: true })
  OldestPhoneNoActivationDate: string | null;

  @Column({
    name: 'NewestPhoneNo',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  NewestPhoneNo: string | null;

  @Column({ name: 'NewestPhoneNoActivationDate', type: 'date', nullable: true })
  NewestPhoneNoActivationDate: string | null;

  @Column({
    name: 'AdditionalFinancialInfo',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  AdditionalFinancialInfo: string | null;

  @Column({
    name: 'CancelationFee',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  CancelationFee: string | null;

  @Column({
    name: 'PORTDetails',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PORTDetails: string | null;

  @Column({
    name: 'TotalRONEquivalent',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  TotalRONEquivalent: string | null;

  @Column({
    name: 'DebtorIsAlive',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  DebtorIsAlive: string | null;

  @Column({
    name: 'OverdraftAvailableBalance',
    type: 'numeric',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  OverdraftAvailableBalance: number | null;

  @Column({ name: 'NextInstallmentDate', type: 'date', nullable: true })
  NextInstallmentDate: string | null;

  @Column({
    name: 'TotalFeesOnAllAccounts',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  TotalFeesOnAllAccounts: string | null;

  @Column({
    name: 'ClientWash',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ClientWash: string | null;

  @Column({
    name: 'Collateral',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  Collateral: string | null;

  @Column({
    name: 'NewInvoiceInformation',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  NewInvoiceInformation: string | null;

  @Column({
    name: 'Occupation',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  Occupation: string | null;

  @Column({
    name: 'CaseGroupingIdentifier',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  CaseGroupingIdentifier: string | null;

  @Column({
    name: 'CaseGroupingRule',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  CaseGroupingRule: string | null;

  @Column({
    name: 'AddClosedCasesToGrouping',
    type: 'boolean',
    nullable: true,
  })
  AddClosedCasesToGrouping: boolean | null;

  @Column({
    name: 'AegrmRnpmRegistrationCode',
    type: 'varchar',
    length: 200,
    nullable: true,
  })
  AegrmRnpmRegistrationCode: string | null;

  @Column({
    name: 'ProjectIBAN',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  ProjectIBAN: string | null;

  @Column({
    name: 'CaseHasGDPRNotice',
    type: 'boolean',
    nullable: true,
  })
  CaseHasGDPRNotice: boolean | null;

  @Column({
    name: 'LastGDPRNoticeDate',
    type: 'date',
    nullable: true,
  })
  LastGDPRNoticeDate: string | null;

  @Column({
    name: 'LastGDPRNoticeSentUsing',
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  LastGDPRNoticeSentUsing: string | null;

  @Column({
    name: 'MaturityDate',
    type: 'date',
    nullable: true,
  })
  MaturityDate: string | null;

  @Column({
    name: 'TotalNumberOfContractsLoans',
    type: 'integer',
    nullable: true,
  })
  TotalNumberOfContractsLoans: number | null;

  @Column({
    name: 'InterestRate',
    type: 'numeric',
    precision: 12,
    scale: 4,
    nullable: true,
  })
  InterestRate: number | null;

  @Column({
    name: 'OpenWithBank',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  OpenWithBank: string | null;

  @Column({
    name: 'Beneficiary',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  Beneficiary: string | null;

  @Column({
    name: 'PaperFileBatch',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PaperFileBatch: string | null;

  @Column({
    name: 'PaperFileBatchDate',
    type: 'date',
    nullable: true,
  })
  PaperFileBatchDate: string | null;

  @Column({
    name: 'PaperFileStatus',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PaperFileStatus: string | null;

  @Column({
    name: 'PaperFileProblems',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  PaperFileProblems: string | null;

  @Column({
    name: 'ContractLocation',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ContractLocation: string | null;

  @Column({
    name: 'LegalSelectionDate',
    type: 'date',
    nullable: true,
  })
  LegalSelectionDate: string | null;

  @Column({
    name: 'ExclusionReason',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  ExclusionReason: string | null;

  @Column({
    name: 'BackToSoftCollectionDate',
    type: 'date',
    nullable: true,
  })
  BackToSoftCollectionDate: string | null;
}
