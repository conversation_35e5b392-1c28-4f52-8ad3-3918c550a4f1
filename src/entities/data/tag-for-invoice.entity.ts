import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'TagForInvoice', schema: 'Data' })
export class TagForInvoice {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'InvoiceID', type: 'bigint' })
  public invoiceId: number;

  @Column({ name: 'TagID', type: 'integer' })
  public tagId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'ValidTo', type: 'date' })
  public validTo: string;

  @Column({ name: 'DeletionSourceID', type: 'smallint', default: null })
  public deletionSourceId: number | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
