import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Custom005ContactPerson', schema: 'Data' })
export class Custom005ContactPerson {
  @PrimaryColumn({ name: 'ContactPersonID', type: 'bigint' })
  public contactPersonId: string;

  @Column({ name: 'CompanyFullName', type: 'varchar', length: 100 })
  public companyFullName: string | null;

  @Column({ name: 'CompanyFiscalCode', type: 'varchar', length: 100 })
  public companyFiscalCode: string | null;

  @Column({ name: 'CompanyRegistrationNumber', type: 'varchar', length: 200 })
  public companyRegistrationNumber: string | null;

  @Column({ name: 'CompanyLegalAdministrator', type: 'varchar', length: 100 })
  public companyLegalAdministrator: string | null;

  @Column({ name: 'CompanyStatusID', type: 'smallint' })
  public companyStatusID: string | null;

  @Column({ name: 'CompanyDateEstablishment', type: 'date' })
  public companyDateEstablishment: string | null;

  @Column({ name: 'CompanyDateClosing', type: 'date' })
  public CompanyDateClosing: string | null;

  @Column({ name: 'SexID', type: 'smallint', default: 3 })
  public sexId: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
