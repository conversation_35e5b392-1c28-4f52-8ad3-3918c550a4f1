import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'CaseStateHistory', schema: 'Data' })
export class CaseStateHistory {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  public id: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: number;

  @Column({ name: 'CaseStateID', type: 'bigint' })
  public caseStateId: number;

  @Column({ name: 'DateFrom', type: 'timestamp' })
  public dateFrom: string;

  @Column({ name: 'DateTo', type: 'timestamp' })
  public dateTo: string;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;

  @Column({ name: 'InsertedUserID', type: 'bigint' })
  public insertedUserID: number;

  @Column({ name: 'UpdatedUserID', type: 'bigint' })
  public updatedUserID: number;
}
