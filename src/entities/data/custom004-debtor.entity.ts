import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom004Debtor', schema: 'Data' })
export class Custom004Debtor {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'int' })
  public id: number;

  @PrimaryGeneratedColumn({ name: 'DebtorID', type: 'bigint' })
  public debtorId: string;

  @Column({ name: 'GroupUUID', type: 'varchar' })
  public groupUUID: string;

  @Column({ name: 'IsDeleted', type: 'int' })
  public isDeleted: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'DeletedUserID', type: 'integer', nullable: true })
  public deletedUserId: number | null;

  @Column({ name: 'InsertedUserID', type: 'integer', nullable: true })
  public insertedUserId: number | null;

  @Column({ name: 'ChangeGroupReasonID', type: 'integer', nullable: true })
  public changeGroupReasonId: number | null;
}
