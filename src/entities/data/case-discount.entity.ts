import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'CaseDiscount', schema: 'Data' })
export class CaseDiscount {
  @PrimaryColumn({ name: 'ID', type: 'int' })
  public id: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: string;

  @Column({ name: 'FromDate', type: 'date' })
  public fromDate: string;

  @Column({ name: 'ToDate', type: 'date' })
  public toDate: string;

  @Column({
    name: 'PayAmount',
    type: 'numeric',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  public payAmount: number | null;

  @Column({
    name: 'DiscountPercent',
    type: 'numeric',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  public discountPercent: number | null;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({
    name: 'ActualPayAmount',
    type: 'numeric',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  public actualPayAmount: number | null;

  @Column({ name: 'Note', type: 'varchar', length: 200, nullable: true })
  public note: string | null;
}
