import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'Custom004Phone', schema: 'Data' })
export class Custom004Phone {
  @PrimaryColumn({ name: 'PhoneID', type: 'int' })
  public phoneId: number;

  @Column({ name: 'PredictiveOn', type: 'boolean' })
  public predictiveOn?: boolean;

  @Column({ name: 'CanCall', type: 'boolean' })
  public canCall?: boolean;

  @Column({ name: 'CanSendSms', type: 'boolean' })
  public canSendSms?: boolean;
}
