import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'DocumentNote', schema: 'Data' })
export class DocumentNote {
  @PrimaryColumn({ name: 'DocumentID', type: 'bigint' })
  public documentId: string;

  @Column({
    name: 'Value',
    type: 'text',
    default: null,
  })
  public value: string | null;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({ name: 'InsertedUserID', type: 'integer', nullable: true })
  public insertedUserId: number | null;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number | null;
}
