import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { UploadHistory as ActionUploadHistory } from '../action/upload-history.entity';

@Entity({ name: 'ActionToImportHistory', schema: 'Data' })
export class ActionToImportHistory {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'ActionID', type: 'integer' })
  public actionId: number;

  @Column({ name: 'ImportID', type: 'integer' })
  public importId: number;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;

  @ManyToOne(
    () => ActionUploadHistory,
    (action) => action.actionToImportHistory,
  )
  @JoinColumn({ name: 'ImportID' })
  import: ActionUploadHistory;

  @ManyToOne(
    () => ActionUploadHistory,
    (action) => action.actionToImportHistory,
  )
  @JoinColumn({ name: 'ActionID' })
  action: ActionUploadHistory;
}
