import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'HistoryTypeToParameter', schema: 'Dictionary' })
export class HistoryTypeToParameter {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'HistoryTypeID', type: 'smallint' })
  public historyTypeId: number;

  @Column({ name: 'ParameterID', type: 'smallint' })
  public parameterId: number;

  @Column({ name: 'IsRequired', type: 'smallint' })
  public isRequired: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;
}
