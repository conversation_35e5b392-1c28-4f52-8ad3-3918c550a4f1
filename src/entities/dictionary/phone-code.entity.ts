import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'PhoneCode', schema: 'Dictionary' })
export class PhoneCode {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 3 })
  public name: string;

  @Column({ name: 'Description', type: 'varchar', length: 100 })
  public description: string;

  @Column({ name: 'IsMobile', type: 'smallint', default: 0 })
  public isMobile: number;

  @Column({ name: 'From', type: 'varchar', length: 7 })
  public from: string;

  @Column({ name: 'To', type: 'varchar', length: 7 })
  public to: string;

  @Column({ name: 'ReginName', type: 'varchar', length: 100 })
  public reginName: string;

  @Column({ name: 'TimeZone', type: 'smallint' })
  public timeZone: number;

  @Column({ name: 'Updated', type: 'timestamp', default: () => 'now()' })
  public updated: Date;
}
