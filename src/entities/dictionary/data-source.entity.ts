import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'DataSource', schema: 'Dictionary' })
export class DataSource {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 30 })
  public name: string;

  @Column({ name: 'Availability', type: 'jsonb' })
  public availability: Record<string, any>;
}
