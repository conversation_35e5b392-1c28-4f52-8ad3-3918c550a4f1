import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom001PhoneStatus', schema: 'Dictionary' })
export class Custom001PhoneStatus {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'bigint' })
  id: number;

  @Column({ name: 'Name', type: 'varchar', length: 100 })
  name: string;

  @Column({ name: 'Updated', type: 'timestamp', default: () => 'now()' })
  updated: Date;

  @Column({ name: 'IsDeleted', type: 'smallint', default: () => '0' })
  isDeleted: number;

  @Column({ name: 'Priority', type: 'smallint', default: () => '100' })
  priority: number;

  @Column({
    name: 'UseForFilterCaseLoader',
    type: 'smallint',
    default: () => '0',
  })
  useForFilterCaseLoader: number;
}
