import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'HistoryType', schema: 'Dictionary' })
export class HistoryType {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 100 })
  public name: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'SetCaseStatusID', type: 'smallint', default: null })
  public setCaseStatusId: number | null;

  @Column({ name: 'SetCaseStageID', type: 'smallint', default: null })
  public setCaseStageId: number | null;

  @Column({ name: 'SetCaseStatusReasonID', type: 'smallint', default: null })
  public setCaseStatusReasonId: number | null;

  @Column({ name: 'SetCaseStatusReasonNULL', type: 'boolean', default: false })
  public setCaseStatusReasonNull: boolean;
}
