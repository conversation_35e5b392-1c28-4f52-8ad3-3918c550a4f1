import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'AvailabilityOption', schema: 'Dictionary' })
export class AvailabilityOption {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 100 })
  public name: string;

  @Column({ name: 'IsDeleted', type: 'smallint' })
  public isDeleted: number;
}
