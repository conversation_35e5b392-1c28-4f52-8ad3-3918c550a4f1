import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'CaseStatus', schema: 'Dictionary' })
export class CaseStatus {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 100 })
  public name: string;

  @Column({ name: 'HistoryPersonalResultID', type: 'smallint' })
  public historyPersonalResultId: number;
}
