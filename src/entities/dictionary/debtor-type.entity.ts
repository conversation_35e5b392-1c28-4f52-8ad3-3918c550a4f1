import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'DebtorType', schema: 'Dictionary' })
export class DebtorType {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 30 })
  public name: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'UsedForValidation', type: 'smallint', default: 1 })
  public usedForValidation: number;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'IsCompany', type: 'boolean', default: false })
  public isCompany: boolean;

  @Column({ name: 'AdditionalInfo', type: 'jsonb' })
  public additionalInfo: string;
}
