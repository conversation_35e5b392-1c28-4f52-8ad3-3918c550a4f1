import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'ServiceParameter', schema: 'Dictionary' })
export class ServiceParameter {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', nullable: true })
  public name: string;

  @Column({ name: 'ServiceID', type: 'smallint', nullable: true })
  public serviceId: number;

  @Column({ name: 'Value', type: 'jsonb', nullable: true })
  public value: Record<string, any>;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0, nullable: true })
  public isDeleted: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
