import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'PartyType', schema: 'Dictionary' })
export class PartyType {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'Name', type: 'varchar', length: 100 })
  public name: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @Column({ name: 'Availability', type: 'jsonb' })
  public availability: Record<string, any>;

  @Column({ name: 'Priority', type: 'smallint' })
  public priority: number;

  @Column({ name: 'AdditionalInfo', type: 'jsonb' })
  public additionalInfo: Record<string, any>;

  @Column({ name: 'AllowableRoleID', type: 'jsonb' })
  public allowedRoleId: Record<string, any>;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'SetPhoneTypeID', type: 'smallint', default: 4 })
  public setPhoneTypeId: number;

  @Column({ name: 'PhoneResultPriority', type: 'smallint' })
  public phoneResultPriority: number;
}
