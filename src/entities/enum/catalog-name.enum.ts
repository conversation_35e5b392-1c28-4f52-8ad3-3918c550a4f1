export enum CatalogNameEnum {
  MaskedPhone = 'masked-phone',
  ContactPerson = 'contact-person',
  LetterPrintHouseData = 'letter-print-house-data',
  CaseAttachment = 'case-attachment',
  Interaction = 'interaction',
  CaseCloning = 'case-cloning',
  ExtraInfo = 'extra-info',
  LegalCase = 'legal-case',
  ContractData = 'contract-data',
  LegalDocument = 'legal-document',
  CourtParameter = 'court-parameter',
  UpdateLegalDocument = 'update-legal-document',
  LegalCourt = 'legal-court',
  LegalAppellateCourt = 'legal-appellate-court',
  BasisFina = 'basis-fina',
  BasisCpiio = 'basis-cpiio',
  BasisEmployer = 'basis-employer',
  BasisComplaint = 'basis-complaint',
  TransferToCollectionAgency = 'transfer-to-collection-agency',
  ActivityParameter = 'activity-parameter',
  Discount = 'discount',
  Tag = 'tag',
  CaseParameter = 'case-parameter',
  InvoiceUpdate = 'invoice-update',
  Attachment = 'attachment',
  Phone = 'phone',
  DeleteLegalCase = 'delete-legal-case',
  DeleteInteraction = 'delete-interaction',
  CaseState = 'case-state',
  SmsActivity = 'sms-activity',
  CaseDiscount = 'case-discount',
}
