import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom004EEnforcementComplaint', schema: 'Legal' })
export class Custom004EEnforcementComplaint {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public CaseID: number;

  @Column({ name: 'BasisID', type: 'integer' })
  public BasisID: number;

  @Column({ name: 'ComplaintDate', type: 'date', nullable: true })
  public ComplaintDate: string;

  @Column({ name: 'SentForDocumentationDate', type: 'date', nullable: true })
  public SentForDocumentationDate: string;

  @Column({ name: 'SuspensionDate', type: 'date', nullable: true })
  public SuspensionDate: string;

  @Column({ name: 'FirstInstanceDecisionDate', type: 'date', nullable: true })
  public FirstInstanceDecisionDate: string;

  @Column({ name: 'SecondInstanceDecisionDate', type: 'date', nullable: true })
  public SecondInstanceDecisionDate: string;

  @Column({ name: 'FinalJudgmentDate', type: 'date', nullable: true })
  public FinalJudgmentDate: string;

  @Column({
    name: 'ReceivedDocumentationOptionID',
    type: 'smallint',
    nullable: true,
  })
  public ReceivedDocumentationOptionID: number;

  @Column({
    name: 'DocumentationSentToLawyerOptionID',
    type: 'smallint',
    nullable: true,
  })
  public DocumentationSentToLawyerOptionID: number;

  @Column({
    name: 'ExpiresOptionID',
    type: 'smallint',
    nullable: true,
  })
  public ExpiresOptionID: number;

  @Column({
    name: 'ForeignerOptionID',
    type: 'smallint',
    nullable: true,
  })
  public ForeignerOptionID: number;

  @Column({
    name: 'ComplaintOptionID',
    type: 'smallint',
    nullable: true,
  })
  public ComplaintOptionID: number;

  @Column({
    name: 'ResultOptionID',
    type: 'smallint',
    nullable: true,
  })
  public ResultOptionID: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public IsDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Inserted: string;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Updated: string;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public UpdatedUserID: number;
}
