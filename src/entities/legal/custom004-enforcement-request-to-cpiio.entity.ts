import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom004EEnforcementRequestToCPIIO', schema: 'Legal' })
export class Custom004EEnforcementRequestToCpiio {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public CaseID: number;

  @Column({ name: 'BasisID', type: 'integer' })
  public BasisID: number;

  @Column({ name: 'DeliveryDate', type: 'date', nullable: true })
  public DeliveryDate: string;

  @Column({ name: 'DeliveryStatusID', type: 'smallint', nullable: true })
  public DeliveryStatusID: number;

  @Column({ name: 'IsProcessed', type: 'boolean', nullable: true })
  public IsProcessed: boolean;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public IsDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Inserted: string;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Updated: string;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public UpdatedUserID: number;
}
