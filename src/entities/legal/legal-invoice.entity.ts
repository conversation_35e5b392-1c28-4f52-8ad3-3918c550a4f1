import {
  Column,
  <PERSON><PERSON>ty,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { Case } from '../data/case.entity';
import { Invoice } from '../data/invoice.entity';

@Entity({ name: 'Invoice', schema: 'LegalUA' })
export class LegalInvoice {
  @PrimaryColumn({ name: 'ID', type: 'bigint' })
  public id: string;

  @Column({ name: 'InvoiceID', type: 'bigint' })
  public invoiceId: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: number;

  @Column({ name: 'InsertedUserID', type: 'bigint' })
  public insertedUserId: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'IsDeleted', type: 'smallint', default: () => '0' })
  public isDeleted: number;

  @Column({ name: 'UpdatedUserID', type: 'integer' })
  public updatedUserId: number;

  @ManyToOne(() => Case, (model) => model.invoices)
  @JoinColumn({ name: 'CaseID', referencedColumnName: 'id' })
  public case: Case;

  @OneToOne(() => Invoice)
  @JoinColumn({ name: 'InvoiceID', referencedColumnName: 'id' })
  invoice: Invoice;
}
