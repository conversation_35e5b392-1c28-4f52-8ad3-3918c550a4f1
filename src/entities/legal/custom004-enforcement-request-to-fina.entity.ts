import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom004EEnforcementRequestToFina', schema: 'Legal' })
export class Custom004EEnforcementRequestToFina {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public CaseID: number;

  @Column({ name: 'BasisID', type: 'integer' })
  public BasisID: number;

  @Column({ name: 'DeliveryDate', type: 'date', nullable: true })
  public DeliveryDate: string;

  @Column({ name: 'DeliveryStatusID', type: 'smallint', nullable: true })
  public DeliveryStatusID: number;

  @Column({ name: 'IsReturnedByFina', type: 'boolean', nullable: true })
  public IsReturnedByFina: boolean;

  @Column({
    name: 'ReturningReason',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  public ReturningReason: string;

  @Column({ name: 'IsIncludedInFinaPriority', type: 'boolean', nullable: true })
  public IsIncludedInFinaPriority: boolean;

  @Column({ name: 'IsProcessedByFina', type: 'boolean', nullable: true })
  public IsProcessedByFina: boolean;

  @Column({ name: 'DateOfDisposal', type: 'date', nullable: true })
  public DateOfDisposal: string;

  @Column({ name: 'DateOfElimination', type: 'date', nullable: true })
  public DateOfElimination: string;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public IsDeleted: number;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Inserted: string;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Updated: string;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public UpdatedUserID: number;
}
