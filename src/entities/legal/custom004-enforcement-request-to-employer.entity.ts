import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'Custom004EEnforcementRequestToEmployer', schema: 'Legal' })
export class Custom004EEnforcementRequestToEmployer {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public ID: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public CaseID: number;

  @Column({ name: 'BasisID', type: 'integer' })
  public BasisID: number;

  @Column({ name: 'DeliveryDate', type: 'date', nullable: true })
  public DeliveryDate: string;

  @Column({ name: 'DeliveryStatusID', type: 'smallint', nullable: true })
  public DeliveryStatusID: number;

  @Column({ name: 'IsProcessed', type: 'boolean', nullable: true })
  public IsProcessed: boolean;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public IsDeleted: number;

  @Column({
    name: 'RequestToOriginalDocumentation',
    type: 'date',
    nullable: true,
  })
  public RequestToOriginalDocumentation: string;

  @Column({
    name: 'WeDontHaveOriginalDocumentation',
    type: 'date',
    nullable: true,
  })
  public WeDontHaveOriginalDocumentation: string;

  @Column({
    name: 'WaitingOriginalDocumentation',
    type: 'date',
    nullable: true,
  })
  public WaitingOriginalDocumentation: string;

  @Column({ name: 'SentOriginalDocumentation', type: 'date', nullable: true })
  public SentOriginalDocumentation: string;

  @Column({
    name: 'ReturnedOriginalDocumentation',
    type: 'date',
    nullable: true,
  })
  public ReturnedOriginalDocumentation: string;

  @Column({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Inserted: string;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public InsertedUserID: number;

  @Column({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  public Updated: string;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public UpdatedUserID: number;
}
