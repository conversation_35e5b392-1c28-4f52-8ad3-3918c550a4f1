/* eslint-disable unicorn/prevent-abbreviations */
import { Column, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'Custom002Phone', schema: 'ExtMaskedData' })
export class ExtCustom002Phone {
  @PrimaryColumn({ name: 'MaskedPhoneID', type: 'bigint' })
  public maskedPhoneId: string;

  @Column({ name: 'PartID', type: 'smallint' })
  public partId: number;

  @Column({ name: 'ToolSourceID', type: 'smallint' })
  public toolSourceId: number;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;
}
