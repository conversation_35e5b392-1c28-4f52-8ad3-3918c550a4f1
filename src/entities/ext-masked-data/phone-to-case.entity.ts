import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'PhoneToCase', schema: 'ExtMaskedData' })
export class PhoneToCase {
  @PrimaryGeneratedColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'MaskedPhoneID', type: 'integer' })
  public maskedPhoneId: number;

  @Column({ name: 'CaseID', type: 'bigint' })
  public caseId: string;

  @Column({ name: 'Deactivated', type: 'smallint', default: 0 })
  public deactivated: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number;
}
