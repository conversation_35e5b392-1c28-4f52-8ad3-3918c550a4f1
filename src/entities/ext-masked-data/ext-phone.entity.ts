/* eslint-disable unicorn/prevent-abbreviations */
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'Phone', schema: 'ExtMaskedData' })
export class ExtPhone {
  @PrimaryColumn({ name: 'ID', type: 'integer' })
  public id: number;

  @Column({ name: 'DebtorID', type: 'bigint' })
  public debtorId: string;

  @Column({ name: 'MaskedNumber', type: 'varchar', length: 15 })
  public maskedNumber: string;

  @Column({ name: 'PhoneID', type: 'bigint', nullable: true })
  public phoneId: number;

  @Column({ name: 'IsDeleted', type: 'smallint', default: 0 })
  public isDeleted: number;

  @CreateDateColumn({
    name: 'Inserted',
    type: 'timestamp',
    default: () => 'now()',
  })
  public inserted: Date;

  @UpdateDateColumn({
    name: 'Updated',
    type: 'timestamp',
    default: () => 'now()',
  })
  public updated: Date;

  @Column({ name: 'InsertedUserID', type: 'integer' })
  public insertedUserId: number;

  @Column({ name: 'UpdatedUserID', type: 'integer', nullable: true })
  public updatedUserId: number;

  @Column({ name: 'DataSourceID', type: 'smallint', default: 18 })
  public dataSourceId: number;

  @Column({ name: 'DeactivatedToDate', type: 'date', nullable: true })
  public deactivatedToDate: Date;

  @Column({ name: 'DeactivatedDate', type: 'timestamp', nullable: true })
  public deactivatedDate: Date;

  @Column({ name: 'DeactivatedUserID', type: 'integer', nullable: true })
  public deactivatedUserId: number;

  @Column({ name: 'IsMobile', type: 'smallint', default: 0 })
  public isMobile: number;

  @Column({ name: 'TypeID', type: 'smallint', default: 9 })
  public typeId: number;

  @Column({ name: 'PartID', type: 'smallint', default: 1 })
  public partId: number | undefined;

  @Column({ name: 'TimeZone', type: 'smallint', nullable: true })
  public timeZone: number;

  @Column({ name: 'Priority', type: 'smallint', nullable: true })
  public priority: number;

  @Column({ name: 'ReactivatedDate', type: 'timestamp', nullable: true })
  public reactivatedDate: Date;

  @Column({ name: 'ReactivatedUserID', type: 'integer', nullable: true })
  public reactivatedUserId: number;

  @Column({ name: 'UpdatedByUser', type: 'timestamp', nullable: true })
  public updatedByUser: Date;

  @Column({ name: 'ContactStatusID', type: 'smallint', nullable: true })
  public contactStatusId: number;

  @Column({ name: 'Note', type: 'varchar', length: 50 })
  public note: string;
}
