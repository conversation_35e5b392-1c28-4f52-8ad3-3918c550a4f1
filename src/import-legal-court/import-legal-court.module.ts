import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiClient } from 'src/common/api.client';
import { RabbitmqModule } from 'src/common/rabbitmq/rabbitmq.module';

import { ValidatorDataAccess } from 'src/import/data-access/validator.data-access';
import { CatalogColumnRepository } from 'src/repositories/import/catalog-column.repository';
import { UploadHistoryRepository } from 'src/repositories/import/upload-history.repository';
import { ImportRedisModule } from '../common/redis/import-redis.module';
import { EventsGateway } from '../events/events.gateway';
import { Custom005CaseAdditionalInfoRepository } from '../repositories/data/custom005-case-additional-info.repository';
import { DocumentParameterRepository } from '../repositories/data/document-parameter.repository';
import { DocumentRepository } from '../repositories/data/document.repository';
import { ParameterRepository } from '../repositories/dictionary/parameter.repository';
import { RegionRepository } from '../repositories/dictionary/region.repository';
import { TransactionRepository } from '../repositories/financial/transaction.repository';
import { CourtTypeRepository } from '../repositories/legal-ua/court-type.repository';
import { CourtRepository } from '../repositories/legal-ua/court.repository';
import { InvoiceRepository } from '../repositories/legal-ua/invoice.repository';
import { ServiceParameterRepository } from '../repositories/service-parameter.repository';
import { ImportLegalCourtController } from './import-legal-court.controller';
import { ImportLegalCourtService } from './import-legal-court.service';

@Module({
  imports: [
    ConfigModule,
    RabbitmqModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        baseURL: config.get<string>('validationService.url'),
        timeout: 5000,
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      CatalogColumnRepository,
      ServiceParameterRepository,
      UploadHistoryRepository,
      Custom005CaseAdditionalInfoRepository,
      TransactionRepository,
      DocumentRepository,
      DocumentParameterRepository,
      InvoiceRepository,
      ParameterRepository,
      CourtRepository,
      CourtTypeRepository,
      RegionRepository,
    ]),
    ImportRedisModule,
  ],
  controllers: [ImportLegalCourtController],
  providers: [
    ImportLegalCourtService,
    ValidatorDataAccess,
    ApiClient,
    EventsGateway,
  ],
})
export class ImportLegalCourtModule {}
