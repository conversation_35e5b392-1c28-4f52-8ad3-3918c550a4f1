import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  HealthCheckError,
  HealthIndicator,
  HealthIndicatorResult,
} from '@nestjs/terminus';
import IORedis from 'ioredis';

@Injectable()
export class RedisIndicator extends HealthIndicator {
  private redisClient: IORedis.Redis;
  constructor(config: ConfigService) {
    super();
    this.redisClient = new IORedis({
      host: config.get('redis.host'),
      port: config.get<number>('redis.port'),
      password: config.get('redis.password'),
    });
  }

  async ping(key = 'redis'): Promise<HealthIndicatorResult> {
    const ping = await this.redisClient.ping();
    const isHealthy = ping === 'PONG';
    const result = this.getStatus(key, isHealthy);

    if (isHealthy) {
      return result;
    }
    throw new HealthCheckError('Redis check failed', result);
  }
}
