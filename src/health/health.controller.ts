import { Controller, Get } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';

import { RedisIndicator } from 'src/health/indicators/redis.health';

@Controller()
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private redisCheck: RedisIndicator,
    private database: TypeOrmHealthIndicator,
  ) {}

  @Get('liveness/node')
  liveness() {
    return {};
  }

  @Get('readiness/db')
  @HealthCheck()
  readiness() {
    return this.health.check([
      () => this.redisCheck.ping(),
      () => this.database.pingCheck('database', { timeout: 4000 }),
    ]);
  }
}
