<html>
<head>
  <script src='https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.4.0/socket.io.js'
          integrity='sha512-Y8KodDCDqst1e8z0EGKiqEQq3T8NszmgW2HvsC6+tlNw7kxYxHTLl5Iw/gqZj/6qhZdBt+jYyOsybgSAiB9OOA=='
          crossorigin='anonymous' referrerpolicy='no-referrer'>
  </script>
  <script>
    // ws://localhost:8087/imports/?EIO=3&transport=websocket
    const socket = io('http://localhost:8087', {
      path: '/ws/imports',
      transports: ['websocket'],
      multiplex: false,
    });

    // ws://procollect.beta.creditexpress.ru/imports/?EIO=3&transport=websocket
    const socket2 = io('http://procollect.beta.creditexpress.ru/', {
      path: '/ws/imports',
      transports: ['websocket'],
      query: {
        token: 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      },
      multiplex: false,
    });

    // ws://procollect.beta.creditexpress.ru/imports/?EIO=3&transport=websocket
    const socket3 = io('http://procollect.beta.creditexpress.ru/', {
      path: '/ws/chat',
      transports: ['websocket'],
      query: {
        token: 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      },
      multiplex: false,
    });

    socket.on('connect', function() {
      console.log('local imports connected');
      socket.emit(
        'import',
        { id: 1, userID: 7762 }
      );
    });

    socket2.on('connect', function() {
      console.log('beta imports connected');
      socket2.emit(
        'import',
        { id: 1, userID: 7762 }
      );
    });

    socket3.on('connect', function() {
      console.log('chat connected');
      socket2.emit(
        'import',
        { id: 1, userID: 7762 }
      );
    });

    socket.on('progress', function(data) {
      console.log('progress: ', data);
    });
  </script>
</head>

<body></body>
</html>