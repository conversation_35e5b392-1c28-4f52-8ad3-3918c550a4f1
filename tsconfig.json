{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strictPropertyInitialization": false}, "watchOptions": {"excludeFiles": ["extracted", "output"]}}