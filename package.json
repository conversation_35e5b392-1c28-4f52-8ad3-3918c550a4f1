{"name": "import-node-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": "^14.0.0", "npm": "^7.0.0"}, "engineStrict": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install"}, "dependencies": {"@nestjs/common": "^7.6.15", "@nestjs/config": "^1.0.1", "@nestjs/core": "^7.6.15", "@nestjs/platform-express": "^7.6.15", "@nestjs/platform-socket.io": "^7.6.15", "@nestjs/schedule": "^3.0.3", "@nestjs/swagger": "^4.8.0", "@nestjs/terminus": "^7.2.0", "@nestjs/typeorm": "^7.1.5", "@nestjs/websockets": "^7.6.15", "@sentry/node": "^6.9.0", "@willsoto/nestjs-prometheus": "^4.0.0", "adm-zip": "^0.5.10", "amqp-connection-manager": "^4.1.3", "amqplib": "^0.8.0", "class-transformer": "^0.4.0", "class-validator": "^0.13.1", "csv-parse": "^4.16.2", "date-fns": "^2.29.3", "exceljs": "^4.3.0", "form-data": "^4.0.0", "ioredis": "^4.25.0", "mime-types": "^2.1.35", "multer": "^1.4.2", "nest-raven": "^7.2.0", "nestjs-pino": "^1.4.0", "node-unrar-js": "^2.0.0", "pg": "^8.6.0", "pino-http": "^5.6.0", "pino-pretty": "^5.1.2", "prom-client": "^13.1.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^6.6.6", "socket.io": "^2.4.1", "source-map-support": "^0.5.19", "swagger-ui-express": "^4.1.6", "typeorm": "^0.2.32", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^7.6.0", "@nestjs/schematics": "^7.3.0", "@nestjs/testing": "^7.6.15", "@types/amqplib": "^0.8.1", "@types/cron": "^2.4.0", "@types/express": "^4.17.11", "@types/ioredis": "^4.26.6", "@types/jest": "^26.0.22", "@types/luxon": "^3.3.2", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.7", "@types/node": "^14.14.36", "@types/pino-http": "^5.4.2", "@types/socket.io": "^2.1.13", "@types/supertest": "^2.0.10", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "eslint": "^7.22.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-radar": "^0.2.1", "eslint-plugin-unicorn": "^34.0.1", "husky": "^7.0.1", "jest": "^26.6.3", "lint-staged": "^11.1.1", "prettier": "^2.3.0", "supertest": "^6.1.3", "ts-jest": "^26.5.4", "ts-loader": "^8.0.18", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.2.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"src/**/*.{js,ts,json}": ["npm run lint"]}, "volta": {"node": "14.21.3", "npm": "7.24.2"}}